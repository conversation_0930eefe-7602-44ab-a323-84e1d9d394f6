package cn.com.duiba.pcg.constant;

import java.util.Arrays;
import java.util.List;

/**
 * ClassName: ItemIdConstant <br/>
 * date: 2017年4月27日 上午11:06:56 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
public class ItemIdConstant {

	// 线上测试专用优惠券ItemId
	private static final List<Long> TEST_COUPON = Arrays.asList(31605L);
	
	/**
	 * @param itemId
	 * @return boolean
	 */
	public static boolean isTestCoupon(Long itemId){
		return TEST_COUPON.contains(itemId);
	}
	
	/**
	 * @return TEST_COUPON
	 */
	public static List<Long> getTestCoupon(){
		return TEST_COUPON;
	}
}
