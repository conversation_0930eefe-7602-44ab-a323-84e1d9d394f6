package cn.com.duiba.pcg.tool;


import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.util.Base64Utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.Security;

/**
 * 密文编码：Base64；明文编码：UTF-8；
 * 填充方式：AES/ECB/PKCS7Padding
 * 类库：使用bcprov-jdk15on类库实现AES加解密
 *
 * Created by 歪大哥😁 on 2019-09-12.
 */
public class AESCS7Util {

    static final String ENCODING = "UTF-8";
    static final String AES_PADDING = "AES/ECB/PKCS7Padding";

    static {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        } else {
            Security.removeProvider("BC");
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    // 解密
    public static String AES256Decrypt(String data, String key) throws Exception {
        try {
            SecretKey k = new SecretKeySpec(key.getBytes(ENCODING), "AES");
            Cipher cipher = Cipher.getInstance(AES_PADDING, "BC");
            cipher.init(Cipher.DECRYPT_MODE, k);
            byte[] result = cipher.doFinal(Base64Utils.decode(data.getBytes(ENCODING)));
            return new String(result, ENCODING);
        } catch (InvalidKeyException ie) {
            throw new Exception(ie.getMessage() + ". 请下载无政策限制版本的jre，并将local_policy.jar、US_export_policy.jar文件覆盖到jre/lib/security下");
        } catch (Exception e) {
            throw e;
        }
    }

    // 加密
    public static String AESEncrypt(String data, String key) throws Exception {
        try {
            SecretKey k = new SecretKeySpec(key.getBytes(ENCODING), "AES");
            Cipher cipher = Cipher.getInstance(AES_PADDING, "BC");
            cipher.init(Cipher.ENCRYPT_MODE, k);
            return new String(Base64Utils.encode(cipher.doFinal(data.getBytes(ENCODING))), ENCODING);
        } catch (InvalidKeyException ie) {
            throw new Exception(ie.getMessage() + ". 请下载无政策限制版本的jre，并将local_policy.jar、US_export_policy.jar文件覆盖到jre/lib/security下");
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(AESEncrypt("test", "JuGi3FCECD1dA2BPL1lCWC=="));
        System.out.println(AES256Decrypt("JP+BniurQVyE0e4vcIvnYw==", "JuGi3FCECD1dA2BPL1lCWC=="));
    }
}
