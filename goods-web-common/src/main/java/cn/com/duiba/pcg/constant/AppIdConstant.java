package cn.com.duiba.pcg.constant;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * ClassName: AppIdConstant <br/>
 * date: 2017年4月12日 下午5:24:41 <br/>
 *
 * @since JDK 1.6
 */
public class AppIdConstant {

    private static final ImmutableSet<Long> YING_YONG_BAO_NEW_APP_IDS = ImmutableSet.of(62781L, 61921L, 1L);

    /**
     * 兑吧优惠券在兑换成功后，兑换成功通知页、领奖成功通知页、兑换记录详情页是否展示马上使用的按钮
     * 赶集:2264L,2064L ||  58同城:4515   ||  PPTV:4688 || 360手机助手:10935
     */
    public static final ImmutableSet<Long> showUseButton = ImmutableSet.of(2264L, 2064L, 4515L, 5183L, 4688L, 2516L,
            4437L, 6839L, 4786L, 9438L, 8786L, 10935L,
            11150L, 7433L, 15160L);

    private static final List<Long> show3SendObject = Arrays.asList(1609L);

    /**
     * 秒杀商品免校验app
     */
    private static final List<Long> secKillSkipApps = Arrays.asList(491L, 3862L);

    /**
     * 转转appid
     */
    public static final ImmutableSet<Long> zhuanzhuanIds = ImmutableSet.of(43910L, 42587L);
    /**
     * 海底捞开发者id
     * 1.后期,海底捞判断统一用RemoteSaasGrantService类
     */
    public static final List<Long> HAIDILAO_DEVELOPER_IDS = Arrays.asList(20040L, 34924L, 35690L, 1L);
    /**
     * 互动作业appid
     */
    public static final ImmutableSet<Long> hdzuoyeIds = ImmutableSet.of(39055L, 40700L, 40701L, 40707L, 40702L);
    /**
     * 水滴筹appid
     */
    public static final ImmutableSet<Long> shuidichouIds=ImmutableSet.of(50070L,51217L);
    /**
     * keep appId
     */
    public static final ImmutableSet<Long> keepIds=ImmutableSet.of(53023L, 51798L);
    /**
     * 哈罗单车开发者
     */
    public static final List<Long> HALUO_DEVELOPER_IDS= Arrays.asList(10865L);

    /**
     * 防作弊白名单
     */
    private static final List<Long> ANTICHEAT_WHITE_APPS = Lists.newArrayList(21631L, 25780L, 25803L, 25492L, 25801L);

    /**
     * 不展示积分的APP
     * 百度贴吧:10379
     */
    public static final ImmutableSet<Long> dontshowCreditsAppIds = ImmutableSet.of(11056L);

    private static final List<Long> WeiboList = Arrays.asList(new Long[]{Long.valueOf(7585L), Long.valueOf(5606L)});

    private static final List<Long> DownloadUrlToTextList = Arrays.asList(4515L, 5183L, 4688L, 4714L, 2516L, 4437L, 6839L, 5965L, 4786L, 9438L, 8786L, 10935L, 11150L, 7433L, 15160L, 16101L, 12091L, 17509L);

    /**
     * 饿了么appId
     */
    private static final List<Long> ELE_APPIDS = Arrays.asList(14695L);

    /**
     * 网赚客户部分老机型适配有问题，返回使用老页面（直冲类详情页）
     * 零花:352、马上赚钱:29,255、口袋ATM:842、试货:166、口袋赚钱:902、口袋ATM（android）:904、无聊赚：1030、IOS_口袋ATM:1507、有钱APP:1204、快来赚钱724
     * 口袋赚：1820、钱鹿锁屏：1553、蛋蛋赚：2210
     */
    private static final List<Long> useOldPage = Arrays.asList(352L, 29L, 255L, 842L, 166L, 902L, 904L, 1031L, 1507L, 1204L, 724L, 915L, 1553L, 1820L, 2210L);

    /**
     * 虚拟商品（绑定AppId和商品id），如果为未绑定QQ号的用户，按钮提示去绑定QQ。
     * 迅雷：4391L
     */
    private static final List<Long> VirtualGoBindQQAppList = Arrays.asList(4391L);
    /**
     * 苏宁
     */
    private static final Set<Long> SUN_NING_APPIDS = ImmutableSet.of(63217L, 64513L);

    public static boolean isSunNingApp(Long appId) {
        return SUN_NING_APPIDS.contains(appId);
    }

    /**
     * 是否展示第三方发货，目前格瓦拉提这个要求
     *
     * @param appId
     * @return boolean
     */
    public static boolean isShow3SendObject(Long appId) {
        return show3SendObject.contains(appId);
    }

    /**
     * @param appId
     * @return boolean
     */
    public static boolean showCredits(Long appId) {
        return !dontshowCreditsAppIds.contains(appId);
    }

    /**
     * 下载链接在展示的时候，个别app中需要展示纯文本
     *
     * @param appId
     * @return boolean
     */
    public static boolean isDownloadUrlShowText(Long appId) {
        return DownloadUrlToTextList.contains(appId);
    }

    /**
     * 判断是秒杀免验证码APPID
     *
     * @param appId
     * @return boolean
     */
    public static boolean isSecKillSkipApp(Long appId) {
        return secKillSkipApps.contains(appId);
    }

    /**
     * @param appId
     * @return boolean
     */
    public static boolean isAnticheatWhiteApp(Long appId) {
        return ANTICHEAT_WHITE_APPS.contains(appId);
    }

    /**
     * @param appId
     * @return boolean
     */
    public static boolean isWeibo(Long appId) {
        return WeiboList.contains(appId);
    }

    /**
     * 网赚客户部分老机型适配有问题，返回使用老页面
     *
     * @param appId
     * @return boolean
     * <AUTHOR> 2015年5月22日 下午2:27:41
     */
    public static boolean useOldPage(Long appId) {
        return useOldPage.contains(appId);
    }

    /**
     * 虚拟商品（绑定商品id），如果为未绑定QQ号的用户，按钮提示去绑定QQ。
     *
     * @param appId
     * @return boolean
     * <AUTHOR> 2015年9月9日 下午3:20:17
     */
    public static boolean isVirtualGoBindQQApp(Long appId) {
        return VirtualGoBindQQAppList.contains(appId);
    }

    /**
     * 判断是否为转转app
     *
     * @param appId
     * @return boolean
     * @throw
     */
    public static boolean isZhuanzhuanApp(Long appId) {
        return zhuanzhuanIds.contains(appId);
    }

    /**
     * 判断是否为互动作业app
     *
     * @param appId
     * @return boolean
     * @throw
     */
    public static boolean isHdZuoyeApp(Long appId) {
        return hdzuoyeIds.contains(appId);
    }
    /**
     *  判断是否为水滴筹app
     *
     * @param appId
     * @return boolean
     * @throw
     */
    public static boolean isShuidichouApp(Long appId){ return shuidichouIds.contains(appId); }

    /**
     * 判断是否为keep app
     * @param appId
     * @return
     */
    public static boolean isKeepApp(Long appId){ return keepIds.contains(appId);}

    /**
     * @param appId
     * @return boolean
     */
    public static boolean isEleApp(Long appId) {
        return ELE_APPIDS.contains(appId);
    }

    /**
     * 是否哈罗单车开发者
     * @param developerId 开发者Id
     * @return boolean
     */
    public static boolean isHaluo(Long developerId){
        return HALUO_DEVELOPER_IDS.contains(developerId);
    }

    public static boolean isYingYongBaoNewAppIds(Long appId) {
        return YING_YONG_BAO_NEW_APP_IDS.contains(appId);
    }
}
