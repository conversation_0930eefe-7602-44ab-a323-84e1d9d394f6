package cn.com.duiba.pcg.tool;


public class Message {
	private boolean success;
	private String message;
	private Object data;
	public Message() {		
	}	
	public Message(boolean success,String message) {
		this.success=success;
		this.message=message;
	}
	public Message(boolean success,String message,Object data) {
		this.success=success;
		this.message=message;
		this.data=data;
	}
	public boolean getSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
	/**
	 * 返回成功消息
	 * @param content 内容
	 * @return 成功消息
	 */
	public static Message success(String content, Object data) {
		return new Message(true, content, data);
	}
	
	/**
	 * 返回成功消息
	 * @param content 内容
	 * @return 成功消息
	 */
	public static Message success(String content) {
		return new Message(true, content);
	}

	/**
	 * 返回成功消息
	 * @param data
	 * @return
     */
	public static Message success(Object data) {
		return new Message(true, "操作成功",data);
	}
	
	/**
	 * 返回成功消息
	 * @return 成功消息
	 */
	public static Message success() {
		return new Message(true, "操作成功");
	}
	
	/**
	 * 返回失败消息
	 * @param content 内容
	 * @return 成功消息
	 */
	public static Message error(String content, Object data) {
		return new Message(false, content, data);
	}

	/**
	 * 返回失败消息
	 * @param content
	 * @return
     */
	public static Message error(String content) {
		return new Message(false, content);
	}

	/**
	 * 返回失败消息
	 * @return
     */
	public static Message error() {
		return new Message(false, "操作失败");
	}
	/**
	 * 返回失败消息
	 * @param data 内容
	 * @return 成功消息
	 */
	public static Message error( Object data) {
		return new Message(false, "失败", data);
	}

}
