package cn.com.duiba.pcg.service.deploy.controller.supply;

import cn.com.duiba.biz.tool.duiba.reqresult.Result;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedMessage;
import cn.com.duiba.paycenter.dto.payment.notify.cooupon.WxCouponUsedNotifyDetail;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.service.biz.config.ccb.SszCcbConfig;
import cn.com.duiba.pcg.service.biz.response.supply.SupplyPurchaseResponse;
import cn.com.duiba.pcg.service.biz.service.ccb.SszCcbService;
import cn.com.duiba.pcg.service.biz.util.SszCcbDESCryptoUtil;
import cn.com.duiba.pcg.service.biz.vo.ccb.ssz.CcbPurchaseOrderResponse;
import cn.com.duiba.pcg.service.biz.vo.ccb.ssz.CcbPurchaseRequest;
import cn.com.duiba.pcg.service.biz.vo.ccb.ssz.CcbPurchaseResponse;
import cn.com.duiba.pcg.tool.cgb.MD5;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * @author: pengyi
 * @description:
 * @date: 2023/3/15 下午1:43
 */
@RestController
@RequestMapping("/gaw/ssz/ccb/supply")
public class SszCcbSupplyController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SszCcbSupplyController.class);

    @Autowired
    private SszCcbConfig sszCcbConfig;
    @Autowired
    private SszCcbService sszCcbService;
    @Autowired
    private SupplyController supplyController;
    @Autowired
    private RemoteDuiBaSupplyOrderService remoteDuiBaSupplyOrderService;


    /**
     * 山西建总行采购
     * @param param
     * @param sign
     * @param httpServletRequest
     * @return
     */
    @PostMapping("/purchase")
    public CcbPurchaseResponse purchase(@RequestParam("param") String param, @RequestParam("sign") String sign, HttpServletRequest httpServletRequest) {
        LOGGER.info("山西建行请求参数，param={}，sign={}", param, sign);
        if (!sszCcbService.checkSign(param,sign)) {
            // 验签失败
            return new CcbPurchaseResponse(ErrorCode.E1100003);
        }
        String requestStr = SszCcbDESCryptoUtil.decrypt(param, sszCcbConfig.getDesKey(), sszCcbConfig.getDesVector());
        if (StringUtils.isBlank(requestStr)) {
            // 参数解密失败
            return new CcbPurchaseResponse(ErrorCode.E1002301);
        }
        try {
            LOGGER.info("山西建行-微信立减金采购请求，request={}", requestStr);
            Result<SupplyPurchaseResponse> purchase = sszCcbService.sszCcbPurchase(requestStr);
            LOGGER.info("山西建行采购，result={}", JSON.toJSONString(purchase));
            if (purchase.getSuccess()) {
                SupplyPurchaseResponse response = JSON.parseObject(JSON.toJSONString(purchase.getData()), SupplyPurchaseResponse.class);
                String msg = "下单成功，兑吧订单号：" + response.getOrderNum();
                return new CcbPurchaseResponse(1000, msg);
            } else {
                return new CcbPurchaseResponse(Integer.valueOf(purchase.getCode()), purchase.getDesc());
            }
        } catch (Exception e) {
            LOGGER.warn("山西建行采购异常，request={}", requestStr, e);
            return new CcbPurchaseResponse(ErrorCode.E9999999);
        }
    }

    /**
     * 订单查询
     * @param param
     * @param sign
     * @return
     */
    @PostMapping("/queryOrder")
    public CcbPurchaseOrderResponse queryOrder(@RequestParam("param") String param, @RequestParam("sign") String sign) {
        LOGGER.info("山西建行请求参数，param={}，sign={}", param, sign);
        if (!sszCcbService.checkSign(param,sign)) {
            // 验签失败
            return new CcbPurchaseOrderResponse(ErrorCode.E1100003);
        }
        String requestStr = SszCcbDESCryptoUtil.decrypt(param, sszCcbConfig.getDesKey(), sszCcbConfig.getDesVector());
        if (StringUtils.isBlank(requestStr)) {
            // 参数解密失败
            return new CcbPurchaseOrderResponse(ErrorCode.E1002301);
        }
        try {
            String orderNo = JSON.parseObject(requestStr).getString("order_no");
            DuiBaSupplyOrdersDto supplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(orderNo, sszCcbConfig.getAppId());
            if (supplyOrdersDto == null) {
                return new CcbPurchaseOrderResponse(1001, "无订单数据");
            }
            CcbPurchaseOrderResponse response = new CcbPurchaseOrderResponse(1000, "查询成功");
            CcbPurchaseOrderResponse.Data data = new CcbPurchaseOrderResponse.Data();
            data.setOrder_no(orderNo);
            if (Objects.equals(DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode(), supplyOrdersDto.getOrderStatus())) {
                data.setCoupon_status(0);
                sszCcbService.queryWxCouponStatus(supplyOrdersDto, data);
            } else if  (Objects.equals(DuiBaSupplyOrdersStatusEnum.FAIL.getCode(), supplyOrdersDto.getOrderStatus())) {
                data.setCoupon_status(-2);
            } else {
                data.setCoupon_status(-1);
            }
            response.setData(data);
            LOGGER.info("山西建行-微信立减金订单查询，response={}", JSON.toJSONString(response));
            return response;
        } catch (Exception e) {
            LOGGER.warn("山西建行采购异常，request={}", requestStr, e);
            return new CcbPurchaseOrderResponse(ErrorCode.E9999999);
        }
    }

    @GetMapping("/test")
    public void test(String thirdOrderNum) {
        DuiBaSupplyOrdersDto supplyOrdersDto = new DuiBaSupplyOrdersDto();
        supplyOrdersDto.setThirdOrderNum(thirdOrderNum);
        WxCouponUsedMessage message = new WxCouponUsedMessage();
        WxCouponUsedNotifyDetail detail = new WxCouponUsedNotifyDetail();
        detail.setStatus("USED");
        message.setWxCouponUsedNotifyDetail(detail);
        sszCcbService.wxCouponUseFlowNotify(supplyOrdersDto, message.getWxCouponUsedNotifyDetail().getStatus(), 0, true);
    }

//    @GetMapping("/test1")
    public void test1(String appItemId,String skuId, String uid, String thirdOrderNo,HttpServletRequest httpServletRequest) {
        try {
            CcbPurchaseRequest request = new CcbPurchaseRequest();
            request.setBatchCd(appItemId);
            request.setCouponCd(skuId);
            request.setOpenid(uid);
            request.setOrderNo(thirdOrderNo);
            String param = SszCcbDESCryptoUtil.encrypt(JSON.toJSONString(request), sszCcbConfig.getDesKey(), sszCcbConfig.getDesVector());
            String sign = MD5.getStringMD5(param + sszCcbConfig.getMd5Salt());
            CcbPurchaseResponse purchase = purchase(param, sign, httpServletRequest);
            LOGGER.info("test purchase={}", JSON.toJSONString(purchase));
        } catch (Exception e) {
            LOGGER.warn("test purchase error", e);
        }
    }

//    @GetMapping("/test2")
    public void test2(String orderNo) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("order_no", orderNo);
            String param = SszCcbDESCryptoUtil.encrypt(JSON.toJSONString(jsonObject), sszCcbConfig.getDesKey(), sszCcbConfig.getDesVector());
            String sign = MD5.getStringMD5(param + sszCcbConfig.getMd5Salt());
            CcbPurchaseOrderResponse ccbPurchaseOrderResponse = queryOrder(param, sign);
            LOGGER.info("test purchase={}", JSON.toJSONString(ccbPurchaseOrderResponse));
        } catch (Exception e) {
            LOGGER.warn("test purchase error", e);
        }
    }
}
