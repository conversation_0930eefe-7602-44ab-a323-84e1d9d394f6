package cn.com.duiba.pcg.service.deploy.controller;

import cn.com.duiba.activity.center.api.dto.ActivityCategoryDto;
import cn.com.duiba.activity.center.api.dto.CategoryActivityDto;
import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.activity.center.api.dto.hdtool.DuibaHdtoolDto;
import cn.com.duiba.activity.center.api.dto.manual.AppManualLotteryDto;
import cn.com.duiba.activity.center.api.dto.ngame.DuibaNgameDto;
import cn.com.duiba.activity.center.api.dto.seckill.DuibaSeckillDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.AppSingleLotteryDto;
import cn.com.duiba.activity.center.api.dto.singlelottery.DuibaSingleLotteryDto;
import cn.com.duiba.activity.center.api.remoteservice.RemoteActivityCategoryService;
import cn.com.duiba.activity.center.api.remoteservice.activity.RemoteOperatingActivityServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.hdtool.RemoteDuibaHdtoolServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.manual.RemoteAppManualLotteryServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.ngame.RemoteDuibaNgameService;
import cn.com.duiba.activity.center.api.remoteservice.seckill.RemoteDuibaSeckillServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteDuibaSingleLotteryServiceNew;
import cn.com.duiba.activity.center.api.remoteservice.singlelottery.RemoteSingleLotteryServiceNew;
import cn.com.duiba.api.bo.KeyValueEntity;
import cn.com.duiba.api.enums.PageBizTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.cros.CrossDomainUtils;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerFootprintDto;
import cn.com.duiba.dcommons.enums.AppItemSourceTypeEnum;
import cn.com.duiba.developer.center.api.domain.dto.AppBannerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.domain.dto.appextra.DuibaShareDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppBannerServiceNew;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppExtraService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.saas.RemoteSaasGrantService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteAddrLimitService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteAppItemClassifyService;
import cn.com.duiba.goods.center.api.remoteservice.dto.AppItemClassifyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.AppItemClassifyRelationDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsBackendService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemNewExtraService;
import cn.com.duiba.pcg.constant.AppIdConstant;
import cn.com.duiba.pcg.constant.CookieDefine;
import cn.com.duiba.pcg.constant.CustomAppIdConstant;
import cn.com.duiba.pcg.service.biz.enums.DpmTypeEnum;
import cn.com.duiba.pcg.service.biz.service.AppItemClassifyService;
import cn.com.duiba.pcg.service.biz.service.AppItemService;
import cn.com.duiba.pcg.service.biz.service.ConsumerFootprintService;
import cn.com.duiba.pcg.service.biz.service.ConsumerItemStatusService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.HaidilaoService;
import cn.com.duiba.pcg.service.biz.service.HomeService;
import cn.com.duiba.pcg.service.biz.service.ItemViewService;
import cn.com.duiba.pcg.service.biz.service.QQmusicService;
import cn.com.duiba.pcg.service.biz.service.YingyongbaoService;
import cn.com.duiba.pcg.service.biz.util.CrossDomainHelper;
import cn.com.duiba.pcg.service.biz.util.DcmAndDpmUtil;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.util.ShareLandUrlUtils;
import cn.com.duiba.pcg.service.biz.vo.ItemKeyVO;
import cn.com.duiba.pcg.service.biz.vo.ItemOpenVO;
import cn.com.duiba.pcg.tool.Result;
import cn.com.duiba.pcg.tool.ResultUtil;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duiba.wolf.utils.SwitchUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duibaboot.ext.autoconfigure.accesslog.AccessLogFilter;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by yansen on 16/12/21.
 */
@Controller
@RequestMapping("/button")
@Api(tags = "Button")
public class ButtonController {

    private static final int COMMON_PAGE_SIZE = 20;

    /**
     * 开发者自有加钱购商品，用户是否可用钱包余额抵扣属性
     */
    private static final String SUPPORT_BALANCE_DEDUCT = "support_balance_deduct";

    /**
     * 开发者自有加钱购商品，支持用户钱包余额抵扣情况下的，角标默认文案
     */
    private static final String SUPPORT_BALANCE_DEDUCT_TAGTEXT = "支持钱包抵扣";

    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    RemoteAppBannerServiceNew remoteAppBannerServiceNew;
    @Autowired
    private ItemViewService itemViewService;
    @Autowired
    private RemoteAppExtraService remoteAppExtraServiceNew;
    @Autowired
    private GoodsItemDataService goodsItemDataService;
    @Autowired
    private HomeService homeService;
    @Autowired
    private AppItemService appItemService;
    @Autowired
    private RemoteAppItemClassifyService remoteAppItemClassifyService;
    @Autowired
    private AppItemClassifyService appItemClassifyService;
    @Autowired
    private RemoteActivityCategoryService remoteActivityCategoryService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private ConsumerItemStatusService consumerItemStatusService;
    @Autowired
    private ConsumerFootprintService consumerFootprintService;
    @Autowired
    private RemoteDuibaSingleLotteryServiceNew remoteDuibaSingleLotteryServiceNew;
    @Autowired
    private RemoteSingleLotteryServiceNew remoteSingleLotteryServiceNew;
    @Autowired
    private RemoteDuibaHdtoolServiceNew remoteDuibaHdtoolServiceNew;
    @Autowired
    private ItemViewService dhomeModuleService;
    @Autowired
    private RemoteDuibaNgameService remoteDuibaNgameService;
    @Autowired
    private RemoteDuibaSeckillServiceNew remoteDuibaSeckillServiceNew;
    @Autowired
    private RemoteOperatingActivityServiceNew remoteOperatingActivityServiceNew;
    @Autowired
    private RemoteAppItemGoodsBackendService remoteAppItemGoodsBackendService;
    @Autowired
    private RemoteAppManualLotteryServiceNew remoteAppManualLotteryServiceNew;
    @Autowired
    private YingyongbaoService yingyongbaoService;
    @Autowired
    private RemoteItemNewExtraService remoteItemNewExtraService;
    @Autowired
    private RemoteAddrLimitService remoteAddrLimitService;
    @Autowired
    private RemoteSaasGrantService remoteSaasGrantService;
    @Autowired
    private HaidilaoService haidilaoService;
    @Autowired
    private CustomAppIdConstant customAppIdConstant;
    @Autowired
    private QQmusicService qqmusicService;

    private Map<String, String> classifyImageMap = Maps.newHashMap();

    private static final Cache<Long, List<Map<String, Object>>> openKeyCache = CacheBuilder.newBuilder().expireAfterWrite(5, TimeUnit.SECONDS).maximumSize(1000).build();

    private static Logger log = LoggerFactory.getLogger(ButtonController.class);


    {
        classifyImageMap.put("classify", "/upload/ly/zdydhx.jpg");
        classifyImageMap.put("all", "/upload/ly/sydhx.jpg");
        classifyImageMap.put("cost", "/upload/ly/zcl.jpg");
        classifyImageMap.put("coupon", "/upload/ly/yhq.jpg");
        classifyImageMap.put("object", "/upload/ly/sw.jpg");
        classifyImageMap.put("lottery", "/upload/ly/cjhd.jpg");
    }

    /**
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/classify")
    @ApiOperation(value = "自定义列表", notes = "自定义图标跳转链接", httpMethod = "GET")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "id", value = "自定义分类ID", dataType = "String", required = true, paramType = "query")

    })
    public ModelAndView classify(HttpServletRequest request) {
        String classifyId = request.getParameter("id");
        if (StringUtils.isBlank(classifyId)) {
            return new ModelAndView("error", "error", "id为空");
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        Long id = Long.valueOf(classifyId);
        AppItemClassifyDto appItemClassifyDto = remoteAppItemClassifyService.find(id).getResult();
        if (!Objects.equal(appItemClassifyDto.getAppId(), consumer.getAppId())) {
            return new ModelAndView("error", "error", "无权访问");
        }

        String classifyImage = appItemClassifyDto.getClassifyImage();
        if (StringUtils.isBlank(classifyImage)) {
            DomainConfigDto domainConfig = domainService.getSystemDomain(app.getId());
            classifyImage = domainConfig.getCdnDomain() + classifyImageMap.get("classify");
        }

        ModelAndView model = new ModelAndView("consumer/mobile/itemlist");
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        model.addObject("isMobike", isMoBike);
        model.addObject("consumer", consumer);
        model.addObject("showCredits", AppIdConstant.showCredits(app.getId()));
        model.addObject("app", app);
        model.addObject("arn", false);
        model.addObject("type", "classify");
        model.addObject("classify", appItemClassifyDto);
        model.addObject("classifyImage", classifyImage);
        model.addObject("classifyImageSwitch", appItemClassifyDto.getClassifyImageSwitch());
        return model;
    }

    /**
     * @param request
     * @param response
     * @return Result
     */
    @RequestMapping("/getClassify")
    @ApiOperation(value = "自定义兑换项列表", notes = "自定义兑换项列表", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "自定义分类ID", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appItemPage", value = "当前页", dataType = "String", paramType = "query")})
    @ResponseBody
    public Result<JSONObject> getClassify(HttpServletRequest request, HttpServletResponse response) {
        String domain = request.getHeader("Origin");
        if (domain != null && domain.contains(CrossDomainHelper.getCrossDomain())) {
            // 设置允许跨域
            DomainCrossUtils.crossDomain(request, response, domain);
        }
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
        String classifyId = request.getParameter("id");
        if (StringUtils.isBlank(classifyId)||ObjectUtils.equals("undefined",classifyId)) {
            return ResultUtil.fail("id不能为空");
        }
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        Long id = Long.valueOf(classifyId);
        AppItemClassifyDto appItemClassifyDto = remoteAppItemClassifyService.find(id).getResult();
        if (appItemClassifyDto == null || !Objects.equal(appItemClassifyDto.getAppId(), consumer.getAppId())) {
            return ResultUtil.fail("无权访问");
        }

        int appItemPage = 1;
        if (StringUtils.isNotBlank(request.getParameter("appItemPage"))) {
            appItemPage = Integer.parseInt(request.getParameter("appItemPage"));
        }
        Integer start = (appItemPage - 1) * COMMON_PAGE_SIZE;
        String ip = RequestTool.getIpAddr(request);
        List<ItemKeyDto> itemKeyList = appItemClassifyService.findByClassifyLimit(app, consumer, id, start,
                COMMON_PAGE_SIZE, ip);
        //查询出关联的活动
        List<Long> oaIds = new ArrayList<>();
        Map<Long, AppItemClassifyRelationDto> activityClassifyRelationDtoMap = new HashMap<>();
        List<AppItemClassifyRelationDto> appItemClassifyRelationDtos = remoteAppItemClassifyService.getByClassifyId(id);
        selectActivity(appItemClassifyRelationDtos, oaIds, activityClassifyRelationDtoMap);
        Set<OperatingActivityDto> imgOpDtos = new HashSet<>();
        Map<Long, Map<String, Object>> detailVOMap = new HashMap<>();
        Map<Long, Map<String, Object>> activityMap = new HashMap<>();

        //先从缓存取
        List<Map<String, Object>> items = getItems(domainConfigDto, consumer, app, id, start, itemKeyList, oaIds, activityClassifyRelationDtoMap, imgOpDtos, detailVOMap, activityMap);
        /** 应用宝 针对自己用户群体的商品推荐列表会提供给兑吧，他们在商城首页，这一部分数据需要置顶依次排列展示；*/
        if (cn.com.duiba.developer.center.api.utils.AppIdConstant.isYingyongbaos(app.getId())) {
            items = yingyongbaoService.customizeItemList(RequestLocal.getPartnerUserId(), RequestTool.getCookie(request, CookieDefine.Transfer), items);
        }
        // QQ音乐
        if (customAppIdConstant.getQqmusicAppIds().contains(app.getId())){
            items = qqmusicService.customizeItemList(app, consumer, items, id);
        }
        boolean appItemNextPage = true;
        int size = items.size();
        if (start + COMMON_PAGE_SIZE <= size) {
            items = items.subList(start, start + COMMON_PAGE_SIZE);
        } else if (start < size && start + COMMON_PAGE_SIZE > size) {
            items = items.subList(start, size);
        } else {
            items = Lists.newArrayList();
        }
        if (items.size() < COMMON_PAGE_SIZE) {
            appItemNextPage = false;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appItemNextPage", appItemNextPage);
        jsonObject.put("autoRecommendNextPage", false);
        jsonObject.put("arunum", items.size());
        jsonObject.put("items", items);
        return ResultUtil.successWithData(jsonObject);
    }

    private List<Map<String, Object>> getItems(final DomainConfigDto domainConfigDto, final ConsumerDto consumer, final AppSimpleDto app, final Long id, final Integer start, final List<ItemKeyDto> itemKeyList, final List<Long> oaIds, final Map<Long, AppItemClassifyRelationDto> activityClassifyRelationDtoMap, final Set<OperatingActivityDto> imgOpDtos, final Map<Long, Map<String, Object>> detailVOMap, final Map<Long, Map<String, Object>> activityMap) {
        try {
            return openKeyCache.get(id, new Callable<List<Map<String, Object>>>() {
                @Override
                public List<Map<String, Object>> call() throws Exception {
                    if (!oaIds.isEmpty()) {
                        //查询出开启的活动
                        List<OperatingActivityDto> operatingActivityDtos = remoteOperatingActivityServiceNew.findAllByIdsAndStatus(oaIds, 2);
                        for (OperatingActivityDto operatingActivityDto : operatingActivityDtos) {
                            String url = ItemKeyUtil.getActivityUrl(operatingActivityDto.getType(), operatingActivityDto.getId(), domainConfigDto);
                            Map<String, Object> item = new HashMap<>();
                            item.put("operatingActivityId", operatingActivityDto.getId());
                            isSeckill(operatingActivityDto.getType(), item);
                            item.put("title", operatingActivityDto.getTitle());
                            item.put("url", url);
                            item.put("payload", activityClassifyRelationDtoMap.get(operatingActivityDto.getId()).getPayload());
                            item.put("credits", getCredits(operatingActivityDto, app) + app.getUnitName());
                            item.put("dcm", getActivityDcm(operatingActivityDto.getId()));
                            if (!StringUtils.isEmpty(operatingActivityDto.getSmallImage())) {
                                item.put("logo", operatingActivityDto.getSmallImage());
                            } else {
                                imgOpDtos.add(operatingActivityDto);
                                detailVOMap.put(operatingActivityDto.getId(), item);
                            }
                            activityMap.put(operatingActivityDto.getId(), item);
                        }
                    }
                    List<Map<String, Object>> oldItems = formatItemListType(app, itemKeyList, consumer);
                    addItem(activityMap, imgOpDtos, detailVOMap, oldItems);
                    setEmdInfo(oldItems, DpmTypeEnum.getCodeByType("classify"), start, app.getId(), domainConfigDto);
                    if (remoteSaasGrantService.belongToHaiDiLaoDeveloper(app.getDeveloperId())) {
                        //海底捞定制,需要塞入商品销售字段
                        //setSaleNum其实可以放在formatItemListType里面,但是formatItemListType有两个地方调用到,
                        //另外一个接口不做海底捞定制,所以选择放在外面
                        oldItems = setSaleNum(oldItems);
                        //海底捞使用saleNum(销量）排序
                        sort(oldItems,"saleNum");
                    } else {
                        //原有逻辑根据payLoad排序
                        sort(oldItems,"payload");
                    }
                    return oldItems;
                }
            });
        } catch (Exception e) {
            log.error("openKeyCache", e);
        }
        return Collections.EMPTY_LIST;
    }

    private List<Map<String, Object>> setSaleNum(List<Map<String, Object>> oldItems) {
        if (CollectionUtils.isEmpty(oldItems)) {
            return oldItems;
        }

        for (Map<String, Object> each : oldItems) {
            //和同事确认过，海底捞的商品区必定有appItemId
            Object appItemId = each.get("appItemId");
            //默认值
            each.put("saleNum", 0);
            if (appItemId == null) {
                continue;
            }
            Integer value = haidilaoService.getSaleNumWithCache(Long.valueOf(appItemId.toString())).intValue();
            //排序的时候是Integer 所以这里用intValue
            each.put("saleNum", value);
        }
        return oldItems;
    }


    public void addItem(Map<Long, Map<String, Object>> activityMap, Set<OperatingActivityDto> imgOpDtos, Map<Long, Map<String, Object>> detailVOMap, List<Map<String, Object>> items) {
        if (!MapUtils.isEmpty(activityMap)) {
            selectPicture(imgOpDtos, detailVOMap);
            Collection<Map<String, Object>> values = activityMap.values();
            //把活动放入到item
            for (Map<String, Object> value : values) {
                //防止前端报错
                value.put("hasDegree", false);
                value.put("isOpTypeCredits", false);
                value.put("appItemId", null);
                value.put("facePrice", null);
                value.put("remainStock", null);
                value.put("subTitle", "");
                value.put("tagClasses", "");
                value.put("tagColor", null);
                value.put("tagText", "");
                value.put("type", "");
                items.add(value);
            }
        }
    }

    public void isSeckill(Integer type, Map<String, Object> item) {
        if (type == OperatingActivityDto.TypeSecondsKill || type == OperatingActivityDto.TypeDuibaSeckill) {
            item.put("recommendText", "兑换");
        } else {
            item.put("recommendText", "抽奖");
        }
    }

    public void selectActivity(List<AppItemClassifyRelationDto> appItemClassifyRelationDtos, List<Long> oaIds, Map<Long, AppItemClassifyRelationDto> activityClassifyRelationDtoMap) {
        if (!CollectionUtils.isEmpty(appItemClassifyRelationDtos)) {
            for (AppItemClassifyRelationDto appItemClassifyRelationDto : appItemClassifyRelationDtos) {
                if (appItemClassifyRelationDto.getOperatingActivityId() != null) {
                    oaIds.add(appItemClassifyRelationDto.getOperatingActivityId());
                    activityClassifyRelationDtoMap.put(appItemClassifyRelationDto.getOperatingActivityId(), appItemClassifyRelationDto);
                }
            }
        }
    }

    /**
     * 根据字段field值倒叙排序
     * @param items
     * @param field 排序的字段
     */
    private void sort(List<Map<String, Object>> items,String field) {
        if (CollectionUtils.isNotEmpty(items)) {
            // 根据自定义权重进行排序
            Collections.sort(items, new Comparator<Map>() {

                public int compare(Map one, Map two) {
                    if ((Integer) one.get(field) < (Integer) two.get(field)) {
                        return 1;
                    } else if ((Integer) one.get(field) > (Integer) two.get(field)) {
                        return -1;
                    } else {
                        return 0;
                    }
                }
            });
        }
    }

    //查询活动图片
    public void selectPicture(Set<OperatingActivityDto> imgOpDtos, Map<Long, Map<String, Object>> detailVOMap) {
        if (CollectionUtils.isEmpty(imgOpDtos)) {
            return;
        }
        int hdtoolType = -1;
        LinkedListMultimap<Integer, Long> multimap = LinkedListMultimap.create();
        putActivityType(imgOpDtos, hdtoolType, multimap);
        Map<String, String> imgMap = new HashMap<>();
        Map<String, String> imgNewMap = new HashMap<>();
        //sonar
        containsKey(multimap, imgMap, imgNewMap);
        containsKill(multimap, imgMap, imgNewMap);
        if (multimap.containsKey(hdtoolType)) {
            List<DuibaHdtoolDto> hdtoolDOImage = remoteDuibaHdtoolServiceNew.findAllByIds(multimap.get(hdtoolType));
            for (DuibaHdtoolDto h : hdtoolDOImage) {
                String key = hdtoolType + "_" + h.getId();
                //新的imageSmall
                imgNewMap.put(key, h.getSmallImgNew());
                imgMap.put(key, dhomeModuleService.getThumbnailImage(h.getSmallImage()));
            }
        }
        if (multimap.containsKey(OperatingActivityDto.TypeDuibaNgame)) {
            List<DuibaNgameDto> ngameDtos = remoteDuibaNgameService.findAllByIds(multimap.get(OperatingActivityDto.TypeDuibaNgame));
            for (DuibaNgameDto ngameDto : ngameDtos) {
                String key = OperatingActivityDto.TypeDuibaNgame + "_" + ngameDto.getId();
                imgMap.put(key, ngameDto.getSmallImage());
                //新的imageSmall
                imgNewMap.put(key, ngameDto.getSmallImgNew());
            }
        }
        if (multimap.containsKey(OperatingActivityDto.TypeDuibaSeckill)) {
            List<DuibaSeckillDto> seckillDtos = remoteDuibaSeckillServiceNew.findAllByIds(multimap.get(OperatingActivityDto.TypeDuibaSeckill));
            for (DuibaSeckillDto seckillDto : seckillDtos) {
                String key = OperatingActivityDto.TypeDuibaSeckill + "_" + seckillDto.getId();
                imgMap.put(key, seckillDto.getWhiteImage());
                //新的imageSmall
                imgNewMap.put(key, seckillDto.getSmallImgNew());
            }
        }

        putPicture(imgOpDtos, detailVOMap, hdtoolType, imgMap, imgNewMap);

    }

    public void containsKill(LinkedListMultimap<Integer, Long> multimap, Map<String, String> imgMap, Map<String, String> imgNewMap) {
        if (multimap.containsKey(OperatingActivityDto.TypeSecondsKill)) {
            List<AppItemDto> appItemDtoList = remoteAppItemGoodsBackendService.findAllByIds(multimap.get(OperatingActivityDto.TypeSecondsKill)).getResult();
            for (AppItemDto appItemDto : appItemDtoList) {
                String key = OperatingActivityDto.TypeSecondsKill + "_" + appItemDto.getId();
                imgMap.put(key, appItemDto.getWhiteImage());
                //新的imageSmall
                imgNewMap.put(key, appItemDto.getSmallImgNew());
            }
        }
    }

    public void containsKey(LinkedListMultimap<Integer, Long> multimap, Map<String, String> imgMap, Map<String, String> imgNewMap) {
        if (multimap.containsKey(OperatingActivityDto.TypeDuibaSingleLottery)) {
            List<DuibaSingleLotteryDto> duibasingle = remoteDuibaSingleLotteryServiceNew.findAllByIds(multimap.get(OperatingActivityDto.TypeDuibaSingleLottery));
            for (DuibaSingleLotteryDto s : duibasingle) {
                String key = OperatingActivityDto.TypeDuibaSingleLottery + "_" + s.getId();
                imgMap.put(key, dhomeModuleService.getThumbnailImage(s.getSmallImage()));
                //新的imageSmall
                imgNewMap.put(key, s.getSmallImgNew());
            }
        }
        if (multimap.containsKey(OperatingActivityDto.TypeAppSingleLottery)) {
            List<AppSingleLotteryDto> singles = remoteSingleLotteryServiceNew.findAllByIds(multimap.get(OperatingActivityDto.TypeAppSingleLottery));
            for (AppSingleLotteryDto s : singles) {
                String key = OperatingActivityDto.TypeAppSingleLottery + "_" + s.getId();
                imgMap.put(key, s.getSmallImage());
                //新的imageSmall
                imgNewMap.put(key, s.getSmallImgNew());
            }
        }
        if (multimap.containsKey(OperatingActivityDto.TypeAppManualLottery)) {
            List<AppManualLotteryDto> appManualLotteryDtos = remoteAppManualLotteryServiceNew.findAllByIds(multimap.get(OperatingActivityDto.TypeAppManualLottery));
            for (AppManualLotteryDto appManualLotteryDto : appManualLotteryDtos) {
                String key = OperatingActivityDto.TypeAppManualLottery + "_" + appManualLotteryDto.getId();
                imgMap.put(key, appManualLotteryDto.getSmallImage());
                //新的imageSmall
                imgNewMap.put(key, appManualLotteryDto.getSmallImgNew());
            }
        }
    }

    public void putActivityType(Set<OperatingActivityDto> imgOpDtos, int hdtoolType, LinkedListMultimap<Integer, Long> multimap) {
        for (OperatingActivityDto activity : imgOpDtos) {
            if (OperatingActivityDto.hdToolTypeSet.contains(activity.getType())) {
                multimap.put(hdtoolType, activity.getActivityId());// 用-1代表活动工具
            } else if (activity.getType() == OperatingActivityDto.TypeSecondsKill) {
                multimap.put(activity.getType(), activity.getAppItemId());
            } else {
                multimap.put(activity.getType(), activity.getActivityId());
            }
        }
    }

    public void putPicture(Set<OperatingActivityDto> imgOpDtos, Map<Long, Map<String, Object>> detailVOMap, int hdtoolType, Map<String, String> imgMap, Map<String, String> imgNewMap) {
        for (OperatingActivityDto activity : imgOpDtos) {
            Map<String, Object> item = detailVOMap.get(activity.getId());
            String key;
            if (OperatingActivityDto.hdToolTypeSet.contains(activity.getType())) {
                key = hdtoolType + "_" + activity.getActivityId();
            } else if (activity.getType() == OperatingActivityDto.TypeSecondsKill) {
                key = activity.getType() + "_" + activity.getAppItemId();
            } else {
                key = activity.getType() + "_" + activity.getActivityId();
            }
            String img = imgMap.get(key);
            if (item != null && img != null) {
                item.put("logo", img);
            } else {
                //如果没有图片,显示默认图片
                item.put("logo", "//yun.duiba.com.cn/developer/img/default_img.jpg");
            }
        }
    }

    private String getActivityDcm(Long id) {
        return 202 + "." + id + ".0" + ".0";
    }

    /**
     * 活动积分展示
     */
    private String getCredits(OperatingActivityDto operatingActivityDto, AppSimpleDto app) {
        //活动积分默认取兑吧配置积分，如果开发者自定义积分大于0，则取开发者自定义积分
        Long credits = operatingActivityDto.getCredits();
        if(null!=operatingActivityDto.getCustomCredits() && operatingActivityDto.getCustomCredits() > 0){
            credits = operatingActivityDto.getCustomCredits();
        }
        Long creditsLong = credits;
        if (operatingActivityDto.getActivityId() != null
                && (OperatingActivityDto.TypeAppSingleLottery != operatingActivityDto.getType()) && (OperatingActivityDto.TypeAppManualLottery != operatingActivityDto.getType())) {
            creditsLong = (long) Math.ceil(credits * app.getCreditsRate() / 100.0);
        }
        if (SwitchUtils.switchIsOpen(app.getAppSwitch(), AppSimpleDto.SwitchCreditsDecimalPoint)) {
            return String.valueOf(creditsLong / (double) 100);
        }
        return String.valueOf(creditsLong);

    }

    /**
     * @param request
     * @param response
     * @return ModelAndView
     */
    @RequestMapping("/activityCategory")
    @ApiOperation(value = "活动抽奖页面", notes = "活动抽奖页面", httpMethod = "GET")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "id", value = "子页面id", dataType = "String", required = true, paramType = "query")

    })
    public ModelAndView activityCategory(HttpServletRequest request, HttpServletResponse response) {
        ModelAndView model = new ModelAndView("consumer/mobile/activityCategory");
        Long id = Long.valueOf(request.getParameter("id"));
        ActivityCategoryDto dto = remoteActivityCategoryService.findById(id).getResult();
        if (dto == null) {
            model = new ModelAndView("/listEmpty");
            return model;
        }
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
        model.addObject("pageid", 1);
        model.addObject("title", dto.getName());
        model.addObject("id", id);
        model.addObject("html", dto.getContent());
        model.addObject("plugin_domain", domainConfigDto.getActivityDomain());
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        model.addObject("consumerId", consumer.getId());
        model.addObject("plugin_url", "activityCategory/" + id);

        addAccessLogEx(id, consumer);
        return model;
    }

    private void addAccessLogEx(Long id, ConsumerDto consumer) {
        AccessLogFilter.putExPair("id", id);
        AccessLogFilter.putExPair("suc", 1);
        AccessLogFilter.putExPair("loginStatus", consumer.isNotLoginUser() ? 2 : 1);
        AccessLogFilter.putExPair("pageBizId", PageBizTypeEnum.SUBPAGE_ICON_INDEX.getBizPageId());
    }

    /**
     * @param request
     * @return Result
     */
    @RequestMapping("/getcategory")
    @ApiOperation(value = "活动分类", notes = "根据活动分类ID查询活动", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "子页面id", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "pageid", value = "当前页", dataType = "String", required = true, paramType = "query")

    })
    @ResponseBody
    public JSONObject getcategory(HttpServletRequest request) {
        String id = request.getParameter("id");
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isBlank(id)) {
            jsonObject.put("success", false);
            jsonObject.put("errorMsg", "id不能为空");
            return jsonObject;
        }
        long categoryId = Long.parseLong(id);
        int appItemPage = StringUtils.isEmpty(request.getParameter("pageid")) ? 1 : Integer.valueOf(request.getParameter("pageid"));
        int start = (appItemPage - 1) * COMMON_PAGE_SIZE;
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        List<CategoryActivityDto> list = remoteActivityCategoryService.findAllActivitiesByAppCategoryNew(app.getId(),
                categoryId,
                consumer.getId(),
                RequestTool.getIpAddr(request),
                RequestTool.getOSNew(request),
                domainConfigDto.getEmbedDomain(),
                start,
                COMMON_PAGE_SIZE).getResult();
        boolean appItemNextPage = true;
        if (CollectionUtils.isEmpty(list) || list.size() < COMMON_PAGE_SIZE) {
            appItemNextPage = false;
        }
        String unitName;
        if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
            unitName = "金额";
        } else {
            unitName = app.getUnitName();
        }
        DuibaShareDto appExtra = remoteAppExtraServiceNew.findDuibaShareByAppId(app.getId()).getResult();
        // 活动支持分享
        for (int i = 0; i < list.size(); i++) {
            CategoryActivityDto ca = list.get(i);
            String link = ca.getLink();
            if (!link.startsWith("//") && link.startsWith("/")) {
                link = domainConfigDto.getActivityDomain() + link;
            }
            ca.setLink(ShareLandUrlUtils.getShareLandUrl(appExtra, link, ca.getOperatingId(), domainConfigDto));
        }
        jsonObject.put("unitName", unitName);
        jsonObject.put("appItemNextPage", appItemNextPage);
        jsonObject.put("data", list);
        jsonObject.put("pageid", appItemPage);
        jsonObject.put("credits", consumer.getCredits());// 用户积分
        jsonObject.put("success", true);
        return jsonObject;
    }

    /**
     * @param request
     * @param params
     * @return itemlist
     */
    @RequestMapping("/items")
    @ApiOperation(value = "兑换项列表", notes = "跳转进入兑换项列表的地址", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "type", value = "type", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "iconIdStr", value = "iconIdStr", dataType = "String", paramType = "query")

    })
    public ModelAndView items(HttpServletRequest request, Map<String, Object> params) {
        ModelAndView modelAndView = new ModelAndView("consumer/mobile/itemlist");
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        modelAndView.addObject("isMobike", isMoBike);
        modelAndView.addObject("showCredits", AppIdConstant.showCredits(app.getId()));
        modelAndView.addObject("app", app);
        modelAndView.addObject("type", request.getParameter("type"));
        modelAndView.addObject("iconIdStr",
                StringUtils.isEmpty(request.getParameter("iconIdStr")) ? "" : request.getParameter("iconIdStr"));
        return modelAndView;
    }

    /**
     * @param request
     * @return 兑换项列表
     */
    @RequestMapping("/getitems")
    @ApiOperation(value = "兑换项列表", notes = "异步加载兑换项列表数据的地址", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "type", value = "type", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "iconIdStr", value = "iconIdStr", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "autoRecommendNextPage", value = "autoRecommendNextPage", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "appItemNextPage", value = "appItemNextPage", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "appItemPage", value = "appItemPage", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "autoRecommendPage", value = "autoRecommendPage", dataType = "String", paramType = "query")})
    @ResponseBody
    public Result<JSONObject> ajaxItems(HttpServletRequest request, HttpServletResponse response) {
        CrossDomainUtils.crossDomain(request, response);
        boolean autoRecommendNextPage = Boolean.valueOf(request.getParameter("autoRecommendNextPage"));
        boolean appItemNextPage = Boolean.valueOf(request.getParameter("appItemNextPage"));
        int appItemPage = NumberUtils.parseInt(request.getParameter("appItemPage"), 1);
        int autoRecommendPage = NumberUtils.parseInt(request.getParameter("autoRecommendPage"), 1);

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = remoteAppServiceNew.getSimpleApp(consumer.getAppId()).getResult();
        String type = request.getParameter("type");
        String iconIdStr = request.getParameter("iconId");
        List<ItemKeyDto> itemKey = Lists.newArrayList();
        int start = 0;
        boolean isFristPage = isFirstPage(autoRecommendNextPage, appItemNextPage, autoRecommendPage, appItemPage);
        // 如果是第一页或者是还有下一页,继续查找数据
        if (appItemNextPage || isFristPage) {
            start = (appItemPage - 1) * COMMON_PAGE_SIZE;
            itemKey = goodsItemDataService.findByType(app, consumer, type, start, COMMON_PAGE_SIZE,
                    RequestTool.getIpAddr(request));
            if (itemKey == null) {
                itemKey = Lists.newArrayList();
            }
            appItemNextPage = itemKey.size() >= COMMON_PAGE_SIZE;
        }
        if (itemKey.size() < COMMON_PAGE_SIZE) {
            autoRecommendNextPage = true;
        }
        int autoStart = NumberUtils.parseInt(request.getParameter("arunum"), 0);
        //商品重构下线自动推荐
/*        Long autoRecommendSize = 0l;
        // 数量不足时需要补充自动推荐商品
        if (autoRecommendNextPage) {
            int autoRecommendPageSize = COMMON_PAGE_SIZE - itemKey.size();
            Integer findNum = 0;
            if (autoRecommendPageSize > 0) {
                PaginationVO<ItemKeyDto> autoRecommendPages = homeService.findAutoRecommendByLimit(app, type,
                        autoStart,
                        autoRecommendPageSize);
                itemKey.addAll(autoRecommendPages.getRows());
                findNum = autoRecommendPages.getRows().size();
                autoStart = autoStart + findNum;
            }
            if ((findNum >= COMMON_PAGE_SIZE || autoRecommendSize - autoStart > 0)
                    && autoRecommendSize >= COMMON_PAGE_SIZE) {
                autoRecommendNextPage = true;
            } else {
                autoRecommendNextPage = false;
            }
        }*/

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("autoRecommendPage", autoRecommendPage);
        jsonObject.put("appItemNextPage", appItemNextPage);
        jsonObject.put("autoRecommendNextPage", autoRecommendNextPage);
        jsonObject.put("arunum", autoStart);

        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());

        jsonObject = setFirstPageInfo(iconIdStr, jsonObject, type, domainConfigDto.getCdnDomain());

        //校验地域限制 兑换
        List<ItemKeyDto> list4AddLimit = remoteAddrLimitService.getItemCanPassAddrLimit(itemKey, RequestTool.getIpAddr(RequestLocal.getRequest())).getResult();
        // 组装商品数据
        List<Map<String, Object>> items = formatItemListType(app, list4AddLimit, consumer);
        setEmdInfo(items, DpmTypeEnum.getCodeByType(StringUtils.isNotEmpty(type) ? type : "all"), start, app.getId(), domainConfigDto);
        jsonObject.put("items", items);
        return ResultUtil.successWithData(jsonObject);
    }


    private void setEmdInfo(List<Map<String, Object>> items, int dpmType, int start, long appId, DomainConfigDto domainConfigDto) {
        int i = start + 1;
        for (Map<String, Object> mdto : items) {
            String dpm = appId + "." + dpmType + "." + i + ".1";
            if (DcmAndDpmUtil.isCustomDpm()) {
                dpm = DcmAndDpmUtil.getCustomDPM(appId, i);
            }
            String dcm = (String) mdto.get("dcm");
            JSONObject newEmdMap = new JSONObject();

            newEmdMap.put("dpm", dpm);
            if (dcm != null) {
                newEmdMap.put("dcm", dcm);
            }
            if (dcm == null) {
                dcm = "";
            }
            newEmdMap.put("domain", domainConfigDto.getEmbedDomain());
            mdto.put("emdDpmJson", newEmdMap.toJSONString());

            String url = (String) mdto.get("url");
            if (StringUtils.isNotEmpty(url) && dpmType > 0) {
                int index = url.indexOf('?');
                if (index > 0) {
                    url = url + "&";
                } else {
                    url = url + "?";
                }
                url = url + "dpm=" + dpm + "&dcm=" + dcm;
                mdto.put("url", url);
            }
            i++;
        }
    }

    private JSONObject setFirstPageInfo(String iconIdStr, JSONObject jsonObject, String type, String cdnDomain) {
        String classifyImage = "";
        Boolean classifyImageSwitch = false;
        if (StringUtils.isNotBlank(iconIdStr)) {
            AppBannerDto icon = remoteAppBannerServiceNew.find(Long.valueOf(iconIdStr)).getResult();
            classifyImage = icon.getClassifyImage();
            classifyImageSwitch = icon.getClassifyImageSwitch();
            if (StringUtils.isBlank(classifyImage)) {
                classifyImage = cdnDomain + classifyImageMap.get(type);
            }
        }
        jsonObject.put("classifyImage", classifyImage);
        jsonObject.put("classifyImageSwitch", classifyImageSwitch);
        return jsonObject;
    }

    //
    private List<Map<String, Object>> formatItemListType(AppSimpleDto app, List<ItemKeyDto> itemKey,
                                                         ConsumerDto consumer) {
        List<ItemKeyVO> itemKeyVOs = new ArrayList<>();
        for (ItemKeyDto key : itemKey) {
            itemKeyVOs.add(new ItemKeyVO(key));
        }
        homeService.setStatus(app, itemKeyVOs, false);
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());

        //填充预分配库存和定向库存
        consumerItemStatusService.fillSpecifyAndPreStock(itemKeyVOs, app.getId());

        ConsumerFootprintDto footprintDO = consumerFootprintService.findByConsumerId(consumer.getId());

        List<ItemOpenVO> openVOs = new ArrayList<>();
        for (ItemKeyVO it : itemKeyVOs) {
            ItemOpenVO openVo = new ItemOpenVO(it, null, domainConfigDto);
            setUseStatus(it, footprintDO);
            setTagForUse(it, openVo);
            openVOs.add(openVo);

        }

        List<ItemKeyVO> newItemKeyVOs = new ArrayList<>();
        for (ItemKeyVO v : itemKeyVOs) {
            ItemKeyVO nv = new ItemKeyVO(v.getItemKey());
            nv.setUseStatus(v.getUseStatus());
            nv.setPayload(v.getPayload());
            nv.setMarkStatus(v.getMarkStatus());
            newItemKeyVOs.add(nv);
        }

        // formatItemListTypeInner方法牵扯太多,暂不迁移
        List<Map<String, Object>> ais = itemViewService.formatItemListTypeInner(itemKey,
                                                                                app,
                                                                                openVOs, domainConfigDto);
        DuibaShareDto appExtra = remoteAppExtraServiceNew.findDuibaShareByAppId(app.getId()).getResult();
        // 组装活动分享的url

        /** 当商品为自有加钱购类型时需重定义角标 */
        rebuildTagText(itemKey, ais);

        for (int i = 0; i < ais.size(); i++) {
            Map<String, Object> map = ais.get(i);
            AppItemDto appItem = itemKey.get(i).getAppItem();
            if (null != map.get("url") && null != appItem && null != appItem.getSourceType()
                    && AppItemSourceTypeEnum.SourceTypeNormal.getCode() != appItem.getSourceType()) {
                String linkStr = map.get("url").toString();
                String shareLandUrl;
                if (linkStr.indexOf("&dbtrack-data=") >= 0) {
                    String[] strs = linkStr.split("&dbtrack-data=");
                    strs[0] = ShareLandUrlUtils.getShareLandUrl(appExtra, strs[0], appItem.getSourceRelationId(), domainConfigDto);
                    shareLandUrl = strs[0] + "&dbtrack-data=" + strs[1];
                } else {
                    shareLandUrl = ShareLandUrlUtils.getShareLandUrl(appExtra, linkStr,
                            appItem.getSourceRelationId(), domainConfigDto);
                }
                map.put("url", shareLandUrl);
            }
        }
        return ais;
    }

    /**
     * 针对开发者自有加钱购商品，如果存在配置角标，则显示配置角标
     * 否则根据该商品是否支持用户钱包余额抵扣来设置
     */
    private List<Map<String, Object>> rebuildTagText(List<ItemKeyDto> itemKey, List<Map<String, Object>> ais) {
        /**
         * 1.过滤商品：得到没有配置商品角标的自有加钱购商品
         * 2.取出appItemId;
         */
        List<Long> appItemIdList = itemKey.stream()
                .filter(item ->
                        item.isSelfAppItemMode()
                                && item.isAmbItemKey()
                                && StringUtils.isBlank(item.getAppItem().getCustomTag())
                )
                .map(itemKeyDto -> itemKeyDto.getAppItem().getId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(appItemIdList)) {
            return ais;
        }
        /**
         * 1.根据appItemIdList，和属性key,value，在商品属性扩展表查询出符合条件的属性列表
         * 2.取出属性列表中的appItemId;
         */
        List<KeyValueEntity> keyValueEntity = remoteItemNewExtraService.findAppItemEntriesByItemAndPropApi(appItemIdList, SUPPORT_BALANCE_DEDUCT, "1");
        List<Long> matchIdList = keyValueEntity.stream()
                .map(keyValue -> keyValue.getBizId())
                .collect(Collectors.toList());


        /** 根据查询出的appItemIdList，过滤原map列表,得到最终待处理列表*/
        ais.forEach(map -> {
            if (matchIdList.contains(map.get("appItemId"))) {
                map.put("tagText", SUPPORT_BALANCE_DEDUCT_TAGTEXT);
                map.put("tagColor", "red");
            }
        });

        return ais;
    }

    private void setTagForUse(ItemKeyVO it, ItemOpenVO openVo) {
        StringBuilder sb = new StringBuilder(openVo.getTagClasses());
        String tagText = openVo.getTagText();
        String tagColor = openVo.getTagColor();
        String recommendText = openVo.getRecommendText();

        if (it.getUseStatus() == ItemKeyVO.STATUS_USED) {
            //前端样式 不同的class之间用空格隔开
            sb.append(" hasexchanged");
            tagText = "已兑";
            tagColor = "#aaa";
            recommendText = "已兑";
        } else if (it.getUseStatus() == ItemKeyVO.STATUS_NOREMAING) {
            sb.append(" outofstock");
            tagText = "兑完";
            tagColor = "#aaa";
            recommendText = "兑完";
        }

        openVo.setRecommendText(recommendText);
        openVo.setTagClasses(sb.toString().trim());
        openVo.setTagText(tagText);
        openVo.setTagColor(tagColor);
    }

    private void setUseStatus(ItemKeyVO itemKeyVO, ConsumerFootprintDto footprintDO) {
        ItemKeyDto itemKey = itemKeyVO.getItemKey();
        if (consumerItemStatusService.noMark(itemKey)) {
            //不需要设置使用标记
        } else if (!consumerItemStatusService.isRemaining(itemKeyVO)) {
            //兑完 （库存不足）
            itemKeyVO.setUseStatus(ItemKeyVO.STATUS_NOREMAING);
        } else if (!consumerItemStatusService.isForeverUsed(footprintDO, itemKey)) {
            //已兑 （永久已兑）
            itemKeyVO.setUseStatus(ItemKeyVO.STATUS_USED);
        }
    }


    private boolean isFirstPage(Boolean autoRecommendNextPage, boolean appItemNextPage, int appItemPage,
                                int autoRecommendPage) {
        if (autoRecommendNextPage || appItemNextPage) {
            return false;
        }
        if (appItemPage > 1 || autoRecommendPage > 1) {
            return false;
        }
        return true;
    }

}
