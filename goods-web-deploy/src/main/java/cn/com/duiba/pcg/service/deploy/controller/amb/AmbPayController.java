package cn.com.duiba.pcg.service.deploy.controller.amb;

import cn.com.duiba.activity.center.api.domain.dto.singleAward.SingleAwardRecordDto;
import cn.com.duiba.activity.center.api.enums.centscan.RedirectPageEnum;
import cn.com.duiba.activity.center.api.remoteservice.singleAward.RemoteSingleAwardRecordService;
import cn.com.duiba.activity.common.center.api.remoteservice.wallet.RemoteWalletAccountService;
import cn.com.duiba.activity.custom.center.api.dto.icbc.IcbcUserCardDto;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnOrderTypeEnum;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnShengQianDeductionDto;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnShengQianOrderDto;
import cn.com.duiba.activity.custom.center.api.enums.ccb.SupplierEntityEnum;
import cn.com.duiba.activity.custom.center.api.remoteservice.icbc.RemoteIcbcUserCardService;
import cn.com.duiba.activity.custom.center.api.remoteservice.pinganshengqian.RemotePingAnOrderService;
import cn.com.duiba.activity.custom.center.api.remoteservice.pinganshengqian.RemotePingAnSqActRecordService;
import cn.com.duiba.activity.custom.center.api.remoteservice.pinganshengqian.RemotePingAnSqService;
import cn.com.duiba.api.bo.KeyValueDto;
import cn.com.duiba.api.bo.KeyValueEntity;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.api.tools.MoneyUtil;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.dto.ReceiveAddressDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppNewExtraService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.saas.RemoteSaasGrantService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemNewExtraService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.goods.center.api.remoteservice.tool.ExtraInfoUtils;
import cn.com.duiba.live.supplier.center.api.constant.DuibaLiveOrderSupplierConstant;
import cn.com.duiba.live.supplier.center.api.remoteservice.RemoteDuibaLiveSupplierBftService;
import cn.com.duiba.order.center.api.constant.SubPaychannelType;
import cn.com.duiba.order.center.api.constant.VisitChannelEnum;
import cn.com.duiba.order.center.api.dto.AmbOrderFastDto;
import cn.com.duiba.order.center.api.dto.AmbPaychannelOrdersDto;
import cn.com.duiba.order.center.api.dto.AmbSubOrdersDto;
import cn.com.duiba.order.center.api.dto.OrderAssetDeductDto;
import cn.com.duiba.order.center.api.dto.OrderItemDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteAmbOrderFastService;
import cn.com.duiba.order.center.api.remoteservice.RemoteAmbPaychannelOrdersService;
import cn.com.duiba.order.center.api.remoteservice.RemoteAmbSubOrdersService;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrderAssetDeductService;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrderItemService;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrdersTextChangeService;
import cn.com.duiba.paycenter.constant.DuibaLiveConstant;
import cn.com.duiba.paycenter.dto.payment.charge.BaseChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.ChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeResponseDto;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeResponseDTO;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteXibPayNotifyService;
import cn.com.duiba.pcg.constant.AppIdConstant;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.CcbConfig;
import cn.com.duiba.pcg.service.biz.config.CentscanConfig;
import cn.com.duiba.pcg.service.biz.config.CiticBankNanningConfig;
import cn.com.duiba.pcg.service.biz.config.CiticBankTaiYuanConfig;
import cn.com.duiba.pcg.service.biz.config.CustomPayDomainConfig;
import cn.com.duiba.pcg.service.biz.config.IcbcCreditsConfig;
import cn.com.duiba.pcg.service.biz.config.JsBankConfig;
import cn.com.duiba.pcg.service.biz.config.NingboBankPayConfig;
import cn.com.duiba.pcg.service.biz.config.PingAnShengQianMiniConfig;
import cn.com.duiba.pcg.service.biz.config.UnionPayConfig;
import cn.com.duiba.pcg.service.biz.config.WandaConfig;
import cn.com.duiba.pcg.service.biz.config.WxPayConfig;
import cn.com.duiba.pcg.service.biz.config.amb.InstallmentSubPayChannelConfig;
import cn.com.duiba.pcg.service.biz.constants.RedisKeyFactory;
import cn.com.duiba.pcg.service.biz.constants.WilteListConstants;
import cn.com.duiba.pcg.service.biz.customize.lipstick.LipstickBo;
import cn.com.duiba.pcg.service.biz.enums.OrderUriConstant;
import cn.com.duiba.pcg.service.biz.manager.InnerPageManager;
import cn.com.duiba.pcg.service.biz.service.AddressService;
import cn.com.duiba.pcg.service.biz.service.ConsumerItemRenderService;
import cn.com.duiba.pcg.service.biz.service.ThirdPartyCashierDeskService;
import cn.com.duiba.pcg.service.biz.service.custom.SuZhouBankPayService;
import cn.com.duiba.pcg.service.biz.service.impl.PlatFormCouponServiceImpl;
import cn.com.duiba.pcg.service.biz.service.order.OrderItemService;
import cn.com.duiba.pcg.service.biz.service.payment.PaymentService;
import cn.com.duiba.pcg.service.biz.service.pingan.PingAnService;
import cn.com.duiba.pcg.service.biz.util.AlipayUtil;
import cn.com.duiba.pcg.service.biz.util.AppCreditsUtils;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.util.RequestTool;
import cn.com.duiba.pcg.service.biz.util.unionpay.ZjUnionPayRequestTool;
import cn.com.duiba.pcg.service.biz.vo.AppVO;
import cn.com.duiba.pcg.service.biz.vo.CItemVO;
import cn.com.duiba.pcg.service.biz.vo.ItemsInfoVO;
import cn.com.duiba.pcg.service.biz.vo.abc.PayUrlVo;
import cn.com.duiba.pcg.service.biz.vo.payment.BankOfSuZhouResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.CmbOneNetPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.DuibaLiveInstallmentPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.DuibaLiveMpPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.HellopayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.ShouxinpayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.UnionPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.WxPayLiteResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.WxPayMpResponseVO;
import cn.com.duiba.pcg.service.common.DateUtil;
import cn.com.duiba.pcg.tool.Message;
import cn.com.duiba.pcg.tool.duibalive.LiveUtils;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duibabiz.component.domain.DomainService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 实物加钱购支付
 *
 * <AUTHOR>
 * @date: 2017年4月11日 下午4:55:14 <br/>
 */
@Controller
@RequestMapping("/ambPay")
@Api(tags = {"加钱购支付"})
public class AmbPayController {
    private static final Logger LOG = LoggerFactory.getLogger(AmbPayController.class);

    private static final String WALLET_DEDUCT = "wallet_deduct";
    private static final String ALI_PAY = "ali_pay";

    private static final String VIEW_REDIRECT = "redirect:";

    /**
     * 是否允许用户钱包抵扣
     * 1：是
     * 0：否
     */
    private static final String SUPPORT_BALANCE_DEDUCT = "support_balance_deduct";

    private static final String AMB_PAY_FAIL = "consumer/mobile/amb_pay_fail";

    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;
    @Autowired
    private RemoteAmbSubOrdersService remoteAmbSubOrdersService;
    @Autowired
    private RemoteAmbPaychannelOrdersService remoteAmbPaychannelOrdersService;
    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    private ConsumerItemRenderService consumerItemRenderService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private ThirdPartyCashierDeskService thirdPartyCashierDeskService;
    @Autowired
    private RemoteAmbOrderFastService remoteAmbOrderFastService;
    @Autowired
    private RemoteOrdersTextChangeService remoteOrdersTextChangeService;


    @Autowired
    private RemoteAppItemSkuService remoteAppItemSkuService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private RemoteSaasGrantService remoteSaasGrantService;
    @Autowired
    private RemoteItemNewExtraService remoteItemNewExtraService;
    @Autowired
    private RemoteWalletAccountService remoteWalletAccountService;
    @Autowired
    private RemoteOrderAssetDeductService remoteOrderAssetDeductService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private AddressService addressService;
    @Autowired
    private SuZhouBankPayService suZhouBankPayService;
    @Autowired
    private CcbConfig ccbConfig;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    @Autowired
    private InnerPageManager innerPageManager;
    @Autowired
    private CentscanConfig centscanConfig;
    @Autowired
    private LipstickBo lipstickBo;
    @Autowired
    private WxPayConfig wxPayConfig;
    @Autowired
    private CiticBankNanningConfig citicBankNanningConfig;
    @Autowired
    private UnionPayConfig unionPayConfig;
    @Autowired
    private RemoteOrderItemService remoteOrderItemService;

    @Autowired
    private RemoteDuibaLiveSupplierBftService remoteDuibaLiveSupplierBftService;

    @Resource
    private RemoteIcbcUserCardService remoteIcbcUserCardService;

    @Autowired
    private CustomPayDomainConfig customPayDomainConfig;


    @Resource
    private RemoteAppNewExtraService remoteAppNewExtraService;


    @Resource
    private NingboBankPayConfig ningboBankPayConfig;

    @Resource
    private CiticBankTaiYuanConfig citicBankTaiYuanConfig;


    @Resource
    private IcbcCreditsConfig icbcCreditsConfig;
    @Autowired
    private WandaConfig wandaConfig;

    @Resource
    private InstallmentSubPayChannelConfig installmentSubPayChannelConfig;

    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private JsBankConfig jsBankConfig;
    @Resource
    private RemoteXibPayNotifyService remoteXibPayNotifyService;
    @Autowired
    private RemoteSingleAwardRecordService remoteSingleAwardRecordService;

    @Resource
    private RemotePingAnSqService remotePingAnSqService;


    @Resource
    private RemotePingAnOrderService remotePingAnOrderService;

    @Resource
    private RemoteAppItemGoodsService remoteAppItemGoodsService;

    @Resource
    private RemotePingAnSqActRecordService remotePingAnSqActRecordService;

    @Resource(name = "stringRedisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Resource
    private PingAnService pingAnService;

    @Resource
    private PingAnShengQianMiniConfig pingAnShengQianMiniConfig;

    /**
     * @param orderId
     * @return ModelAndView
     */
    @GetMapping("/cashier")
    public ModelAndView cashier(@RequestParam("orderId") Long orderId, HttpServletRequest request) {
        OrdersDto order = null;
        try {
            ConsumerDto consumer = RequestLocal.getConsumerDO();
            AppSimpleDto app = RequestLocal.getConsumerAppDO();

            order = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid()).getResult();

            if (Objects.isNull(order) ||
                    !Objects.equals(order.getConsumerId(), consumer.getId()) ||
                    !Objects.equals(order.getAppId(), app.getId())) {
                LOG.error("获取订单或无权查看, orderId= {}, order= {}", orderId, order);
                return new ModelAndView(AMB_PAY_FAIL);
            }

            // 如果订单不需要支付, 或者订单支付
            DomainConfigDto domain = DomainCrossUtils.getSystemDomain(domainService);
            // 如果支付金额不大于0或者不需要支付则跳转到兑换详情页
            if (order.getConsumerPayPrice() <= 0
                    || !OrdersDto.StatusConsumeSuccess.equals(order.getStatus())
                    || !Objects.equals(order.getConsumerPayStatus(), OrdersDto.ConsumerPayStatusWaitPay)) {
                return new ModelAndView(VIEW_REDIRECT + domain.getTradeDomain() + OrderUriConstant.ORDER_DETAIL_URL + orderId);
            }

            ModelAndView view = new ModelAndView("consumer/h5/order_cashier");

            view.addObject("order", order);
            view.addObject("appCreditsConfig", AppCreditsUtils.getAppCredits(app, order.getCredits()));
            view.addObject("orderGmtCreate", order.getGmtCreate().getTime());

            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(orderId, AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (fasterDto != null) {
                view.addObject("payOrderEndTime", fasterDto.getScanTime().getTime());
            }

            boolean supportWxPay = false;
            if (paymentService.supportWxPay(app.getId())) {
                supportWxPay = true;
            }
            view.addObject("appId", app.getId());
            view.addObject("supportWxPay", supportWxPay);
            view.addObject("supportLitePay", paymentService.supportLitePay(app.getId()));
            List<String> appPayChannelTypes = remoteAppServiceNew.selectOpenAppPayChannelTypeByAppId(app.getId());
            // 商品定制隐藏支付
            appPayChannelTypes = hiddenWxPay(app, order, appPayChannelTypes);
            String appPayChannelTypesStr = String.join(",", appPayChannelTypes);
            view.addObject("appPayChannelTypes", appPayChannelTypesStr);
            // 直播定制，supportInstallmentPayment参数判断是否分期支付
            supportInstallmentPayment(order, app, view);
            // 全局色
            if (app.isColorSwitch()) {
                view.addObject("themeColor", app.getColor());
            }
            // 微信支付引导语句
            String wxPayGuideText = remoteAppNewExtraService.findByAppIdAndPropName(app.getId(), "wxpay_guide_config");
            view.addObject("wxPayGuideText", Optional.ofNullable(wxPayGuideText).orElse(""));

            return innerPageManager.handleInnerPageModelView(view, "/ambPay/cashier");
        } catch (Exception e) {
            LOG.error("查询订单详情错误, orderId= {}, order= {}", orderId, order, e);
            return new ModelAndView(AMB_PAY_FAIL);
        }
    }

    private List<String> hiddenWxPay(AppSimpleDto app, OrdersDto ordersDto, List<String> appPayChannelTypes) {
        if (Objects.isNull(ordersDto) || Objects.isNull(ordersDto.getAppItemId())) {
            return appPayChannelTypes;
        }
        try {
            if (!WhiteAccessUtil.selectWhiteListConfig(WilteListConstants.getWhiteKey()).contains(app.getId().toString())) {
                return appPayChannelTypes;
            }
            String jsonConfig = WhiteAccessUtil.selectWhiteListJsonConfig(WilteListConstants.HIDDEN_WX_PAY);
            if (StringUtils.isBlank(jsonConfig)) {
                return appPayChannelTypes;
            }
            JSONObject jsonObject = JSON.parseObject(jsonConfig);
            List<Long> goodIds = jsonObject.getObject("goodIds", new TypeReference<List<Long>>(ArrayList.class, Long.class) {
            });
            if (CollectionUtils.isEmpty(goodIds)) {
                return appPayChannelTypes;
            }
            if (goodIds.contains(ordersDto.getAppItemId())) {
                return ListUtils.removeAll(appPayChannelTypes, Arrays.asList("wxpay", "litepay"));
            }
        } catch (Exception e) {
            LOG.error("hiddenWxPay has error : ", e);
        }
        return appPayChannelTypes;
    }


    private void supportInstallmentPayment(OrdersDto order, AppSimpleDto app, ModelAndView view) {
        Boolean supportInstallmentPayment = Boolean.FALSE;
        if (WhiteAccessUtil.selectWhiteListConfig(WilteListConstants.getWhiteKey()).contains(app.getId().toString())) {
            if (order.getAppItemId() != null) {
                List<KeyValueDto> keyValueDtos = remoteItemNewExtraService.findValuesByAppItemIdAndPropNames(order.getAppItemId(), Collections.singletonList(WilteListConstants.IS_SUPPORT_INSTALLMENT_PAYMENT));
                if (CollectionUtils.isNotEmpty(keyValueDtos) && Objects.nonNull(keyValueDtos.get(0)) && StringUtils.equals(Boolean.TRUE.toString(), keyValueDtos.get(0).getPropValue())) {
                    supportInstallmentPayment = Boolean.TRUE;
                }
            }
        }
        view.addObject("yinlianChannelType", supportInstallmentPayment);
    }

    /**
     * @param orderId
     * @param request
     * @return ModelAndView
     */
    @RequestMapping(value = "/ambPay/{orderId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "扣积分成功跳转到支付倒计时页面")
    public ModelAndView ambPay1(@ApiParam(value = "订单编号", required = true) @PathVariable("orderId") Long orderId, HttpServletRequest request) {
        return this.ambPay(orderId, request);
    }

    /**
     * 提交支付接口
     *
     * @return ModelAndView
     */
    @RequestMapping(value = "/pay", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "扣积分成功跳转到支付倒计时页面")
    public ModelAndView pay(HttpServletRequest request) {
        try {
            Long orderId = Long.valueOf(request.getParameter("orderId"));
            String deductStr = StringUtils.isBlank(request.getParameter(WALLET_DEDUCT)) ? "0" : request.getParameter(WALLET_DEDUCT);
            String actualPayStr = StringUtils.isBlank(request.getParameter(ALI_PAY)) ? "0" : request.getParameter(ALI_PAY);

            Long deductAmount = MoneyUtil.convertToCent(deductStr);
            Long actualPay = MoneyUtil.convertToCent(actualPayStr);

            OrdersDto ordersDto = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid()).getResult();

            if (orderId == null) {
                throw new GoodsWebException("订单号为空");
            }

            /** 判断可否发起余额抵扣校验*/
            Long totalAmount = deductAmount + actualPay;
            if (!totalAmount.equals(ordersDto.getConsumerPayPrice())) {
                throw new GoodsWebException("金额计算错误，无法支付");
            }

            ModelAndView modelAndView = null;
            /** 是否包含用户钱包抵扣信息*/
            if (deductAmount.compareTo(0L) == 1) {
                this.walletDeduct(ordersDto, deductAmount);
            }

            /** 是否包含支付宝支付信息*/
            if (actualPay.compareTo(0L) == 1) {
                modelAndView = this.payMoney(orderId, request);
            }

            if (deductAmount.compareTo(totalAmount) == 0) {
                AppSimpleDto app = RequestLocal.getConsumerAppDO();
                DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
                modelAndView = new ModelAndView(VIEW_REDIRECT + domainConfigDto.getTradeDomain() + "/taw/onlyWalletDecute/dealPay?orderId=" + ordersDto.getId() + "&deductAmount=" + deductAmount);
            }
            return modelAndView;
        } catch (GoodsWebException e) {
            LOG.error("[AmbPayController-pay]error:{}", e);
            return new ModelAndView(AMB_PAY_FAIL);
        } catch (Exception e) {
            LOG.error("[AmbPayController-pay]error:{}", e);
            return new ModelAndView(AMB_PAY_FAIL);
        }
    }


    private void walletDeduct(OrdersDto order, Long deductAmount) {
        Long cid = RequestLocal.getCid();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        Long balance;
        try {
            balance = remoteWalletAccountService.findWalletBalanceAmountForExchange(app.getId(), cid);
        } catch (BizException e) {
            throw new GoodsWebException("查询用户钱包账户异常");
        }
        if (balance == null) {
            throw new GoodsWebException("用户钱包账户不存在");
        }
        /**  支付时，如果选择了用户钱包余额抵扣，则按照钱包余额最大抵扣额度进行抵扣*/
        if ((order.getConsumerPayPrice().compareTo(balance) == 0 || order.getConsumerPayPrice().compareTo(balance) == 1) && !balance.equals(deductAmount)) {
            throw new GoodsWebException("用户钱包余额发生变更");
        }
        createDeductRecord(order.getId(), deductAmount, cid, app);
    }


    /**
     * 创建资产抵扣记录
     */
    private void createDeductRecord(Long orderId, Long deductAmount, Long cid, AppSimpleDto app) {
        OrderAssetDeductDto orderAssetDeductDto = new OrderAssetDeductDto();
        orderAssetDeductDto.setAppId(app.getId());
        orderAssetDeductDto.setConsumerId(cid);
        orderAssetDeductDto.setDeductMoney(deductAmount);
        orderAssetDeductDto.setOrderId(orderId);
        remoteOrderAssetDeductService.createDeductRecord(orderAssetDeductDto);
    }

    /**
     * @param orderId
     * @param request
     * @return ModelAndView
     */
    @RequestMapping(value = "/ambPay", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "扣积分成功跳转到支付倒计时页面")
    public ModelAndView ambPayRESTful(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId, HttpServletRequest request) {
        return this.ambPay(orderId, request);
    }

    private ModelAndView ambPay(Long orderId, HttpServletRequest request) {
        Long cid = RequestLocal.getCid();
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, cid);
        OrdersDto ordersDO = ords.getResult();
        if (ordersDO == null) {
            throw new GoodsWebException("[支付倒计时页面] 订单不存在");
        }
        if (ordersDO.getConsumerPayPrice() <= 0
                || !OrdersDto.StatusConsumeSuccess.equals(ordersDO.getStatus())
                || !Objects.equals(ordersDO.getConsumerPayStatus(), OrdersDto.ConsumerPayStatusWaitPay)) {
            throw new GoodsWebException("不是可支付状态");
        }
        DubboResult<AmbSubOrdersDto> subs = remoteAmbSubOrdersService.findSubOrderById(ordersDO.getSubOrderId());
        AmbSubOrdersDto sub = subs.getResult();
        if (sub == null) {
            throw new GoodsWebException("加钱购子订单不存在");
        }

        AppSimpleDto app = remoteAppServiceNew.getSimpleApp(ordersDO.getAppId()).getResult();
        // 是否为 多商品 的标识
        Boolean showItems = false;
        // 判断是否为 多商品 含有子订单情况
        ItemsInfoVO itemsInfoVO = orderItemService.getItemKey(ordersDO, showItems, app);
        ItemKeyDto key = itemsInfoVO.getItemKey();
        // 是否为 多商品 的标识
        showItems = itemsInfoVO.getShowItems();
        // 多商品信息列表
        List<Map<String, Object>> items = itemsInfoVO.getItems();

        Map<String, Object> modelMap = new HashMap<>();
        modelMap.put("showItems", showItems);
        if (showItems) {
            modelMap.put("items", items);
        } else {
            CItemVO citem = consumerItemRenderService.getCItemVO(key, app);
            /**
             * 查询用户钱包余额，查询是否可以余额抵扣
             * queryConsumerWallet(cid, app, key, citem);
             * 20181121功能下架
             */
            modelMap.put("citem", citem);
            modelMap.put("needcredits", PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(citem.getCredits(), app));
        }

        modelMap.put("app", BeanUtils.copy(app, AppVO.class));
        modelMap.put("payMoney", ordersDO.getConsumerPayPrice());
        modelMap.put("orderNum", ordersDO.getOrderNum());
        modelMap.put("expressPrice", sub.getExpressPrice());
        modelMap.put("order", ordersDO);
        modelMap.put("receiver", generateReceiverInfo(ordersDO.getBizParams(), cid));
        // 摩拜单车定制
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        modelMap.put("isMobike", isMoBike);
        // 创建model
        ModelAndView model = new ModelAndView("consumer/mobile/amb_pay");
        model.addObject("isMobike", isMoBike);

        Long timedown = (System.currentTimeMillis() - ordersDO.getGmtCreate().getTime()) / 1000l;
        Long timedownCreateM = 29 - timedown / 60;
        Long timedownCreateS = 60 - timedown % 60;
        if (timedownCreateM < 0 || timedownCreateS < 0) {
            modelMap.put("timedownCreate", "00:00");
        } else {
            modelMap.put("timedownCreate", timedownCreateM + ":" + timedownCreateS);
        }
        modelMap.put("id", orderId.toString());

        boolean canPay = true;
        if (null != sub.getAmbPaychannelOrdersId()) {
            DubboResult<AmbPaychannelOrdersDto> cords = remoteAmbPaychannelOrdersService.findById(sub.getAmbPaychannelOrdersId());
            AmbPaychannelOrdersDto ambPaychannelOrdersDto = cords.getResult();
            if (null != ambPaychannelOrdersDto
                    && AmbPaychannelOrdersDto.PayChannelOrdersStatusSuccess.equals(ambPaychannelOrdersDto.getStatus())) {
                canPay = false;
            }
        }
        modelMap.put("canPay", canPay);
        modelMap.put("showCredits", AppIdConstant.showCredits(app.getId()));

        /** 返回sku信息*/
        List<String> skuList = remoteAppItemSkuService.reBuildSkuInfo(ordersDO.getExtraInfo());
        if (!CollectionUtils.isEmpty(skuList)) {
            modelMap.put("skuInfo", skuList);
        }

        boolean supportWxPay = false;
        if (paymentService.supportWxPay(app.getId())) {
            supportWxPay = true;
        }
        modelMap.put("supportWxPay", supportWxPay);
        List<String> appPayChannelTypes = remoteAppServiceNew.selectOpenAppPayChannelTypeByAppId(app.getId());
        String appPayChannelTypesStr = String.join(",", appPayChannelTypes);
        modelMap.put("appPayChannelTypes", appPayChannelTypesStr);
        // 微信公众号支付获取code回调
        String code = request.getParameter("code");
        modelMap.put("code", code);
        model.addObject("data", JSONObject.toJSONString(modelMap));
        model.addObject("app", app);
        model.addObject("showItems", showItems);
        return model;
    }

    /**
     * @deprecated 1.是否自有加钱购商品
     * 2.根据商品ID查询扩展信息判断该商品是否支持用户钱包抵扣
     * 3.如果支持余额抵扣，查询用户钱包余额；
     */
    @Deprecated
    private void queryConsumerWallet(Long cid, AppSimpleDto app, ItemKeyDto key, CItemVO citem) {
        if (key.isAmbItemKey() && key.isSelfAppItemMode()) {
            List<KeyValueEntity> extraInfoList = remoteItemNewExtraService.findAppItemAllApi(key.getAppItem().getId());
            List<KeyValueEntity> matchExtraInfoList = extraInfoList.stream().filter(keyValueEntity -> keyValueEntity.getPropName().equals(SUPPORT_BALANCE_DEDUCT) && keyValueEntity.getPropValue().equals("1")).collect(Collectors
                    .toList());
            if (CollectionUtils.isNotEmpty(matchExtraInfoList)) {
                try {
                    Long balance = remoteWalletAccountService.findWalletBalanceAmountForExchange(app.getId(), cid);
                    if (balance != null) {
                        citem.setWalletBalance(balance);
                    } else {
                        LOG.error("[MobileController-appItemDetail] error:用户钱包账户不存在，appId:{},ConsumerId:{}", app.getId(), cid);
                    }
                } catch (BizException e) {
                    LOG.error("[MobileController-appItemDetail] error:获取用户钱包账户错误,appId:{},ConsumerId:{}", app.getId(), cid);
                }
            }
        }
    }

    // 先从订单里去地址信息，如果没有，再去收货表里取，如果还没有，只能从用户表里去老数据了
    private Map<String, String> generateReceiverInfo(String bizParams, Long consumerId) {
        Map<String, String> receiverMap = Maps.newHashMap();
        // 地址库升级后的固定长度
        ImmutableList<String> attrList =
                ImmutableList.of("addrName", "addrPhone", "addrProvince", "addrCity", "addrArea", "addrStreet", "addrDetail");
        if (StringUtils.isNotBlank(bizParams)) {
            String[] str = bizParams.split(":");
            // 老数据大部分会走这个逻辑
            if (str.length < attrList.size()) {
                for (int i = 0; i < str.length - 1; i++) {
                    receiverMap.put(attrList.get(i), str[i]);
                }
                // 最后一位肯定是详细地址
                receiverMap.put(attrList.get(attrList.size() - 1), str[str.length - 1]);
            } else if (str.length == attrList.size()) {
                // 新数据的逻辑
                for (int i = 0; i < str.length; i++) {
                    receiverMap.put(attrList.get(i), str[i]);
                }
            } else {
                // 兼容一下异常情况
                for (int i = 0; i < attrList.size() - 1; i++) {
                    receiverMap.put(attrList.get(i), str[i]);
                }
                receiverMap.put(attrList.get(attrList.size() - 1), str[str.length - 1]);
            }
            return receiverMap;
        }
        // 地址库升级
        ReceiveAddressDto addressDto = addressService.getDefaultAddress(consumerId);
        if (addressDto != null) {
            receiverMap.put(attrList.get(0), addressDto.getAddrName());
            receiverMap.put(attrList.get(1), addressDto.getAddrPhone());
            receiverMap.put(attrList.get(2), addressDto.getAddrProvince());
            receiverMap.put(attrList.get(3), addressDto.getAddrCity());
            receiverMap.put(attrList.get(4), addressDto.getAddrArea());
            receiverMap.put(attrList.get(5), addressDto.getAddrStreet());
            receiverMap.put(attrList.get(6), addressDto.getAddrDetail());
        }
        return receiverMap;
    }

    /**
     * @param orderId
     * @param request
     * @return Message
     */
    @RequestMapping(value = "/canPayMoney", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "查询订单是否可去支付宝付款")
    public Message canPayMoney(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId, HttpServletRequest request) {
        Long cid = RequestLocal.getCid();
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, cid);
        OrdersDto ord = ords.getResult();
        JSONObject json = new JSONObject();
        String msgKey = "message";
        if (ord == null) {
            json.put(msgKey, "[支付宝付款] 订单不存在");
            return Message.error(json);
        }
        if (!ord.getConsumerId().equals(cid)) {
            json.put(msgKey, "无权访问");
            return Message.error(json);
        }
        // 判断是否在可支付状态"consume_success"
        if (OrdersDto.StatusConsumeSuccess.equals(ord.getStatus())) {
            return Message.success();
        } else {
            json.put(msgKey, "订单不是可支付状态");
            return Message.error(json);
        }

    }

    @RequestMapping(value = "/wxPayMp/getCode", method = RequestMethod.GET)
    public ModelAndView getAuthCode(@RequestParam Long orderId) {
        DubboResult<OrdersDto> ordersResult = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        if (!ordersResult.isSuccess()) {
            throw new GoodsWebException(ordersResult.getMsg());
        }
        OrdersDto ord = ordersResult.getResult();
        if (ord == null) {
            return errorModelAndView("主订单不存在");
        }
        Result<String> result = paymentService.buildCodeRequestUrl(RequestLocal.getAppId(), ord);
        if (result.getSuccess()) {
            return new ModelAndView(VIEW_REDIRECT + result.getData());
        }

        return errorModelAndView(result.getDesc());
    }

    @RequestMapping(value = "/wxPayMp/getCodeNew", method = RequestMethod.GET)
    public ModelAndView getAuthCodeNew(HttpServletRequest request) {
        Long appId = RequestLocal.getAppId();
        String orderIdStr = request.getParameter("orderId");
        if (StringUtils.isBlank(orderIdStr) && !NumberUtils.isNumeric(orderIdStr)) {
            return errorModelAndView("主订单不存在");
        }
        Long orderId = Long.valueOf(orderIdStr);
        DubboResult<OrdersDto> ordersResult = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        if (!ordersResult.isSuccess()) {
            throw new GoodsWebException(ordersResult.getMsg());
        }
        OrdersDto ord = ordersResult.getResult();
        if (ord == null) {
            throw new GoodsWebException("主订单不存在");
        }
        Result<String> result = paymentService.buildCodeRequestUrlNew(appId, request, ord);
        if (result.getSuccess()) {
            return new ModelAndView(VIEW_REDIRECT + result.getData());
        }
        return errorModelAndView(result.getDesc());
    }


    @RequestMapping(value = "/wxPayMp/getAuthCodeForXst", method = RequestMethod.GET)
    public ModelAndView getAuthCodeForXst(HttpServletRequest request) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        String redirectUrl = request.getParameter("redirectUrl");
        LOG.info("入参地址,url:{}", redirectUrl);
        try {
            Result<String> result = paymentService.buildCodeRequestUrlForXst(app.getId(), app.getDeveloperId(), StringUtils.replace(redirectUrl, "amp;", ""));
            if (result.getSuccess()) {
                return new ModelAndView(VIEW_REDIRECT + result.getData());
            }
        } catch (Exception e) {
            LOG.error("获取静默授权地址异常,url:{}", redirectUrl, e);
        }
        return errorModelAndView("获取authCode失败");
    }

    /**
     * 公众号支付
     */
    @RequestMapping(value = "/wxPayMp/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayMpResponseVO> wxPayMp(@RequestParam Long orderId, Integer visitChannel, @RequestParam String authCode) {
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId
                , RequestLocal.getCid()
                , paymentService.getAmbPayChannelType(RequestLocal.getAppId(), ChannelEnum.WX_PUB.getChannelType())
                , SubPaychannelType.WECHAT_PUB_PAY);

        VisitChannelEnum visitChannelEnum = VisitChannelEnum.getByCode(visitChannel);
        if (visitChannelEnum != null) {
            remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, visitChannel);
        }

        WxPayMpChargeRequest params = new WxPayMpChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAuthCode(authCode);
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBizOrderNo(ord.getOrderNum());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        return paymentService.wxPayMpCharge(params, ord);
    }

    /**
     * 发奖活动工具-公众号支付
     */
    @RequestMapping(value = "/wxPayMp/activity/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayMpResponseVO> activityWxPayMp(@RequestParam Long orderId, @RequestParam String openId) {
        // 查询活动订单
        SingleAwardRecordDto recordDto = getAndCheckActivityRecord(orderId);

        WxPayMpChargeRequest params = new WxPayMpChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setOpenId(openId);
        // 支付金额
        params.setAmount(recordDto.getPayAmount().intValue());
        // 活动订单号
        params.setBizOrderNo(String.valueOf(recordDto.getActivityOrderNum()));
        return paymentService.activityWxPayMpCharge(params, recordDto);
    }

    private SingleAwardRecordDto getAndCheckActivityRecord(Long orderId) {
        try {
            SingleAwardRecordDto recordDto = remoteSingleAwardRecordService.getByOrderNum(orderId);
            if (recordDto == null) {
                throw new GoodsWebException("主订单不存在");
            }
            if (!recordDto.getConsumerId().equals(RequestLocal.getCid())) {
                throw new GoodsWebException("无权访问");
            }
            return recordDto;
        } catch (Exception e) {
            throw new GoodsWebException("订单不存在");
        }
    }

    @RequestMapping(value = "wjrcb/wxPayMp")
    @ResponseBody
    public Result<String> getPrepay(@RequestParam Long orderId, @RequestParam String openId) {
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId,
                RequestLocal.getCid()
                , AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeWjrcb
                , SubPaychannelType.WECHAT_PUB_PAY);

        WjrcbPayWxPubChargeRequest request = new WjrcbPayWxPubChargeRequest();

        request.setDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setOpenId(openId);
        request.setAppId(RequestLocal.getAppId());
        return paymentService.wjrcbWxPubPay(request);
    }

    /**
     * 小程序支付
     */
    @RequestMapping(value = "/wxPayLite/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> wxPayLite(@RequestParam Long orderId, @RequestParam String authCode) {
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId
                , RequestLocal.getCid()
                , paymentService.getAmbPayChannelType(RequestLocal.getAppId(), ChannelEnum.WX_LITE.getChannelType())
                , SubPaychannelType.WECHAT_LITE_PAY);
        remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, VisitChannelEnum.WX_LITE.getCode());
        WxPayLiteChargeRequest params = new WxPayLiteChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAuthCode(authCode);
        params.setBizOrderNo(ord.getOrderNum());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        params.setChannelType(ChannelEnum.WX_LITE.getChannelType());

        try {
            customizeChannelTag(params, RequestLocal.getCid());
        } catch (Exception e) {
            LOG.warn("wxPayLite customizeChannelTag orderId = {}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
        return paymentService.wxPayLiteCharge(params, ord);
    }

    /**
     * 平安小程序支付
     */
    @RequestMapping(value = "/pingan/wxPayLite/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> pinganWxPayLite(@RequestParam String orderId,
                                                       @RequestParam String channel,
                                                       @RequestParam String authCode) {
        // 获取订单信息
        OrdersDto ord = checkOrderAndGetOrdersDtoForPingan(orderId, channel);
        WxPayLiteChargeRequest params = new WxPayLiteChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAuthCode(authCode);
        params.setBizOrderNo(ord.getOrderNum());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        params.setChannelType(ChannelEnum.WX_LITE.getChannelType());

        try {
            customizeChannelTag(params, RequestLocal.getCid());
        } catch (Exception e) {
            LOG.warn("wxPayLite customizeChannelTag orderId = {}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
        return paymentService.wxPayLiteChargeForPingan(params, ord, channel);
    }

    /**
     * 平安小程序支付-千猪
     */
    @RequestMapping(value = "/pingan/wxPayLite/qianzhuCharge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> pinganWxPayLiteForQianzhu(@RequestParam String orderId,
                                                                 @RequestParam String channel,
                                                                 @RequestParam String authCode) {
        // 获取订单信息
        OrdersDto ord = checkOrderAndGetOrdersDtoForPingan(orderId, channel);
        WxPayLiteChargeRequest params = new WxPayLiteChargeRequest();
        params.setAppId(pingAnShengQianMiniConfig.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAuthCode(authCode);
        params.setBizOrderNo(ord.getOrderNum());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        params.setChannelType(ChannelEnum.WX_LITE.getChannelType());
        return paymentService.wxPayLiteChargeForPingan(params, ord, channel);
    }


    private void customizeChannelTag(WxPayLiteChargeRequest params, Long cid) throws BizException {
        if (wxPayConfig.getMulliteAppIds().contains(params.getAppId())) {
            ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(cid).getResult();
            LOG.info("customizeChannelTag json = {}", JSON.toJSONString(consumerExtraDto));
            if (consumerExtraDto != null && StringUtils.isNotBlank(consumerExtraDto.getJson()) && JSONValidator.from(consumerExtraDto.getJson()).validate()) {
                JSONObject object = JSON.parseObject(consumerExtraDto.getJson());
                String channelStr = object.getString("channel");
                if (!StringUtils.isNumeric(channelStr)) {
                    throw new BizException("无法获取到用户小程序渠道");
                }
                Map<String, Integer> data = Maps.newHashMap();
                data.put("channel", Integer.valueOf(channelStr));
                params.setExtJson(JSON.toJSONString(data));
                return;
            }
            throw new BizException("无法获取到用户小程序渠道");
        }
    }


    /**
     * 万达定制-微信小程序支付
     */
    @RequestMapping(value = "/icbc/wxPayLite/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> wanDaWxPayLite(@RequestParam Long orderId, @RequestParam String authCode) {
        if (!wandaConfig.ifWandaApp(RequestLocal.getAppId())) {
            return ResultBuilder.fail("无权访问");
        }
        return wxPayCharge(orderId, authCode, ChannelEnum.ICBC_AP_WX_LITE, SubPaychannelType.ICBC_AP_WX_LITE_PAY);
    }

    /**
     * 工行-微信公众号/小程序支付
     *
     * @param orderId
     * @param authCode
     * @param channelEnum
     * @param subPaychannelType
     * @return
     */
    private Result<WxPayLiteResponseVO> wxPayCharge(Long orderId, String authCode, ChannelEnum channelEnum, SubPaychannelType subPaychannelType) {
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId
                , RequestLocal.getCid()
                , AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf
                , subPaychannelType);
        WxPayLiteChargeRequest params = new WxPayLiteChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAuthCode(authCode);
        params.setBizOrderNo(ord.getOrderNum());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        params.setChannelType(channelEnum.getChannelType());
        if (wanDaCustom(orderId, ord, params)) {
            return ResultBuilder.fail("未获取到小程序用户信息");
        }
        return paymentService.wxPayLiteCharge(params, ord);
    }

    private boolean wanDaCustom(Long orderId, OrdersDto ord, WxPayLiteChargeRequest params) {
//        if (SpringEnvironmentUtils.isTestEnv()) {
//            JSONObject wxExtraInfo = new JSONObject();
//            wxExtraInfo.put("openid", wandaConfig.getTestOpenId());
//            params.setExtJson(JSON.toJSONString(wxExtraInfo));
//            return false;
//        }
        // 万达不提供微信appSecret，从免登dcustom中获取openid
        DubboResult<ConsumerExtraDto> dubboResult = remoteConsumerExtraService.findByConsumerId(ord.getConsumerId());
        if (dubboResult.isSuccess() && dubboResult.getResult() != null) {
            ConsumerExtraDto consumerExtraDto = dubboResult.getResult();
            JSONObject extraInfo = JSON.parseObject(consumerExtraDto.getJson());
            if (extraInfo == null || !extraInfo.containsKey("openid") || StringUtils.isBlank(extraInfo.getString("openid"))) {
                LOG.warn("万达-工行聚合支付异常，未获取到用户openid，orderId={}", orderId);
                return true;
            }
            JSONObject wxExtraInfo = new JSONObject();
            wxExtraInfo.put("openid", extraInfo.getString("openid"));
            params.setExtJson(JSON.toJSONString(wxExtraInfo));
        }
        return false;
    }

    /**
     * 万达定制-公众号支付
     */
    @RequestMapping(value = "/icbc/wxPayMp/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> wdWxPayMp(@RequestParam Long orderId, @RequestParam String authCode) {
        if (!wandaConfig.ifWandaApp(RequestLocal.getAppId())) {
            return ResultBuilder.fail("无权访问");
        }
        return wxPayCharge(orderId, authCode, ChannelEnum.ICBC_AP_WX_PUB, SubPaychannelType.ICBC_AP_WX_PUB_PAY);
    }

    /**
     * 微信H5支付
     */
    @RequestMapping(value = "/wxPayWap/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> wxPayWap(@RequestParam Long orderId, Integer visitChannel) {
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId
                , RequestLocal.getCid()
                , paymentService.getAmbPayChannelType(RequestLocal.getAppId(), ChannelEnum.WX_WAP.getChannelType())
                , SubPaychannelType.WECHAT_H5PAY);

        VisitChannelEnum visitChannelEnum = VisitChannelEnum.getByCode(visitChannel);
        if (visitChannelEnum != null) {
            remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, visitChannel);
        }

        WxPayWapChargeRequest params = new WxPayWapChargeRequest();
        params.setAppId(RequestLocal.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBizOrderNo(ord.getOrderNum());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        return paymentService.wxPayWapCharge(params, ord);
    }

    /**
     * 微信H5支付 -- 新订单
     */
    @RequestMapping(value = "/wxPay", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> wxPay(@RequestParam Long orderId, Integer visitChannel, HttpServletRequest request, HttpServletResponse response) {

        DomainConfigDto domainConfigDto = DomainCrossUtils.getSystemDomain(domainService);
        DomainCrossUtils.crossDomain(request, response, domainConfigDto.getTradeDomain());

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId, consumer.getId(),
                paymentService.getAmbPayChannelType(consumer.getAppId(), ChannelEnum.WX_WAP.getChannelType()),
                SubPaychannelType.WECHAT_H5PAY);

        VisitChannelEnum visitChannelEnum = VisitChannelEnum.getByCode(visitChannel);
        if (visitChannelEnum != null) {
            remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, visitChannel);
        }

        WxPayWapChargeRequest params = new WxPayWapChargeRequest();
        params.setAppId(consumer.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBizOrderNo(ord.getOrderNum());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        return paymentService.wxPayH5Charge(params, ord, null);
    }

    @RequestMapping(value = "/szPay", method = RequestMethod.GET)
    @ResponseBody
    public Result<BankOfSuZhouResponseVO> szPay(@RequestParam Long orderId) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        // 苏州银行秒杀支付定制需求
        String errorRedirectUrl = suZhouBankPayService.payCunstomForSouZhouBank(orderId);
        if (StringUtils.isNotBlank(errorRedirectUrl)) {
            BankOfSuZhouResponseVO responseVO = new BankOfSuZhouResponseVO();
            responseVO.setGetwayUrl(errorRedirectUrl);
            return ResultBuilder.fail("", responseVO);
        }
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId, consumer.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSuZhou, SubPaychannelType.DEFAULT);
        BankOfSuZhouWapChargeRequest request = new BankOfSuZhouWapChargeRequest();

        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setGoodsDetail(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setIp(RequestLocal.getIp());
        request.setAppId(consumer.getAppId());
        request.setAmount(ord.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ord.getOrderNum());

        return paymentService.szPay(request);
    }

    @RequestMapping(value = "/ccbPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> ccbPay(@RequestParam Long orderId, String remark1, String remark2) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.CCB);

        String posId;
        try {
            posId = parseCcbPosId(ordersDto);
            if (StringUtils.isBlank(posId)) {
                LOG.warn("posId为空: orderNum={}, bizParams={}", ordersDto.getOrderNum(), ordersDto.getBizParams());
                return ResultBuilder.fail("下单失败，请联系客服");
            }
        } catch (Exception e) {
            LOG.warn("posId获取失败: {}", ordersDto.getBizParams(), e);
            return ResultBuilder.fail("下单失败，请联系客服");
        }
        CcbWapChargeRequest request = new CcbWapChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setPosId(posId);
        request.setRemark1(remark1);
        request.setRemark2(remark2);

        return paymentService.ccbPay(request);
    }


    @RequestMapping(value = "/unionPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<UnionPayResponseVO> unionPay(
            @RequestParam String url,
            @RequestParam Long orderId) {
        try {
            ConsumerDto consumerDto = RequestLocal.getConsumerDO();

            OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                    AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeUnionPay, SubPaychannelType.UNION_PAY);

            // TODO appId定制

            UnionPayWapChargeRequest request = convert2Request(ordersDto);

            Result<String> result = paymentService.unionPay(request);
            if (result.getSuccess()) {
                String tn = result.getData();

                // 组装upsdk唤起支付需要的参数
                String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
                String nonceStr = ZjUnionPayRequestTool.createNonceStr();

                String frontToken = ZjUnionPayRequestTool.getFrontToken(APP_ID_VALUE, APP_SECRET_VALUE);

                TreeMap<String, String> resultMap = new TreeMap<>(Comparator.naturalOrder());
                resultMap.put(ZjUnionPayRequestTool.Constants.APP_ID_KEY, APP_ID_VALUE);
                resultMap.put(ZjUnionPayRequestTool.Constants.TIMESTAMP, timestamp);
                resultMap.put(ZjUnionPayRequestTool.Constants.NONCE_STR, nonceStr);
                resultMap.put(ZjUnionPayRequestTool.Constants.FRONT_TOKEN, frontToken);
                resultMap.put(ZjUnionPayRequestTool.Constants.URL, url);

                String sign = ZjUnionPayRequestTool.signNoSecret(resultMap);
                resultMap.put(ZjUnionPayRequestTool.Constants.SIGNATURE, sign);

                UnionPayResponseVO responseVO = new UnionPayResponseVO();
                responseVO.setTn(tn);
                responseVO.setAppId(APP_ID_VALUE);
                responseVO.setTimestamp(timestamp);
                responseVO.setNonceStr(nonceStr);
                responseVO.setFrontToken(frontToken);
                responseVO.setUrl(url);
                responseVO.setSignature(sign);

                return ResultBuilder.success(responseVO);
            } else {
                return ResultBuilder.fail(result.getCode(), result.getDesc());
            }
        } catch (BizException e) {
            LOG.error("unionpay 获取backendToken 异常,orderId={}", orderId);
            return ResultBuilder.fail(e.getCode(), e.getMessage());
        }
    }

    private UnionPayWapChargeRequest convert2Request(OrdersDto ordersDto) {
        UnionPayWapChargeRequest unionPayWapChargeRequest = new UnionPayWapChargeRequest();
        unionPayWapChargeRequest.setMerId(getMerId(ordersDto.getAppId()));
        unionPayWapChargeRequest.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        unionPayWapChargeRequest.setResultNotifyUrl(null);
        unionPayWapChargeRequest.setAmount(ordersDto.getConsumerPayPrice().intValue());
        unionPayWapChargeRequest.setBizOrderNo(ordersDto.getOrderNum());
        unionPayWapChargeRequest.setBizType(BizTypeEnum.ORD.getCode());

        unionPayWapChargeRequest.setIp(RequestLocal.getIp());
        unionPayWapChargeRequest.setAppId(ordersDto.getAppId());

        unionPayWapChargeRequest.setSubjectType(null);
        return unionPayWapChargeRequest;
    }

    private String getMerId(Long appId) {
        UnionPayConfig.MerIdConfigBO merIdConfigBO = unionPayConfig.getMerIdConfigMap().get(appId);
        if (merIdConfigBO != null) {
            return merIdConfigBO.getMerId();
        }
        // 默认返回兑吧正式
        return "898991148166588";
    }

    private static final String APP_ID_VALUE = "7f701174bf8a4cb681cb58b9db995429";
    private static final String APP_SECRET_VALUE = "51a1d4d30bf948eb84b2b3cc1381b5da";

    @RequestMapping(value = "/abcPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<PayUrlVo> abcPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeAbc, SubPaychannelType.ABC);

        AbcWapChargeRequest request = new AbcWapChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        SimpleDateFormat day_str = new SimpleDateFormat("yyyy/MM/dd");
        request.setCreateDate(day_str.format(ordersDto.getGmtCreate()) + " " + DateUtil.getSecondOnlyStr(ordersDto.getGmtCreate()));
        PayUrlVo payUrlVo = new PayUrlVo();
        Result<String> data = paymentService.abcPay(request);
        if (data.getSuccess()) {
            payUrlVo.setPayUrl(data.getData());
            return ResultBuilder.success(payUrlVo);
        } else {
            return ResultBuilder.fail(data.getDesc());
        }

    }

    @RequestMapping(value = "/bocPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<BocWapChargeResponse> bocPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeBoc, SubPaychannelType.BOC);

        BocWapChargeRequest request = new BocWapChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        SimpleDateFormat day_str = new SimpleDateFormat("yyyyMMddHHmmss");
        request.setCreateDate(day_str.format(ordersDto.getGmtCreate()));
        Result<BocWapChargeResponse> data = paymentService.bocPay(request);
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }
    }

    @RequestMapping(value = "/citicPay/charge")
    @ResponseBody
    public Result<CiticWapChargeResponse> citicPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeCitic, SubPaychannelType.CITIC);

        CiticWapChargeRequest request = new CiticWapChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setConsumerId(consumerDto.getPartnerUserId());
        if (StringUtils.isBlank(request.getConsumerId())) {
            request.setConsumerId(ordersDto.getConsumerId() + "");
        }
        request.setItemType(ordersDto.getType());
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(consumerDto.getAppId());
        String callbackUrl = "";
        if (citicBankTaiYuanConfig.getAppIds().contains(consumerDto.getAppId())) {
            callbackUrl = RequestLocal.getRequest().getScheme() + ":" + domainConfigDto.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + ordersDto.getId();
        } else {
            callbackUrl = RequestLocal.getRequest().getScheme() + ":" + domainConfigDto.getTradeDomain() + "/crecord/orderPayResult?orderId=" + ordersDto.getId();
        }
        request.setCallbackUrl(callbackUrl);
        SimpleDateFormat day_str = new SimpleDateFormat("yyyyMMdd HHmmss");
        request.setCreateDate(day_str.format(ordersDto.getGmtCreate()));
        request.setSupplierTag(customizeSupplierTag(ordersDto, request));
        Result<CiticWapChargeResponse> data = paymentService.citicPay(request);
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }
    }

    /**
     * 南宁中信定制,支付时,设置供应商
     */
    private Integer customizeSupplierTag(OrdersDto ordersDto, BaseChargeRequest request) {
        if (citicBankNanningConfig.getAppIdSet().contains(request.getAppId())) {
            String extraInfo = ordersDto.getExtraInfo();
            if (StringUtils.isNotBlank(extraInfo) && JSONValidator.from(extraInfo).validate()) {
                Integer supplierTag = JSONObject.parseObject(extraInfo).getInteger(ExtraInfoUtils.SUPPLIER_TAG);
                return supplierTag;
            }
        }

        return null;
    }

    @GetMapping(value = "/cmbOneNetPay/charge")
    @ResponseBody
    public Result<CmbOneNetPayResponseVO> cmbOneNetPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeCmbOneNet, SubPaychannelType.CMB_ONE_NET);

        CmbOneNetPayRequest chargeRequest = new CmbOneNetPayRequest();
        chargeRequest.setAppId(consumerDto.getAppId());
        chargeRequest.setAmount(ordersDto.getConsumerPayPrice().intValue());
        chargeRequest.setBizOrderNo(ordersDto.getOrderNum());
        chargeRequest.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        chargeRequest.setClientIP(RequestLocal.getIp());
        chargeRequest.setOrderDate(ordersDto.getGmtCreate());

        return paymentService.cmbOneNetPay(chargeRequest);
    }

    /**
     * 前置支付校验
     */
    private OrdersDto checkOrderAndGetOrdersDto(Long orderId, Long consumerId, String channelType, SubPaychannelType subChannel) {
        DubboResult<OrdersDto> ordersResult = remoteConsumerOrderSimpleService.findById(orderId, consumerId);
        if (!ordersResult.isSuccess()) {
            throw new GoodsWebException(ordersResult.getMsg());
        }
        OrdersDto ord = ordersResult.getResult();
        if (ord == null) {
            throw new GoodsWebException("主订单不存在");
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            throw new GoodsWebException("无权访问");
        }
        DubboResult<AmbSubOrdersDto> subs = remoteAmbSubOrdersService.findSubOrderById(ord.getSubOrderId());
        if (!subs.isSuccess()) {
            throw new GoodsWebException(subs.getMsg());
        }
        AmbSubOrdersDto sub = subs.getResult();
        if (sub == null) {
            throw new GoodsWebException("加钱购子订单不存在");
        }
        // 一分扫订单
        perCentScan(subChannel, ord);

        AmbPaychannelOrdersDto cord = null;
        if (sub.getAmbPaychannelOrdersId() != null) {
            DubboResult<AmbPaychannelOrdersDto> cords = remoteAmbPaychannelOrdersService.findById(sub.getAmbPaychannelOrdersId());
            cord = cords.getResult();
        }
        if (cord == null) {
            remoteAmbPaychannelOrdersService.createAmbPayChannelOrder(ord, channelType, subChannel);
        } else {
            remoteAmbPaychannelOrdersService.updatePayChannel4PaychannelTypeById(cord.getId(), channelType, subChannel);
        }
        return ord;
    }

    private OrdersDto checkOrderAndGetOrdersDtoForPingan(String orderId, String orderChannel) {
        // 并发控制
        try (RedisLock lock = redisAtomicClient.getLock(RedisKeyFactory.K607.join(orderId), 5)) {
            if (lock == null) {
                throw new GoodsWebException("系统繁忙请稍后再试");
            }
            if (PingAnOrderTypeEnum.OWN.getType().equals(orderChannel)) {
                return pingAnService.handleOwnOrder(orderId);
            }
            if (PingAnOrderTypeEnum.QIANZHU.getType().equals(orderChannel)) {
                return pingAnService.handleQianzhuOrder(orderId);
            }
            if (PingAnOrderTypeEnum.FL.getType().equals(orderChannel)) {
                return pingAnService.handleFlOrder(orderId);
            }
            throw new GoodsWebException("未知的支付类型");
        } catch (GoodsWebException goodsWebException) {
            LOG.error("平安小程序 支付异常", goodsWebException);
            throw new GoodsWebException(goodsWebException.getMessage());
        } catch (Exception e) {
            LOG.error("平安小程序 支付异常", e);
            throw new GoodsWebException("系统异常请稍后再试");
        }
    }


    private OrdersDto handleHadCreate(String orderId, String orderChannel, PingAnShengQianDeductionDto qianDeductionDto) {
        OrdersDto ordersDto = new OrdersDto();
        if (PingAnOrderTypeEnum.OWN.getType().equals(orderChannel)) {
            return checkOrderAndGetOrdersDto(
                    Long.parseLong(orderId)
                    , RequestLocal.getCid()
                    , paymentService.getAmbPayChannelType(RequestLocal.getAppId(), ChannelEnum.WX_LITE.getChannelType())
                    , SubPaychannelType.WECHAT_LITE_PAY);
        } else if (PingAnOrderTypeEnum.QIANZHU.getType().equals(orderChannel)) {
            List<PingAnShengQianOrderDto> orderDtos = remotePingAnSqService.queryActOrderByOrderIds(Lists.newArrayList(orderId));
            if (CollectionUtils.isEmpty(orderDtos)) {
                throw new GoodsWebException("千猪订单不存在");
            }
            PingAnShengQianOrderDto pingAnShengQianOrderDto = orderDtos.get(0);
            if (pingAnShengQianOrderDto.getStatus() != 0) {
                throw new GoodsWebException("当前订单状态非待付款");
            }
            ordersDto.setConsumerPayPrice(new BigDecimal(pingAnShengQianOrderDto.getTotalPrice()).multiply(new BigDecimal(100L)).longValue());
            ordersDto.setBrief(pingAnShengQianOrderDto.getFilmName());
            // 千猪订单剩余支付时间
            ordersDto.setFailType(pingAnShengQianOrderDto.getPaymentExpireSeconds());
            ordersDto.setOrderNum(qianDeductionDto.getPayOrderNum());
            return ordersDto;
        }
        throw new GoodsWebException("未知订单类型");
    }


    /**
     * //一分扫订单
     *
     * @param subChannel
     * @param ord
     */
    private void perCentScan(SubPaychannelType subChannel, OrdersDto ord) {
        if (ObjectUtils.equals(ord.getRelationType(), OrdersDto.RelationTypeCentscan)) {
            if (subChannel == SubPaychannelType.WAP_ALI_PAY) {
                throw new GoodsWebException("此订单无法用支付宝支付");
            }
            // 口红机一分扫活动定制逻辑
            if (centscanConfig.getLipstickIds().contains(ord.getAppId())) {
                if (!lipstickBo.checkPay(ord.getId(), ord.getAppId())) {
                    throw new GoodsWebException("订单非法,无法支付");
                }
            }
        }
    }

    @RequestMapping(value = "/alipay/charge", method = RequestMethod.GET)
    public ModelAndView alipayCharge(@RequestParam Long orderId, HttpServletRequest request) {
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId
                , RequestLocal.getCid()
                , paymentService.getAliPayChannelType(RequestLocal.getAppId(), ChannelEnum.ALIPAY_WAP.getChannelType())
                , SubPaychannelType.WAP_ALI_PAY);

        DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(ord.getAppId(), domainService);
        String return_url = domainConfig.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + orderId + "&after=1";
        // 商品展示网址
        String show_url = RequestTool.getServerPath(request) + "/ambPay/ambPay?orderId=" + orderId + "&isReturnUrl=1";
        AlipayWapChargeRequest alipayWapChargeRequest = new AlipayWapChargeRequest();

        alipayWapChargeRequest.setReturnUrl(return_url);
        alipayWapChargeRequest.setQuitUrl(show_url);
        alipayWapChargeRequest.setSubject(ord.getBrief());
        alipayWapChargeRequest.setAmount(ord.getConsumerPayPrice().intValue());
        alipayWapChargeRequest.setAppId(RequestLocal.getAppId());
        alipayWapChargeRequest.setBizOrderNo(ord.getOrderNum());
        alipayWapChargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        alipayWapChargeRequest.setChannelType(ChannelEnum.ALIPAY_WAP.getChannelType());
        Result<String> result = paymentService.alipayWapCharge(alipayWapChargeRequest, ord);
        if (result.getSuccess()) {
            return new ModelAndView(VIEW_REDIRECT + result.getData());
        } else {
            return errorModelAndView("支付请求失败，请稍后再试！");
        }
    }

    /**
     * 新订单alipay支付
     *
     * @param orderId
     * @param request
     * @throws IOException
     */
    @RequestMapping(value = "/aliPay", method = RequestMethod.GET)
    public ModelAndView aliPay(@RequestParam Long orderId, Integer visitChannel, HttpServletRequest request) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId, consumer.getId(),
                paymentService.getAliPayChannelType(consumer.getAppId(), ChannelEnum.ALIPAY_WAP.getChannelType()),
                SubPaychannelType.WAP_ALI_PAY);
        DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(ord.getAppId(), domainService);

        VisitChannelEnum visitChannelEnum = VisitChannelEnum.getByCode(visitChannel);
        if (visitChannelEnum != null) {
            remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, visitChannel);
        }

        // 支付取消 > 订单详情页
        String showUrl = request.getScheme() + ":" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_DETAIL_URL + orderId;
        if (customPayDomainConfig.getPayResultAppIds().contains(consumer.getAppId())) {
            showUrl = request.getScheme() + ":" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + orderId;
        }

        // 支付完成返回地址 > 支付结果页
        String returnUrl = request.getScheme() + ":" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + orderId;

        AlipayWapChargeRequest alipayWapChargeRequest = new AlipayWapChargeRequest();
        alipayWapChargeRequest.setReturnUrl(returnUrl);
        alipayWapChargeRequest.setQuitUrl(showUrl);
        alipayWapChargeRequest.setSubject(ord.getBrief());
        alipayWapChargeRequest.setAmount(ord.getConsumerPayPrice().intValue());
        alipayWapChargeRequest.setAppId(ord.getAppId());
        alipayWapChargeRequest.setBizOrderNo(ord.getOrderNum());
        alipayWapChargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        alipayWapChargeRequest.setChannelType(ChannelEnum.ALIPAY_WAP.getChannelType());

        Result<String> result = paymentService.alipayWapCharge(alipayWapChargeRequest, ord);

        if (result.getSuccess()) {
            return new ModelAndView(VIEW_REDIRECT + result.getData());
        } else {
            return errorModelAndView("支付请求失败，请稍后再试！");
        }
    }

    /**
     * @param orderId
     * @param request
     * @return ModelAndView
     */
    @RequestMapping(value = "/aliPay/payMoney", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "唤起阿里支付网关")
    public ModelAndView payMoney(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId, HttpServletRequest request) {
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        OrdersDto ord = ords.getResult();
        if (ord == null) {
            throw new GoodsWebException("[阿里支付网关] 订单不存在");
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            throw new GoodsWebException("无权访问");
        }
        DubboResult<AmbSubOrdersDto> subs = remoteAmbSubOrdersService.findSubOrderById(ord.getSubOrderId());
        AmbSubOrdersDto sub = subs.getResult();
        if (sub == null) {
            throw new GoodsWebException("[阿里支付网关] 子订单不存在");
        }
        AmbPaychannelOrdersDto cord = null;
        if (sub.getAmbPaychannelOrdersId() != null) {
            DubboResult<AmbPaychannelOrdersDto> cords = remoteAmbPaychannelOrdersService.findById(sub.getAmbPaychannelOrdersId());
            cord = cords.getResult();
        }

        OrdersDto ordersDto = whenWalletDeduct(request, ord);
        String channelType = paymentService.getAliPayChannelType(RequestLocal.getAppId(), ChannelEnum.ALIPAY_WAP.getChannelType());
        if (cord == null) {
            remoteAmbPaychannelOrdersService.createAmbPayChannelOrder(ordersDto, channelType, SubPaychannelType.WAP_ALI_PAY);
        } else {
            remoteAmbPaychannelOrdersService.updatePayChannel4PaychannelTypeById(cord.getId(), channelType, SubPaychannelType.WAP_ALI_PAY);
        }
        // 服务器异步通知页面路径
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(ord.getAppId());
        String notify_url = "https:" + domainConfigDto.getTradeDomain() + "/aliPay/notifyUrl";
        // 页面跳转同步通知页面路径 ?after=1:标识从成功回来，详情页显示成功信息
        String return_url = RequestTool.getServerPath(request) + "/ambPay/aliPay/returnUrl";
        // 商品展示网址
        String show_url = RequestTool.getServerPath(request) + "/ambPay/ambPay?orderId=" + orderId + "&isReturnUrl=1";
        // 把请求参数打包成数组
        StringBuilder params = null;
        try {
            Map<String, String> payParam = new HashMap<>();
            payParam.put("service", AlipayUtil.service);
            payParam.put("partner", AlipayUtil.partner);
            payParam.put("seller_id", AlipayUtil.seller_id);
            payParam.put("_input_charset", AlipayUtil.input_charset);
            payParam.put("payment_type", AlipayUtil.payment_type);
            payParam.put("notify_url", notify_url);
            payParam.put("return_url", return_url);
            payParam.put("out_trade_no", ord.getOrderNum());
            payParam.put("subject", new String(ord.getBrief().getBytes(), "utf-8"));
            payParam.put("total_fee", MoneyUtil.formatMoneyToKeep2Point(ordersDto.getConsumerPayPrice()));
            payParam.put("show_url", show_url);
            payParam.put("app_pay", "Y");
            // 生成签名结果
            String mysign = AlipayUtil.buildRequestMysign(payParam);
            // 签名结果与签名方式加入请求提交参数组中
            payParam.put("sign", mysign);
            payParam.put("sign_type", AlipayUtil.sign_type);
            params = new StringBuilder();
            for (String key : payParam.keySet()) {// NOSONAR
                params.append(key + "=" + URLEncoder.encode(payParam.get(key), "UTF-8") + "&");
            }
        } catch (UnsupportedEncodingException e) {
            LOG.error("请求参数组装失败", e);
            return errorModelAndView("请求参数组装失败");
        }

        return new ModelAndView(VIEW_REDIRECT + AlipayUtil.ALIPAY_GATEWAY_NEW + params.toString().substring(0, params.length() - 1));
    }

    /**
     * 特殊处理：
     * 当 actualPayAmount参数不为空时(用户支付时，含有用户钱包余额抵扣)
     * 因实际支付金额非订单中的ConsumerPayPrice属性，而创建渠道记录时支付金额取值为ConsumerPayPrice
     * 这里将拷贝一份订单对象副本，将ConsumerPayPrice属性赋值为实际支付金额，以此创建支付渠道记录
     */
    private OrdersDto whenWalletDeduct(HttpServletRequest request, OrdersDto ord) {
        OrdersDto ordersDto;
        String actualPayStr = StringUtils.isBlank(request.getParameter(ALI_PAY)) ? "0" : request.getParameter(ALI_PAY);
        Long actualPayAmount = MoneyUtil.convertToCent(actualPayStr);
        if (actualPayAmount.compareTo(0L) == 1) {
            OrdersDto containsWalletDeductOrdersDto = BeanUtils.copy(ord, OrdersDto.class);
            containsWalletDeductOrdersDto.setConsumerPayPrice(actualPayAmount);
            ordersDto = containsWalletDeductOrdersDto;
        } else {
            ordersDto = ord;
        }

        return ordersDto;
    }

    /**
     * 请求第三方收银台（开发者自有收银台）创建支付订单
     *
     * @param orderId
     * @param request
     * @return ModelAndView
     */
    @RequestMapping(value = "/creatThirdPartyOrder", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "请求创建开发者支付订单")
    public String creatThirdPartyOrder(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId, HttpServletRequest request) {
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        OrdersDto ord = ords.getResult();
        if (ord == null) {
            throw new GoodsWebException("[开发者支付订单] 订单不存在");
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            throw new GoodsWebException("无权访问");
        }
        DubboResult<AmbSubOrdersDto> subs = remoteAmbSubOrdersService.findSubOrderById(ord.getSubOrderId());
        AmbSubOrdersDto sub = subs.getResult();
        if (sub == null) {
            throw new GoodsWebException("[开发者支付订单] 子订单不存在");
        }
        AmbPaychannelOrdersDto cord = null;
        if (sub.getAmbPaychannelOrdersId() != null) {
            DubboResult<AmbPaychannelOrdersDto> cords = remoteAmbPaychannelOrdersService.findById(sub.getAmbPaychannelOrdersId());
            cord = cords.getResult();
        }

        if (remoteSaasGrantService.belongToHaiDiLaoDeveloper(ord.getDeveloperId())) {
            haidilaoExchangePayType(request, cord, ord);
        } else if (cord == null) {
            remoteAmbPaychannelOrdersService.createAmbPayChannelOrder(ord, AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeDeveloper, SubPaychannelType.WECHAT_H5PAY);
        } else {
            remoteAmbPaychannelOrdersService.updatePayChannel4PaychannelTypeById(cord.getId(), AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeDeveloper, SubPaychannelType.WECHAT_H5PAY);
        }

        return thirdPartyCashierDeskService.creatThirdPartyOrder(ord);
    }

    /**
     * 海底捞对应的一些判断
     *
     * @param request
     * @param cord
     */
    private void haidilaoExchangePayType(HttpServletRequest request, AmbPaychannelOrdersDto cord, OrdersDto ord) {
        // 海底捞需要加入支付方式
        String payTypeCode = request.getParameter("payTypeCode");
        if (StringUtils.isBlank(payTypeCode)) {
            throw new GoodsWebException("支付方式必填");
        }
        SubPaychannelType subPaychannelType = SubPaychannelType.getEnum(payTypeCode);
        if (SubPaychannelType.DEFAULT.equals(subPaychannelType)) {
            throw new GoodsWebException("支付类型有误");
        }

        if (cord == null) {
            remoteAmbPaychannelOrdersService.createAmbPayChannelOrder(ord, AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeDeveloper, subPaychannelType);
        } else {
            SubPaychannelType cordType = SubPaychannelType.getEnum(cord.getSubPaychannelType());
            // 用户更改了支付方式
            if (!cordType.equals(subPaychannelType)) {
                remoteAmbPaychannelOrdersService.updateSubPaychannelTypeById(cord.getId(), subPaychannelType);
            }
        }
    }


    /**
     * @param orderId
     * @param request
     * @return Message
     */
    @RequestMapping(value = "/payStatus", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "查询支付状态")
    public Message payStatus(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId, HttpServletRequest request) {
        Long cid = RequestLocal.getCid();
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, cid);
        OrdersDto ord = ords.getResult();
        if (ord == null) {
            return Message.error("订单不存在");
        }
        if (!ord.getConsumerId().equals(cid)) {
            return Message.error("无权访问");
        }
        DubboResult<AmbSubOrdersDto> subs = remoteAmbSubOrdersService.findSubOrderById(ord.getSubOrderId());
        AmbSubOrdersDto sub = subs.getResult();
        if (sub == null || null == sub.getAmbPaychannelOrdersId()) {
            return Message.error("子订单不存在");
        }
        DubboResult<AmbPaychannelOrdersDto> cords = remoteAmbPaychannelOrdersService.findById(sub.getAmbPaychannelOrdersId());
        ;
        AmbPaychannelOrdersDto cord = cords.getResult();
        if (cord == null) {
            return Message.error("加钱购支付通道不存在");
        }
        JSONObject json = new JSONObject();
        if (AmbPaychannelOrdersDto.PayChannelOrdersStatusWaitPay.equals(cord.getStatus())) {
            json.put("status", "waitPay");
            json.put("message", "付款未成功");
        } else {
            json.put("status", "paied");
            json.put("url", "/crecord/record");
        }
        return Message.success(json);
    }

    /**
     * @param request
     * @return String
     */
    @RequestMapping(value = "/aliPay/returnUrl", method = RequestMethod.GET)
    @ApiOperation(value = "同步通知页面")
    public String returnUrl(HttpServletRequest request) {
        // 获取支付宝的通知返回参数，可参考技术文档中页面跳转同步通知参数列表(以下仅供参考)//
        // 商户订单号

        String out_trade_no = null;
        try {
            out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOG.error("编码异常", e);
            return null;
        }
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(null);
        ChargeOrderDto chargeOrderDto = paymentService.findByOutTradeNo(out_trade_no);
        String orderNo;
        if (chargeOrderDto == null) {
            orderNo = out_trade_no;
        } else {
            orderNo = chargeOrderDto.getBizOrderNo();
        }
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findByOrderNum(orderNo);
        if (ords.isSuccess() && ords.getResult() != null) {
            return VIEW_REDIRECT + domainConfigDto.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + ords.getResult().getId() + "&after=1";
        } else {
            throw new GoodsWebException("订单不存在");
        }
    }

    private ModelAndView errorModelAndView(String message) {
        return new ModelAndView("error", "error", message);
    }

    /**
     * 微信H5支付 一分扫
     */
    @RequestMapping(value = "/centscan/wxPay", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> centscanWxPay(@RequestParam Long orderId, Integer visitChannel, HttpServletRequest request, HttpServletResponse response) {

        DomainConfigDto domainConfigDto = DomainCrossUtils.getSystemDomain(domainService);
        DomainCrossUtils.crossDomain(request, response, domainConfigDto.getTradeDomain());

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        OrdersDto ord = checkOrderAndGetOrdersDto(
                orderId, consumer.getId(),
                paymentService.getAmbPayChannelType(consumer.getAppId(), ChannelEnum.WX_WAP.getChannelType()),
                SubPaychannelType.WECHAT_H5PAY);

        VisitChannelEnum visitChannelEnum = VisitChannelEnum.getByCode(visitChannel);
        if (visitChannelEnum != null) {
            remoteOrdersTextChangeService.addExtraInfoNew(ord.getId(), ord.getConsumerId(), OrdersDto.PAY_VISIT_CHANNEL, visitChannel);
        }

        WxPayWapChargeRequest params = new WxPayWapChargeRequest();
        params.setAppId(consumer.getAppId());
        params.setClientIp(RequestLocal.getIp());
        params.setAmount(ord.getConsumerPayPrice().intValue());
        params.setBizOrderNo(ord.getOrderNum());
        params.setBody(new String(ord.getBrief().getBytes(), StandardCharsets.UTF_8));
        DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(consumer.getAppId(), domainService);
        String redirectUrl = wxPayConfig.getUrl() + OrderUriConstant.ORDER_CENTSCANPAY_RESULT_URL + "pageCode=" + RedirectPageEnum.PAGE_ORDER.getCode() + "&orderId=" + orderId;
        if (centscanConfig.getLipstickIds().contains(consumer.getAppId())) {
            redirectUrl = wxPayConfig.getUrl() + centscanConfig.getLipstickUrl();
        }
        return paymentService.wxPayH5Charge(params, ord, redirectUrl);
    }

    @RequestMapping(value = "/wxPayMp/getCodeUrl", method = RequestMethod.GET)
    public ModelAndView getCodeUrl(HttpServletRequest request) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        String url = request.getParameter("url");
        if (StringUtils.isBlank(url)) {
            return errorModelAndView("路径配置异常");
        }
        String unescapeHtml3url = StringEscapeUtils.unescapeHtml3(url);
        Result<String> result = paymentService.buildCodeRequestUrlAjax(app.getId(), app.getDeveloperId(), unescapeHtml3url);
        if (result.getSuccess()) {
            return new ModelAndView(VIEW_REDIRECT + result.getData());
        }
        return errorModelAndView(result.getDesc());
    }

    @RequestMapping(value = "/helloPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<HellopayResponseVO> helloPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.HELLO);

        HelloPayChargeRequest request = new HelloPayChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setIp(RequestLocal.getIp());
        request.setUid(consumerDto.getPartnerUserId());
        Result<HellopayResponseVO> data = paymentService.helloPay(request);
        if (data.getSuccess()) {
            DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(consumerDto.getAppId(), domainService);
            HellopayResponseVO hellopayResponseVO = data.getData();
            hellopayResponseVO.setRedirectUrl("https:" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + ordersDto.getId());
            return ResultBuilder.success(hellopayResponseVO);
        } else {
            return ResultBuilder.fail(data.getDesc());
        }

    }

    @RequestMapping(value = "/shouxinPay/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<ShouxinpayResponseVO> shouxinPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.SHOUXIN);
        ShouxinPayChargeRequest request = new ShouxinPayChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setIp(RequestLocal.getIp());
        request.setUid(consumerDto.getPartnerUserId());
        DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(consumerDto.getAppId(), domainService);
        request.setReturnUrl("https:" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + ordersDto.getId());
        request.setNotifyUrl("https:" + domainConfig.getTradeDomain() + "/taw/shouxin/orderNotify");
        request.setAppItemId(ordersDto.getAppItemId());

        Result<ShouxinpayResponseVO> data = paymentService.shouxinPay(request);
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }

    }

    /**
     * 解析建行订单的 posId
     *
     * @param order
     * @return
     */
    private String parseCcbPosId(OrdersDto order) {
        if (ccbConfig.isCardWinAppItemIds(order.getAppItemId())) {
            // 如果是建行定制的卡赢商品 直接走卡赢支付
            return SupplierEntityEnum.CARD_WINNER.getPosId();
        }
        if (Objects.equals(order.getType(), ItemDto.TypeCoupon)) {
            // 如果是优惠券商品  直接走兑吧支付
            return SupplierEntityEnum.Duiba.getPosId();
        }

        if (ccbConfig.getNoDebitPayAppItemIds().contains(order.getAppItemId())) {
            // 活动定制，仅支持信用卡支付
            return SupplierEntityEnum.ONLY_CREDIT.getPosId();
        }

        String[] configs = order.getBizParams().split("-");
        String supplierId = configs[1];
        SupplierEntityEnum supplier = SupplierEntityEnum.getByCode(Integer.valueOf(supplierId));
        if (supplier == SupplierEntityEnum.BIG_WHEEL) {
            // 如果是大转盘 则需要解析第三位的配置  999-卡赢  998-般豆
            String goodsId = configs[2];
            if (Objects.equals(goodsId, "998")) {
                return SupplierEntityEnum.BAN_DOU.getPosId();
            } else if (Objects.equals(goodsId, "999")) {
                return SupplierEntityEnum.CARD_WINNER.getPosId();
            } else {
                return null;
            }
        }
        return supplier.getPosId();
    }

    /**
     * mock 支付
     *
     * @param orderId
     * @return
     * @throws BizException
     */
    @RequestMapping(value = "/mockPay/charge")
    @ResponseBody
    public Result<MockWapChargeResponse> mockPay(@RequestParam Long orderId) throws BizException {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeMock, SubPaychannelType.MOCK);

        MockWapChargeRequest request = new MockWapChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        Result<MockWapChargeResponse> data = paymentService.mockPay(request);
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }
    }

    @GetMapping(value = "/duibaLiveMpPay/charge")
    @ResponseBody
    public Result<DuibaLiveMpPayResponseVO> duibaLiveMpPay(@RequestParam Long orderId) throws Exception {

        // 1.data
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        List<OrderItemDto> list = remoteOrderItemService.queryItemListByOderId(orderId);
        ConsumerExtraDto extraDto = remoteConsumerExtraService.findByConsumerId(consumerDto.getId()).getResult();
        // 2.commonCheck
        if (extraDto == null || StringUtils.isBlank(extraDto.getJson())) {
            return ResultBuilder.fail("用户openId不存在");
        }
        JSONObject jo = JSON.parseObject(extraDto.getJson());
        String openId = jo.getString(DuibaLiveConstant.OPEN_ID);
        String wechatAppId = jo.getString(DuibaLiveConstant.WECHAT_APP_ID);
        if (StringUtils.isBlank(openId)) {
            return ResultBuilder.fail("用户openId不存在");
        }

        // 3.request
        Result<DuibaLiveMpPayResponseVO> data;
        // 按照订单中商品属性是否供应商，确定支付实现
        OrdersDto ordersDto = remoteConsumerOrderSimpleService.findById(orderId, consumerDto.getId()).getResult();
        String extraInfo = ordersDto.getExtraInfo();
        JSONObject orderJo = JSON.parseObject(extraInfo);
        Boolean duibaLivesupplierItem;
        if (null == orderJo) {
            duibaLivesupplierItem = false;
        } else {
            duibaLivesupplierItem = orderJo.getBoolean(DuibaLiveOrderSupplierConstant.DUIBA_LIVE_SUPPLIER_ITEM);
        }
        // 3.1宝付支付实现
        if (duibaLivesupplierItem != null && duibaLivesupplierItem) {
            LOG.info("orderId:{},兑吧直播宝付支付", orderId);
            // 3.1.1c端用户 游客开户查询

            Boolean consumerPreRegister = remoteDuibaLiveSupplierBftService.consumerPreRegister(consumerDto.getId().toString());
            if (!consumerPreRegister) {
                return ResultBuilder.fail("游客注册失败");
            }

            // 3.1.2 支付
            // check
            checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                    AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.DUIBA_LIVE_BF_PAY);
            // 是否兑吧订单标识
            if (StringUtils.isBlank(extraInfo)) {
                return ResultBuilder.fail("非兑吧直播供应商订单");
            }
            // request
            DuibaLiveBFChargeRequest request = new DuibaLiveBFChargeRequest();
            request.setAppId(consumerDto.getAppId());
            // 区别与普通微信支付 bf 订单金额为商品售价
            request.setAmount(ordersDto.getConsumerPayPrice().intValue());
            request.setBizOrderNo(ordersDto.getOrderNum());
            request.setWechatAppId(wechatAppId);
            request.setConsumerId(consumerDto.getId());
            request.setOpenId(openId);
            request.setIp(RequestLocal.getIp());
            request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
            // 供应商客户账号
            request.setContractNo(orderJo.getString(DuibaLiveOrderSupplierConstant.CONTRACT_NO));
            // 供货价
            request.setSupplyPrice(orderJo.getInteger(DuibaLiveOrderSupplierConstant.SUPPLIER_PRICE));
            // 供应商id
            request.setSupplierId(orderJo.getLong(DuibaLiveOrderSupplierConstant.SUPPLIER_ID));
            // 商品id
            request.setItemId(ordersDto.getItemId());
            // 供应商商品id
            request.setSupplierItemId(orderJo.getLong(DuibaLiveOrderSupplierConstant.SUPPLIER_ITEM_ID));
            // 是否跨境商品
            request.setCrossBorder(orderJo.getInteger(DuibaLiveOrderSupplierConstant.CROSS_BORDER));
            data = paymentService.duibaLiveBFPay(request);

        } else {
            // 3.2兑吧直播小程序支付实现
            LOG.info("orderId:{},兑吧直播小程序支付", orderId);

            // check
            checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                    AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.DUIBA_LIVE_MP_PAY);
            // request
            DuibaLiveMpChargeRequest request = new DuibaLiveMpChargeRequest();
            request.setAppId(consumerDto.getAppId());
            request.setAmount(ordersDto.getConsumerPayPrice().intValue());
            request.setBizOrderNo(ordersDto.getOrderNum());
            request.setGoodsId(ordersDto.getAppItemId() + "");
            if (!CollectionUtils.isEmpty(list)) {
                Long appItemSkuId = list.get(0).getAppSkuId();
                AppItemSkuDto appItemSkuDto = remoteAppItemSkuService.findSkuByIdIncludeDeleted(appItemSkuId);
                if (appItemSkuDto != null && StringUtils.isNotBlank(appItemSkuDto.getMerchantCoding())) {
                    request.setMerchantCoding(appItemSkuDto.getMerchantCoding());
                }
            }
            request.setWechatAppId(wechatAppId);
            request.setUserId(LiveUtils.getPartnerUserId(consumerDto.getPartnerUserId()));
            request.setOpenId(openId);
            request.setIp(RequestLocal.getIp());
            request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
            data = paymentService.duibaLiveMpPay(request);
        }
        // 4.result
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }
    }

    @GetMapping(value = "/duibaLiveInstallmentPay/charge")
    @ResponseBody
    public Result<DuibaLiveInstallmentPayResponseVO> duibaLiveInstallmentPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        // 1. 校验杭州邮储银行的appId，走自己的账户
        SubPaychannelType subPaychannelType = getInstallmentSubPayChannelType(consumerDto.getAppId());

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, subPaychannelType);

        DuibaLiveInstallmentChargeRequest request = new DuibaLiveInstallmentChargeRequest();
        request.setAppId(consumerDto.getAppId());
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setIp(RequestLocal.getIp());
        request.setGoodsDetail(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        installmentCustom(request, consumerDto.getId());
        Result<DuibaLiveInstallmentPayResponseVO> data = paymentService.duibaLiveInstallmentPay(request);
        if (data.getSuccess()) {
            return ResultBuilder.success(data.getData());
        } else {
            return ResultBuilder.fail(data.getDesc());
        }
    }

    private void installmentCustom(DuibaLiveInstallmentChargeRequest request, Long consumerId) {
        try {
            // 江苏银行定制
            if (jsBankConfig.isApp(request.getAppId())) {
                DubboResult<ConsumerExtraDto> dubboResult = remoteConsumerExtraService.findByConsumerId(consumerId);
                if (dubboResult.isSuccess() && dubboResult.getResult() != null) {
                    ConsumerExtraDto consumerExtraDto = dubboResult.getResult();
                    JSONObject extraInfo = JSON.parseObject(consumerExtraDto.getJson());
                    if (extraInfo == null || !extraInfo.containsKey("phoneNum") || StringUtils.isBlank(extraInfo.getString("phoneNum"))) {
                        LOG.warn("江苏银行-用户免登无phoneNo, cid={}", consumerId);
                        return;
                    }
                    request.setExtraInfo(extraInfo.getString("phoneNum"));
                }
            }
        } catch (Exception e) {
            LOG.warn("江苏银行分期支付定制异常", e);
        }
    }

    /**
     * 分期支付，根据appId获取SubPayChannelType
     *
     * @param appId
     * @return
     */
    private SubPaychannelType getInstallmentSubPayChannelType(Long appId) {
        if (MapUtils.isEmpty(installmentSubPayChannelConfig.getAppPayChannel()) || !installmentSubPayChannelConfig.getAppPayChannel().containsKey(appId)) {
            return SubPaychannelType.DUIBA_LIVE_INSTALLMENT_PAY;
        }
        Integer channelType = installmentSubPayChannelConfig.getAppPayChannel().get(appId);
        SubPaychannelType subPaychannelType = SubPaychannelType.getEnum(channelType);
        LOG.info("兑吧直播分期接口, appId[{}]使用渠道类型[{}]", appId, subPaychannelType.getCode());
        return subPaychannelType;
    }


    /**
     * 宁波银行预支付
     *
     * @param orderId 订单id
     * @return 预支付返回
     */
    @RequestMapping(value = "/nbcb/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<?> ningboCharge(@RequestParam Long orderId) {
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeNingbo, SubPaychannelType.NINGBO_BANK_PAY);
        try {
            NbcbChargeResponseDto response = paymentService.ningboCreateCharge(buildNbcbChargeRequest(ordersDto));

            if (response.isSuccess()) {
                return ResultBuilder.success(response.getPayGateWayUrl());
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("[宁波银行] 支付下单失败", e);
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e1) {
            LOG.error("[宁波银行] 支付下单失败", e1);
            return ResultBuilder.fail("网络异常，请稍后再试");
        }

    }


    /**
     * 拼装宁波银行支付请求参数
     *
     * @param ordersDto 主订单
     * @return 请求参数
     */
    private NbcbChargeRequestDto buildNbcbChargeRequest(OrdersDto ordersDto) throws UnsupportedEncodingException {

        NbcbChargeRequestDto chargeRequestDto = new NbcbChargeRequestDto();

        chargeRequestDto.setAppId(RequestLocal.getConsumerDO().getAppId());
        chargeRequestDto.setAmount(ordersDto.getConsumerPayPrice().intValue());
        chargeRequestDto.setBizOrderNo(ordersDto.getOrderNum());
        chargeRequestDto.setOrderDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        chargeRequestDto.setIp(RequestLocal.getIp());
        chargeRequestDto.setPartnerUserId(RequestLocal.getConsumerDO().getPartnerUserId());
        chargeRequestDto.setMerDate(ordersDto.getGmtCreate());

        chargeRequestDto.setPayFrontUrl(getNbcbUrl(ordersDto));
        chargeRequestDto.setNotifyUrl(ningboBankPayConfig.getNotifyUrl());
        chargeRequestDto.setPaySuccessUrl(getNbcbUrl(ordersDto));

        LOG.info("[宁波银行] 预支付请求参数 chargeRequestDto = {}", JSON.toJSONString(chargeRequestDto));
        return chargeRequestDto;
    }

    private String getNbcbUrl(OrdersDto ordersDto) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        Long cid = RequestLocal.getCid();

        ConsumerDto consumerDto = remoteConsumerService.find(cid);

        String uid = RequestLocal.getPartnerUserId();
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("uid", uid);
        paramMap.put("credits", consumerDto.getCredits() + "");
        paramMap.put("appKey", app.getAppKey());
        paramMap.put("timestamp", DateUtils.daysAddOrSub(new Date(), 1).getTime() + "");
        paramMap.put("appSecret", app.getAppSecret());
        String sign = SignTool.sign(paramMap);
        paramMap.remove("appSecret");
        paramMap.put("sign", sign);
        paramMap.put("orderId", String.valueOf(ordersDto.getId()));

        String loginUrl = assembleUrl(ningboBankPayConfig.getPaySuccessJump(), paramMap);

        LOG.info("宁波银行 支付回调地址 loginUrl={}", loginUrl);
        return loginUrl;
    }

    private String assembleUrl(String url, Map<String, String> params) {
        if (!url.contains("?")) {
            url = url + "?";
        } else {
            url = url + "&";
        }
        for (String key : params.keySet()) {
            try {
                if (params.get(key) != null && params.get(key).length() != 0) {
                    url = url + key + "=" + URLEncoder.encode(params.get(key), "utf-8") + "&";
                } else {
                    url = url + key + "=" + params.get(key) + "&";
                }
            } catch (UnsupportedEncodingException var5) {
                var5.printStackTrace();
            }
        }
        return url;
    }


    /**
     * 工商银行预支付
     *
     * @param orderId 订单id
     * @return 预支付返回
     */
    @RequestMapping(value = "/icbc/credits/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<?> icbcCreditsCharge(@RequestParam Long orderId,
                                       @RequestParam Long cardId) {
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.ICBC_CREDITS);

        IcbcUserCardDto icbcUserCardDto = remoteIcbcUserCardService.findUserCardById(cardId);
        try {
            IcbcCreditsChargeResp response = paymentService.icbcCreditsCreateCharge(buildIcbcChargeRequest(ordersDto, icbcUserCardDto));

            if (response.isSuccess()) {
                return ResultBuilder.success(response.getH5Url());
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("[icbc] 工商银行预支付下单失败", e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    /**
     * 生成预支付请求体
     *
     * @param ordersDto 主订单
     * @return 预支付请求
     */
    private IcbcCreditsChargeRequest buildIcbcChargeRequest(OrdersDto ordersDto, IcbcUserCardDto icbcUserCardDto) {
        IcbcCreditsChargeRequest chargeRequest = new IcbcCreditsChargeRequest();

        chargeRequest.setAppId(RequestLocal.getAppId());
        chargeRequest.setAmount(ordersDto.getConsumerPayPrice().intValue());
        chargeRequest.setBizOrderNo(ordersDto.getOrderNum());
        chargeRequest.setBizOrderBrief(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));

        if (Objects.nonNull(ordersDto.getAppItemId())) {
            chargeRequest.setAppItemId(ordersDto.getAppItemId());
        }

        chargeRequest.setClientIp(RequestLocal.getIp());
        // 银行卡号
        chargeRequest.setAccountNo(icbcUserCardDto.getCardNumber());
        // 银行卡用户名
        chargeRequest.setCustName(icbcUserCardDto.getOwnerName());
        // 用户手机号
        chargeRequest.setMobileno(icbcUserCardDto.getMobileNo());
        chargeRequest.setChargeDate(new Date());
        // 行方支付回调地址
        chargeRequest.setNotifyUrl(icbcCreditsConfig.getNotifyUrl());


        return chargeRequest;
    }

    /**
     * 工行e支付（app端）
     *
     * @param orderId 订单id
     * @return postForm
     */
    @RequestMapping(value = "/icbcElife4App/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> icbcElife4AppCharge(@RequestParam Long orderId) {

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.ICBC_ELIFE_PAY_APP);
        try {
            IcbcElife4AppChargeResponse response = paymentService.icbcElife4AppCharge(ordersDto);

            if (response.isSuccess()) {
                return ResultBuilder.success(response.getPostForm());
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("工行e生活支付 app端 支付下单失败 orderId={}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    /**
     * 工行e支付（微信端）
     *
     * @param orderId 订单id
     * @return qrCode
     */
    @RequestMapping(value = "/icbcElife4Wx/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> icbcElife4WxCharge(@RequestParam Long orderId) {

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf, SubPaychannelType.ICBC_ELIFE_PAY_WX);
        try {
            IcbcElife4WxChargeResponse response = paymentService.icbcElife4WxCharge(ordersDto);

            if (response.isSuccess()) {
                return ResultBuilder.success(response.getQrcode());
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("工行e生活支付 微信端 支付下单失败 orderId={}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    /**
     * 厦门国际银行
     *
     * @param orderId 订单id
     * @return qrCode
     */
    @RequestMapping(value = "/xib/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> xibCharge(@RequestParam Long orderId) {

        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeXibPay, SubPaychannelType.XIB_PAY);
        try {
            XibChargeResponseDTO response = paymentService.xibCharge(ordersDto);

            if (response.isSuccess()) {
                return ResultBuilder.success(response.getPayUrl());
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("厦门银行支付 支付下单失败 orderId={}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        }
    }


    /**
     * 兴业银行预支付
     *
     * @param orderId 订单id
     * @return 预支付返回
     */
    @RequestMapping(value = "/cib/wxPayLite/charge", method = RequestMethod.GET)
    @ResponseBody
    public Result<WxPayLiteResponseVO> cibCharge(@RequestParam Long orderId) {
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, RequestLocal.getConsumerDO().getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeCibPay, SubPaychannelType.CIB_PAY_WX);
        try {
            CibPayWxChargeResponseDTO response = paymentService.cibCreateCharge(ordersDto);
            LOG.info("兴业银行 接口返回:{}", JSON.toJSONString(response));
            if (response.isSuccess()) {
                JSONObject jsonObject = JSON.parseObject(response.getPayInfo());
                WxPayLiteResponseVO wxPayLiteResponseVO = JSON.parseObject(response.getPayInfo(), WxPayLiteResponseVO.class);
                wxPayLiteResponseVO.setPrepayId(jsonObject.getString("package"));
                return ResultBuilder.success(wxPayLiteResponseVO);
            } else {
                return ResultBuilder.fail(response.getMessage());
            }
        } catch (BizException e) {
            LOG.error("[兴业银行] 支付下单失败", e);
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e1) {
            LOG.error("[兴业银行] 支付下单失败", e1);
            return ResultBuilder.fail("网络异常，请稍后再试");
        }

    }

    /**
     * 微博支付
     *
     * @param orderId 订单id
     * @param subject 商品名称
     * @return 微博支付参数
     */
    @RequestMapping(value = "/createWeiboPay", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> createWeiboPay(@RequestParam Long orderId, @RequestParam String subject) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeWeibo, SubPaychannelType.WEIBO_PAY);

        try {
            String payInfo = paymentService.weiboCharge(ordersDto, subject);
            return ResultBuilder.success(payInfo);
        } catch (BizException e) {
            LOG.error("微博支付 业务异常 orderId={}", orderId, e);
            return ResultBuilder.fail(e.getMessage());
        } catch (Exception e) {
            LOG.error("微博支付 异常 orderId={}", orderId, e);
            return ResultBuilder.fail("网络异常，请稍后再试!");
        }
    }

    /**
     * 零食很忙支付
     * @param orderId 订单id
     * @return 微博支付参数
     */
    @RequestMapping(value = "/createLshmPay", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> createLshmPay(@RequestParam Long orderId) {
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        OrdersDto ordersDto = checkOrderAndGetOrdersDto(orderId, consumerDto.getId(),
                AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeLshm, SubPaychannelType.LSHM_PAY);

        try {
            String payInfo = paymentService.lshmCharge(ordersDto);
            return ResultBuilder.success(payInfo);
        }catch (BizException e){
            LOG.error("零食很忙支付 业务异常 orderId={}",orderId,e);
            return ResultBuilder.fail(e.getMessage());
        }catch (Exception e){
            LOG.error("零食很忙支付 异常 orderId={}",orderId,e);
            return ResultBuilder.fail("网络异常，请稍后再试!");
        }
    }
}
