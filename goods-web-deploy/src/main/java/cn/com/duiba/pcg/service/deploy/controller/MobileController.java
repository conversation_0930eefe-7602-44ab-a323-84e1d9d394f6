package cn.com.duiba.pcg.service.deploy.controller;

import cn.com.duiba.activity.center.api.dto.equity.EquityConfigDto;
import cn.com.duiba.activity.center.api.dto.equity.StationStockDto;
import cn.com.duiba.activity.center.api.enums.equity.EquityValidateEnum;
import cn.com.duiba.activity.center.api.enums.label.ConsumerLabelType;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityConfigService;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteStationStockService;
import cn.com.duiba.activity.center.api.remoteservice.label.RemoteConsumerLabelService;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParam;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParamBuilder;
import cn.com.duiba.activity.center.api.remoteservice.label.result.LabelPassResult;
import cn.com.duiba.activity.center.api.request.equity.StationStockFindRequest;
import cn.com.duiba.activity.custom.center.api.remoteservice.hsbc.RemoteHsbcAppItemTagService;
import cn.com.duiba.api.bo.KeyValueEntity;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.dto.MJJingLiVipDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteMJJingLiVipService;
import cn.com.duiba.credits.sdk.AssembleTool;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.dcommons.enums.AppItemSourceTypeEnum;
import cn.com.duiba.dcommons.enums.GoodsTypeEnum;
import cn.com.duiba.developer.center.api.domain.dto.AppBudgetDto;
import cn.com.duiba.developer.center.api.domain.dto.AppBulletinDto;
import cn.com.duiba.developer.center.api.domain.dto.AppNewExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DeveloperDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.domain.dto.RemainingMoneyDto;
import cn.com.duiba.developer.center.api.domain.dto.app.AppExtraLargeFieldDto;
import cn.com.duiba.developer.center.api.domain.dto.appextra.DuibaShareDto;
import cn.com.duiba.developer.center.api.domain.param.authority.VersionResourceDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppBulletinService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppExtraService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppNewExtraService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteDeveloperService;
import cn.com.duiba.developer.center.api.remoteservice.app.RemoteAppExtraLargeFieldSevice;
import cn.com.duiba.developer.center.api.remoteservice.authority.RemoteResourcesService;
import cn.com.duiba.developer.center.api.remoteservice.saas.RemoteSaasGrantService;
import cn.com.duiba.developer.center.api.utils.ReplaceCdnUtil;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.developer.center.api.utils.WhiteListUniqueKeyConstants;
import cn.com.duiba.goods.center.api.remoteservice.RemoteGoodsAppItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.constant.AppItemNewExtraPropKeyConstant;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsBatchDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemBaseDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemClassifyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDescConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.CreditsSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.ItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.RedefineAttributeDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.RedefineAttributeValueDto;
import cn.com.duiba.goods.center.api.remoteservice.enums.ExchangeTypeEnum;
import cn.com.duiba.goods.center.api.remoteservice.enums.ItemClassifyTypeEnum;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemClassifyBackendService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemNewExtraService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemotePreStockService;
import cn.com.duiba.goods.center.api.remoteservice.marketing.RemoteMarketingItemCreditsSkuService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.goods.center.api.remoteservice.tool.ExtraInfoUtils;
import cn.com.duiba.goods.center.api.remoteservice.tool.ItemKeyUtils;
import cn.com.duiba.goods.center.api.remoteservice.tool.MarketingSkuUtils;
import cn.com.duiba.live.supplier.center.api.dto.DuibaLiveSupplierDto;
import cn.com.duiba.live.supplier.center.api.dto.DuibaLiveSupplierGoodsDto;
import cn.com.duiba.live.supplier.center.api.enums.supplier.DuibaLiveSupplierTypeEnum;
import cn.com.duiba.live.supplier.center.api.remoteservice.RemoteDuibaLiveSupplierGoodsService;
import cn.com.duiba.live.supplier.center.api.remoteservice.RemoteDuibaLiveSupplierService;
import cn.com.duiba.mall.center.api.domain.dto.fulcredits.FulcreditsActConfDto;
import cn.com.duiba.mall.center.api.domain.dto.fulcredits.FulcreditsActOptionsDto;
import cn.com.duiba.mall.center.api.domain.dto.groupbuy.GroupBuyActConfDto;
import cn.com.duiba.mall.center.api.domain.dto.groupbuy.GroupBuyDetailDto;
import cn.com.duiba.mall.center.api.domain.dto.groupbuy.GroupBuyOrderDto;
import cn.com.duiba.mall.center.api.domain.dto.shop.AppItemActivityRelationDto;
import cn.com.duiba.mall.center.api.domain.dto.shop.MarketActivityDto;
import cn.com.duiba.mall.center.api.domain.dto.vipgoods.VipGoodsConfigDto;
import cn.com.duiba.mall.center.api.domain.enums.groupbuy.GroupBuyDetailStatusEnum;
import cn.com.duiba.mall.center.api.domain.enums.groupbuy.GroupBuyGroupStatusEnum;
import cn.com.duiba.mall.center.api.domain.enums.groupbuy.GroupBuyOrderStatusEnum;
import cn.com.duiba.mall.center.api.domain.paramquary.groupbuy.GroupBuyOrderQueryParam;
import cn.com.duiba.mall.center.api.remoteservice.groupbuy.RemoteGroupBuyActConfService;
import cn.com.duiba.mall.center.api.remoteservice.groupbuy.RemoteGroupBuyDetailService;
import cn.com.duiba.mall.center.api.remoteservice.groupbuy.RemoteGroupBuyOrderService;
import cn.com.duiba.mall.center.api.remoteservice.shop.RemoteAppItemActivityRelationService;
import cn.com.duiba.mall.center.api.remoteservice.shop.RemoteMarketActivityService;
import cn.com.duiba.mall.center.api.remoteservice.vipgoods.RemoteVipGoodsConfigService;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.dto.TakeOrderQueueRecordDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.pcg.constant.AppIdConstant;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.exception.StatusException;
import cn.com.duiba.pcg.service.biz.config.BeiJinHyundaiConfig;
import cn.com.duiba.pcg.service.biz.config.CcbConfig;
import cn.com.duiba.pcg.service.biz.config.CommonConfig;
import cn.com.duiba.pcg.service.biz.config.EquiltWhiltListAppConfig;
import cn.com.duiba.pcg.service.biz.config.GpsConfig;
import cn.com.duiba.pcg.service.biz.config.HidilaoMembershipConfig;
import cn.com.duiba.pcg.service.biz.config.LaiYiFenConfig;
import cn.com.duiba.pcg.service.biz.config.LshmConfig;
import cn.com.duiba.pcg.service.biz.config.MJConfig;
import cn.com.duiba.pcg.service.biz.config.NewShoppingCartConfig;
import cn.com.duiba.pcg.service.biz.config.UnitPayFlagConfig;
import cn.com.duiba.pcg.service.biz.config.WandaConfig;
import cn.com.duiba.pcg.service.biz.config.WisdomBudsConfig;
import cn.com.duiba.pcg.service.biz.config.YangShiPinConfig;
import cn.com.duiba.pcg.service.biz.config.wey.WeyOwnerConfig;
import cn.com.duiba.pcg.service.biz.constants.WilteListConstants;
import cn.com.duiba.pcg.service.biz.customize.VirtualInputAccountBo;
import cn.com.duiba.pcg.service.biz.customize.YingYongBaoBo;
import cn.com.duiba.pcg.service.biz.dto.CustomExchangeVirtualWhite;
import cn.com.duiba.pcg.service.biz.dto.FreeShippingDto;
import cn.com.duiba.pcg.service.biz.dto.SupplierFullMallItemDto;
import cn.com.duiba.pcg.service.biz.dto.yinge.YingeMessage;
import cn.com.duiba.pcg.service.biz.enums.OrderSourceTypeEnum;
import cn.com.duiba.pcg.service.biz.enums.OrderUriConstant;
import cn.com.duiba.pcg.service.biz.handler.exchangecheck.ExchangeCheckRegistry;
import cn.com.duiba.pcg.service.biz.log.StatDetailAccessLog;
import cn.com.duiba.pcg.service.biz.manager.InnerPageManager;
import cn.com.duiba.pcg.service.biz.param.CustomExchangeParam;
import cn.com.duiba.pcg.service.biz.processor.extrange.ExtrangeBaseProcessor;
import cn.com.duiba.pcg.service.biz.service.ActConfService;
import cn.com.duiba.pcg.service.biz.service.AppRequestService;
import cn.com.duiba.pcg.service.biz.service.AppSimpleQueryService;
import cn.com.duiba.pcg.service.biz.service.ConsumerExchangeLimitService;
import cn.com.duiba.pcg.service.biz.service.ConsumerItemRenderService;
import cn.com.duiba.pcg.service.biz.service.CouponService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemActualPriceCalculateService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStatusService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStatusService.StatusTypeEnum;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStockService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemTimeLimitService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemVipLimitService;
import cn.com.duiba.pcg.service.biz.service.HaidilaoService;
import cn.com.duiba.pcg.service.biz.service.HuaXiZiService;
import cn.com.duiba.pcg.service.biz.service.ItemViewService;
import cn.com.duiba.pcg.service.biz.service.LaiYiFenService;
import cn.com.duiba.pcg.service.biz.service.LevelCustomService;
import cn.com.duiba.pcg.service.biz.service.ShareContentService;
import cn.com.duiba.pcg.service.biz.service.TakeOrderQueueService;
import cn.com.duiba.pcg.service.biz.service.UnitNameCustomService;
import cn.com.duiba.pcg.service.biz.service.VipGoodsService;
import cn.com.duiba.pcg.service.biz.service.YangshiPinService;
import cn.com.duiba.pcg.service.biz.service.amb.impl.AmbExpressTemplateService;
import cn.com.duiba.pcg.service.biz.service.custom.wanda.WandaService;
import cn.com.duiba.pcg.service.biz.service.fulcredits.FulcreditsService;
import cn.com.duiba.pcg.service.biz.service.hsbc.HsbcService;
import cn.com.duiba.pcg.service.biz.service.impl.ActConfServiceImpl;
import cn.com.duiba.pcg.service.biz.service.impl.PlatFormCouponServiceImpl;
import cn.com.duiba.pcg.service.biz.service.shoppingTrallery.ShoppingTralleryService;
import cn.com.duiba.pcg.service.biz.service.shoppingTrallery.SupplierFullMallService;
import cn.com.duiba.pcg.service.biz.util.Conditions;
import cn.com.duiba.pcg.service.biz.util.CrossDomainHelper;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.util.EmdCommonUtil;
import cn.com.duiba.pcg.service.biz.util.Environment;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.util.ShareLandUrlUtils;
import cn.com.duiba.pcg.service.biz.util.UrlUtils;
import cn.com.duiba.pcg.service.biz.vo.AppVO;
import cn.com.duiba.pcg.service.biz.vo.CItemVO;
import cn.com.duiba.pcg.service.biz.vo.CreditsSkuVO;
import cn.com.duiba.pcg.service.biz.vo.ExchangeButtonControlInfoVO;
import cn.com.duiba.pcg.service.biz.vo.ItemKeyVO;
import cn.com.duiba.pcg.service.biz.vo.ItemShareInfoVO;
import cn.com.duiba.pcg.service.biz.vo.Seckill.SecKillActConfVO;
import cn.com.duiba.pcg.service.biz.vo.UnitAuthorityVO;
import cn.com.duiba.pcg.service.biz.vo.VipLimitViewInfoVO;
import cn.com.duiba.pcg.service.biz.vo.fulcredits.BonusActVO;
import cn.com.duiba.pcg.service.biz.vo.fulcredits.BonusItemVO;
import cn.com.duiba.pcg.service.biz.vo.groupbuy.GroupBuyActInfoVO;
import cn.com.duiba.pcg.service.common.DateUtil;
import cn.com.duiba.pcg.tool.JsonRender;
import cn.com.duiba.pcg.tool.Message;
import cn.com.duiba.supplier.center.api.enums.SupplyTypeEnum;
import cn.com.duiba.supplier.center.api.enums.SupplyTypeEnumUtil;
import cn.com.duiba.thirdparty.api.hsbc.RemoteHsbcBankServcie;
import cn.com.duiba.thirdparty.enums.virtual.VirtualItemChannelEnum;
import cn.com.duiba.thirdpartyvnew.api.dcjj.RemoteDcjjService;
import cn.com.duiba.thirdpartyvnew.dto.dcjj.DcjjPreCheckParam;
import cn.com.duiba.thirdpartyvnew.dto.dcjj.DcjjResult;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duiba.wolf.utils.NumberUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.cat.util.Pair;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.base.Preconditions;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by yansen on 16/12/21.
 */
@Controller
@RequestMapping("/mobile")
@Api(tags = "Mobile")
public class MobileController {

    /**
     * 应用积分文案定制展示
     */
    private static final String APP_CREDITS_SHOW = "YYJFWADZZS_liuzhihong";

    /**
     * 大成基金本地化应用白名单
     */
    private static final String DCJJ_WHITE = "DCJJBDHYY_liuzhihong";
    private static Logger log = LoggerFactory.getLogger(MobileController.class);

    private static final String HUIFENG_ACCESS_KEY = "HBGJCZSP_wangyongxia";


    /**
     * 关闭商品评论
     */
    private static final String CLOSE_ITEM_COMMENT = "CDGBSPPLRK_liukai";

    /**
     * 开发者虚拟商品自定义金额业务白名单
     */
    public static final String CUSTOM_EXCHANGE_VIRTUAL_WHITE = "KFZZYXNSPZCZDYJE_liuzhihong";
    /**
     * 零食很忙，商品关系映射
     */
    public static final String LSHM_GOODS_RELATION_WHITE = "LSHMSPGXYS_liuzhihong";

    @Autowired
    private GoodsItemDataService goodsItemDataService;
    @Autowired
    private ConsumerItemRenderService consumerItemRenderService;
    @Autowired
    private GoodsItemVipLimitService goodsItemVipLimitService;
    @Autowired
    private ItemViewService itemViewService;
    @Autowired
    private ShareContentService shareContentService;
    @Autowired
    private RemoteDeveloperService remoteDeveloperService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private RemotePreStockService remotePreStockService;
    @Autowired
    private GoodsItemTimeLimitService goodsItemTimeLimitService;
    @Autowired
    private AmbExpressTemplateService ambExpressTemplateService;
    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;
    @Autowired
    private TakeOrderQueueService takeOrderQueueService;
    @Autowired
    private AppRequestService appRequestService;
    @Autowired
    private GoodsItemActualPriceCalculateService goodsItemActualPriceCalculateService;
    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    private RemoteAppExtraService remoteAppExtraServiceNew;
    @Autowired
    private ConsumerExchangeLimitService consumerExchangeLimitService;
    @Resource
    private RemoteDcjjService remoteDcjjService;
    @Autowired
    private RemoteGoodsAppItemExtraService remoteGoodsAppItemExtraService;
    @Autowired
    private AppSimpleQueryService appSimpleQueryService;
    @Autowired
    private RemoteAppBulletinService remoteAppBulletinService;
    @Autowired
    private GoodsItemStockService goodsItemStockService;
    @Autowired
    private RemoteAppExtraLargeFieldSevice remoteAppExtraLargeFieldSevice;
    @Autowired
    private DomainService domainService;
    @Resource
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private RemoteAppNewExtraService remoteAppNewExtraService;
    @Autowired
    private RemoteAppItemSkuService remoteAppItemSkuService;
    @Autowired
    private RemoteAppItemActivityRelationService remoteAppItemActivityRelationService;
    @Autowired
    private RemoteMarketActivityService remoteMarketActivityService;
    @Autowired
    private RemoteSaasGrantService remoteSaasGrantService;
    @Autowired
    private ActConfService actConfService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private HaidilaoService haidilaoService;
    @Autowired
    private VipGoodsService vipGoodsService;
    @Autowired
    private ShoppingTralleryService shoppingTralleryService;
    @Autowired
    private SupplierFullMallService supplierFullMallService;
    @Autowired
    private EquiltWhiltListAppConfig equiltWhiltListAppConfig;
    @Autowired
    private MJConfig mjConfig;
    @Autowired
    private RemoteMJJingLiVipService remoteMJJingLiVipService;
    @Autowired
    private RemoteStationStockService remoteStationStockService;
    /**
     * 海底捞会员日活动配置
     */
    @Autowired
    private HidilaoMembershipConfig hidilaoMembershipConfig;
    @Resource
    private CcbConfig config;
    @Autowired
    private InnerPageManager innerPageManager;
    @Autowired
    private RemoteEquityConfigService remoteEquityConfigService;
    @Autowired
    private RemoteConsumerLabelService remoteConsumerLabelService;
    @Autowired
    private UnitNameCustomService unitNameCustomService;
    @Autowired
    private LevelCustomService levelCustomService;
    @Autowired
    private FulcreditsService fulcreditsService;
    @Resource
    private RemoteVipGoodsConfigService remoteVipGoodsConfigService;
    @Resource
    private WandaConfig wandaConfig;
    @Autowired
    private GpsConfig gpsConfig;
    @Autowired
    private RemoteGroupBuyActConfService remoteGroupBuyActConfService;
    @Autowired
    private RemoteGroupBuyOrderService remoteGroupBuyOrderService;
    @Autowired
    private RemoteGroupBuyDetailService remoteGroupBuyDetailService;
    @Autowired
    private RemoteResourcesService remoteResourcesService;

    @Autowired
    private RemoteItemClassifyBackendService remoteItemClassifyBackendService;

    @Autowired
    private RemoteDuibaLiveSupplierService remoteDuibaLiveSupplierService;

    @Autowired
    private RemoteDuibaLiveSupplierGoodsService remoteDuibaLiveSupplierGoodsService;

    @Autowired
    private UnitPayFlagConfig unitPayFlagConfig;

    @Autowired
    private YangShiPinConfig yangShiPinConfig;

    @Autowired
    private YangshiPinService yangshiPinService;

    @Autowired
    private LaiYiFenConfig laiYiFenConfig;

    @Autowired
    private LaiYiFenService laiYiFenService;

    @Autowired
    private NewShoppingCartConfig newShoppingCartConfig;
    @Autowired
    private BeiJinHyundaiConfig beiJinHyundaiConfig;
    @Autowired
    private RemoteItemNewExtraService remoteItemNewExtraService;
    @Autowired
    private RemoteMarketingItemCreditsSkuService remoteMarketingItemCreditsSkuService;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;


    @Autowired
    private RemoteHsbcBankServcie remoteHsbcBankServcie;

    @Autowired
    private RemoteHsbcAppItemTagService remoteHsbcAppItemTagService;


    @Autowired
    private ExchangeCheckRegistry exchangeCheckRegistry;
    @Autowired
    private WisdomBudsConfig wisdomBudsConfig;
    @Autowired
    private WeyOwnerConfig weyOwnerConfig;
    @Autowired
    private HsbcService hsbcService;
    @Autowired
    private HuaXiZiService huaXiZiService;

    @Resource
    private LshmConfig lshmConfig;

    /**
     * 已兑、剩余库存展示
     */
    private static final String EXCHANGE_REDEEMED_SHOW = "DHSYSLXSKZ_wangyongxia";

    private static final com.github.benmanes.caffeine.cache.Cache<Long, List<VersionResourceDto>> CACHE_APP_VERSION = Caffeine.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .initialCapacity(100)
            .maximumSize(500)
            .build();

    private static final String ERROR = "error";
    private static final String SUCCESS = "success";
    private static final String MESSAGE = "message";
    private static final String HAS_ACCOUNT = "hasAccount";
    private static final String STATUS = "status";
    private static final String QQMARKET_DOWNLOAD = "download";
    private static final String QQMARKET_PACKAGENAME = "packageName";
    private static final String QQMARKET_APPID = "appId";
    private static final String QQMARKET_SCENE = "scene";
    private static final String QQMARKET_SLOT_ID = "slotId";


    private Cache<Long, String> appShearProgramCache = CacheBuilder.newBuilder().maximumSize(500).expireAfterWrite(60, TimeUnit.SECONDS).build();

    private Cache<Long, String> appConfigerPropNameCache = CacheBuilder.newBuilder().maximumSize(100).expireAfterWrite(5, TimeUnit.SECONDS).build();

    private static final String FUJIAN_WHITE_KEY = "FJZHDZ_zouweixiang";
    private static final String FUJIAN_ICON = "SZRMBHB_";
    @Autowired
    private WandaService wandaService;

    /**
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/detail")
    @ApiOperation(value = "兑吧商品展示", notes = "兑吧商品展示", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "appItemId", value = "appItemId", dataType = "Long", required = false, paramType = "query"),
            @ApiImplicitParam(name = "itemId", value = "itemId", dataType = "Long", required = false, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "type", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "vs", value = "vs", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "accountSource", value = "accountSource", dataType = "String", paramType = "query"),

    })
    public ModelAndView detail(HttpServletRequest request, HttpServletResponse response) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        Preconditions.checkNotNull(app, "app获取失败");
        app = unitNameCustomService.customUnitName(app);
        consumer = levelCustomService.customLevel(app, consumer);

        //新的链接参数
        String appItemId = request.getParameter("appItemId");
        //兼容老的链接参数
        String itemId = request.getParameter("itemId");
        ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemId, itemId, app);

        boolean isPreview = StringUtils.equals("preview", request.getParameter("type"));
        if (!isPreview) {
            itemCheck(itemKeyDto);
        }

        setAttribute(request, itemKeyDto);


        catInfo(itemKeyDto);

        // 商品UV,PV统计
        if (!StringUtils.equals(request.getParameter("_duibaPerf"), "1")) {
            StatDetailAccessLog.addExtPair(itemKeyDto, app, consumer, 0);
        }
        DeveloperDto developer = remoteDeveloperService.getDeveloperById(app.getDeveloperId()).getResult();
        response.setHeader("Cache-Control", "no-store,no-cache,must-revalidate");

        if (ItemDto.TypePhonebillDingzhi.equals(itemKeyDto.getItemDtoType())
                || ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType())
                || ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())
                || (ItemDto.TypeVirtual.equals(itemKeyDto.getItemDtoType()) && itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeLimitVPro))
        ) {
            ModelAndView modelAndView = couponObjectDetail(request, consumer, itemKeyDto, app, developer, response);
            String viewName = modelAndView.getViewName();
            if (!viewName.startsWith("/")) {
                viewName = "/" + viewName;
            }
            return innerPageManager.handleInnerPageModelView(modelAndView, viewName);
        } else if (ItemDto.TypeAlipay.equals(itemKeyDto.getItemDtoType())
                || ItemDto.TypePhonebill.equals(itemKeyDto.getItemDtoType())
                || ItemDto.TypePhoneflow.equals(itemKeyDto.getItemDtoType())
                || ItemDto.TypeQB.equals(itemKeyDto.getItemDtoType())
                || ItemDto.AplipaySku.equals(itemKeyDto.getItemDtoType())

        ) {
            ModelAndView modelAndView = zhichongDetail(request, consumer, itemKeyDto, app, developer);
            String viewName = modelAndView.getViewName();
            if (!viewName.startsWith("/")) {
                viewName = "/" + viewName;
            }
            return innerPageManager.handleInnerPageModelView(modelAndView, viewName);
        }
        throw new GoodsWebException("其他类型暂不支持");
    }

    private void setAttribute(HttpServletRequest request, ItemKeyDto itemKeyDto) {
        try {
            Optional.ofNullable(itemKeyDto)
                    .map(ItemKeyDto::getAppItem)
                    .map(AppItemDto::getId)
                    .ifPresent(appItemId -> request.setAttribute("appItemId", appItemId));
        } catch (Exception ignored) {

        }
    }

    @GetMapping("/previewDetail")
    public ModelAndView previewDetail(@RequestParam("itemType") String itemType, HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        String view;
        if (ItemDto.TypeCoupon.equals(itemType)) {
            view = "consumer/mobile/refactor/coupondetail";
        } else if (ItemDto.TypeObject.equals(itemType)) {
            view = "consumer/mobile/refactor/objectdetail";
        } else if (ItemDto.TYPE_CRYPTO_CARD.equals(itemType)) {
            view = "consumer/mobile/refactor/carddetail";
        } else {
            view = ERROR;
        }
        modelAndView.addObject("data", JSONArray.toJSONString(Maps.newHashMap()));
        modelAndView.setViewName(view);

        String viewName = modelAndView.getViewName();
        if (!viewName.startsWith("/")) {
            viewName = "/" + viewName;
        }
        return innerPageManager.handleInnerPageModelView(modelAndView, viewName);
    }

    public void catInfo(ItemKeyDto itemKeyDto) {
        try {
            if (ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsObjectDetail");
            } else if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsCouponDetail");
            } else if (isZhichong(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsZhichongDetail");
            }
        } catch (Exception e) {
            log.error("cat error", e);
        }
    }

    /**
     * 校验是否是智能推荐商品或者活动商品
     *
     * @param itemKeyDto
     */
    private void itemCheck(ItemKeyDto itemKeyDto) {

        ItemDto item = itemKeyDto.getItem();
        if (item == null) {
            throw new GoodsWebException("无权访问");
        }
        // 如果是兑吧的预览，允许访问
        if ((itemKeyDto.getAppItem() == null && !item.getAutoRecommend())
                || item.isOpTypeItem(ItemDto.OpTypeActivity)) {
            throw new GoodsWebException("无权访问");
        }

    }

    /**
     * 积分商城3.0
     * 校验是否是智能推荐商品或者活动商品
     *
     * @param itemKeyDto
     */
    private void itemCheckExp(ItemKeyDto itemKeyDto) {

        ItemDto item = itemKeyDto.getItem();
        if (item == null) {
            throw new GoodsWebException("无权访问");
        }
        // 如果是兑吧的预览，允许访问
        if (item.isOpTypeItem(ItemDto.OpTypeActivity)) {
            throw new GoodsWebException("无权访问");
        }
    }

    @RequestMapping(value = "ccbDetail", method = RequestMethod.GET)
    @ResponseBody
    public Result<AppItemDto> detail(@RequestParam Long appItemId) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        if (app != null && config.getAppIds().contains(app.getId())) {
            return ResultBuilder.success(goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemId, null, app).getAppItem());
        }
        return ResultBuilder.success(null);
    }

    @RequestMapping(value = "itemShare/info", method = RequestMethod.GET)
    @ResponseBody
    public Result<ItemShareInfoVO> itemShareInfo() {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        ItemShareInfoVO itemShareInfoVO = new ItemShareInfoVO();
        itemShareInfoVO.setItemDetailShareSwitch(false);

        AppExtraLargeFieldDto appExtraLargeFieldDto = remoteAppExtraLargeFieldSevice.queryByAppIdWithCache(app.getId());
        if (appExtraLargeFieldDto == null) {
            return ResultBuilder.success(itemShareInfoVO);
        } else {
            Boolean itemRedirectStatus = appExtraLargeFieldDto.isShareSwitch(AppExtraLargeFieldDto.SwitchAppItemRedirect);
            if (itemRedirectStatus) {
                itemShareInfoVO.setItemDetailShareContent(appExtraLargeFieldDto.getItemShareConfig());
            }
            itemShareInfoVO.setItemDetailShareSwitch(itemRedirectStatus);
            return ResultBuilder.success(itemShareInfoVO);
        }
    }

    /**
     * @param request
     * @param response note:方法改造，添加了经纬度的入参
     * @return ModelAndView
     */
    @RequestMapping("/appItemDetail")
    @ApiOperation(value = "app商品展示", notes = "兑吧商品展示", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "appItemId", value = "appItemId", dataType = "Long", required = true, paramType = "query"),
            @ApiImplicitParam(name = "type", value = "type", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "latitude", value = "type", dataType = "Double", required = false, paramType = "query"),
            @ApiImplicitParam(name = "longitude", value = "type", dataType = "Double", required = false, paramType = "query"),

    })
    public ModelAndView appItemDetail(HttpServletRequest request, HttpServletResponse response) {
        String appidStr = request.getParameter("appItemId");
        // 如权益id存在，则确认是来自权益核销的商品详情页，不跳转至秒杀页面
        String equityId = request.getParameter("equityId");
        // 获取经纬度 (如果传的是空)
        Double latitude = null;
        Double longitude = null;
        if (StringUtils.isNotBlank(request.getParameter("latitude"))) {
            latitude = Double.valueOf(request.getParameter("latitude"));
        }
        if (StringUtils.isNotBlank(request.getParameter("longitude"))) {
            longitude = Double.valueOf(request.getParameter("longitude"));
        }
        try {
            //防止被 乱码参数 刷接口
            Long.parseLong(appidStr.trim());
        } catch (Exception e) {
            log.info("参数appItemId 错误，", e);
            return null;
        }
        // 实物需要加密
        ConsumerDto consumer = remoteConsumerService.find(RequestLocal.getCid());
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        app = unitNameCustomService.customUnitName(app);
        consumer = levelCustomService.customLevel(app, consumer);
        ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appidStr, null, app);
        // 此处用于判断是否fake类型的兑换项，如果是，跳转到相应的活动,已跟张攀确认还有用
        ItemKeyVO keyVO = new ItemKeyVO(itemKeyDto);
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        if (ItemDto.TypeFake.equals(itemKeyDto.getItemDtoType())) {
            return doFake(response, app, itemKeyDto, keyVO, domainConfigDto);
        }
        Map<String, Object> modelMap = new HashMap<>();
        //营销类信息
        if (itemKeyDto.isV1() && itemKeyDto.getAppItem() != null && StringUtils.isBlank(equityId)) {
            SecKillActConfVO secKillActConfVO = actConfService.getCachedSeckillActConfWithUserData(itemKeyDto.getAppItem().getId());
            if (ActConfServiceImpl.beforeSecKillStart30Min(secKillActConfVO)) {//秒杀开始前30分钟强制跳转到秒杀页面
                sendRedirect(response, UrlUtils.addScheme(domainConfigDto.getSeckillDomain() + ActConfServiceImpl.SECKILL_URL + secKillActConfVO.getId()));
                return null;
            }
            modelMap.put("secKillActConf", secKillActConfVO);
        }
        catLog(itemKeyDto);

        // 部分展示信息的封装
        CItemVO citem = consumerItemRenderService.getCItemVO(itemKeyDto, app);
        // 邮费
        setExpressPrice(consumer, citem);
        // 分享功能用的content
        String shareContent = shareContentService.getItemShareContent(itemKeyDto, citem, request.getScheme(), app);
        boolean isShareOpen = app.isAppSwitch(AppSimpleDto.SwitchOpenShare);
        String ip = RequestTool.getIpAddr(request);
        String osType = RequestTool.getOSNew(RequestLocal.getRequest());
        // 构造埋点信息
        Map<String, Object> embedInfo = buildItemDetailEmbedInfo(app.getId(),
                consumer.getId(),
                itemKeyDto,
                ip,
                osType);

        VipLimitViewInfoVO vipLimitViewInfoVO = goodsItemVipLimitService.buildViPlimitViewInfo(itemKeyDto, consumer,
                app, domainConfigDto);

        String type = request.getParameter("type");
        String buttonTest = "马上兑换";
        if (itemKeyDto.getAppItem() != null) {
            buttonTest = ExtraInfoUtils.getValue(itemKeyDto.getAppItem().getExtraInfo(), ExtraInfoUtils.BUTTON_TEXT);
        }
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        String loginProgram = appSimpleQueryService.findCallLoginProgramByAppIdByCache(app.getId());

        if (itemKeyDto.isV1() || commonConfig.isNewGoodsDetailApp(app)) {
            buildV1SkuInfo(itemKeyDto, citem, app, modelMap, false, consumer);
        } else {
            /**
             * sku信息
             * sku一期只是针对开发者自有实物，后期可能针对所有商品
             * 即，根据商品id(目前是开发者自有实物商品id),能获取到sku信息即返回，这里的判断去除不会有影响，开发者商品创建时，会按照一期的要求，只针对开发者自有商品做好判断；即非开发者实物，是不会存在sku信息的；
             */
            buildSkuInfo(itemKeyDto, citem, modelMap);
        }

        // 设置兑换来源信息
        setSourceInfo(modelMap, request, app, itemKeyDto, citem);

        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = getExchangeButtonControlInfoVO(consumer, app, itemKeyDto, type, buttonTest, citem.getCredits(), latitude, longitude);


        /**
         * 1.是否自有加钱购商品
         * 2.根据商品ID查询扩展信息判断该商品是否支持用户钱包抵扣
         * buildWalletDeductInfo(itemKeyDto, citem);
         * 20181121功能下架
         */
        citem.setIsWalletDeductible(false);

        //会员商品信息
        if (itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeVipGoods)) {
            vipGoodsService.buildVipGoodsInfo(itemKeyDto, citem, app, consumer, exchangeButtonControlInfoVO, vipLimitViewInfoVO);
        }

        // 印鸽虚拟商品定制
        yingeVirtualHandle(app.getId(),itemKeyDto,modelMap);

        // 开发者自定义金额虚拟商品
        selfCustomExchangeVirtualHandler(itemKeyDto,modelMap);

        // 商品详情PV UV统计
        wrapStatDetail(request, consumer, app, itemKeyDto, vipLimitViewInfoVO);

        modelMap.put("isMobike", isMoBike);
        //应用宝定制文案
        YingYongBaoBo.setCustomizeButtonText(app, itemKeyDto, exchangeButtonControlInfoVO);
        checkConsumerLabel(consumer, app, itemKeyDto, exchangeButtonControlInfoVO);
        // 标签兑换限制最低优先级
        checkAppItemLimitTag(consumer, Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getId).orElse(null), app, exchangeButtonControlInfoVO, equityId);
        // 兑换按钮定制
        exchangeTextByCustomized(consumer,itemKeyDto,app,exchangeButtonControlInfoVO);
        // 组装返回信息
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        replaceDomain(domainConfigDto, citem);
        replaceDomain(domainConfigDto, itemKeyDto);

        // 虚拟商品账号限购文案
        Pair<Boolean, String> accountLimitInfo = getAccountLimitInfo(itemKeyDto);
        modelMap.put("accountLimit", accountLimitInfo.getKey());
        modelMap.put("accountLimitDesc", accountLimitInfo.getValue());
        modelMap.put("citem", citem);
        modelMap.put("title", citem.getTitle());
        modelMap.put("poweredBy", !app.isAppSwitch(AppSimpleDto.SwitchPoweredBy));
        modelMap.put("consumer", consumer);
        modelMap.put("itemKey", itemKeyDto);//计划等模型统一后下线，禁止整个对象丢给前端
        modelMap.put("itemType", itemKeyDto.isAmbItemKey());// 是否加钱购
        modelMap.put("app", this.getAppWithEarnCreditsLetter(app,consumer));
        modelMap.put("geeType", GoodsTypeEnum.APP.getGtype());
        modelMap.put("ip", ip);
        modelMap.put("os", osType);
        modelMap.put("stInfo", EmdCommonUtil.emdExporFromMap(embedInfo, domainConfigDto.getEmbedDomain()));
        modelMap.put("shareContent", shareContent);
        modelMap.put("isShareOpen", isShareOpen);
        modelMap.put("over", false);
        modelMap.put("appClient", domainConfigDto.getAppDomain());
        modelMap.put("isLogin", !(consumer.isNotLoginUser()));
        modelMap.put("exchangeButtonControlInfoVO", exchangeButtonControlInfoVO);
        modelMap.put("isWeibo", AppIdConstant.isWeibo(app.getId()));
        modelMap.put("isDownloadUrlShowText", AppIdConstant.isDownloadUrlShowText(app.getId()));
        modelMap.put("openLogin", app.isAppSwitch(AppSimpleDto.SwitchUseLoginCode) && StringUtils.isNotBlank(loginProgram));
        addViLimitViewInfoVO(modelMap, vipLimitViewInfoVO);
        modelMap.put("recommendDomain", domainConfigDto.getActivityDomain());
        modelMap.put("vipLevel", consumer.getVipLevel());
        //央视频公益会员身份判断定制
        handleYangshiPin(modelMap, RequestLocal.getAppId(), itemKeyDto.getAppItem(), RequestLocal.getConsumerDO());
        //判断 购物车 满包邮
        hasMall(itemKeyDto, modelMap, app);
        //海底捞定制
        modelMap = setSaleNum(itemKeyDto, modelMap);
        modelMap.put("hideValidDate", hsbcService.hideValidDate(itemKeyDto));
        // 加入分享代码
        modelMap.put("isHdShareOpen", app.isAppSwitch(AppSimpleDto.SwitchShareProgramType));
        modelMap.put("cfg", detailCfg(modelMap, app));
        modelMap.put("canExchangeMJVip", canExchangeMJVip(Long.parseLong(appidStr.trim()), exchangeButtonControlInfoVO));
        //判断是否支持混合支付 和开启底价
        canMixPay(itemKeyDto.getAppItem().getId(), modelMap, app, consumer);
        //海底捞会员日活动定制
        hideShoppingCart(app, itemKeyDto, modelMap);
        //定制权益白名单
        modelMap = setEquiteWhiteList(modelMap, itemKeyDto, equityId);
        //来伊份定制 单独获取--
        setLaiYiFenField(modelMap,RequestLocal.getAppId(),appidStr);
        // 花西子定制
        setHuaXiZiField(modelMap, RequestLocal.getAppId(), itemKeyDto);
        // 福建中行定制
        setBocFuJianField(modelMap, app, itemKeyDto);
        // 零食很忙定制
        setLshmField(modelMap,itemKeyDto, app);
        // 拼团商品详情
        setGroupBuyInfo(app, request, consumer.getId(), itemKeyDto, modelMap);

        // todo 万达定制
        setWandaField(modelMap,app,itemKeyDto);

        response.setHeader("Cache-Control", "no-store,no-cache,must-revalidate");
        ModelAndView modelAndView = getAppitemModelAndView(consumer, app, itemKeyDto, loginProgram, modelMap);
        String viewName = modelAndView.getViewName();
        if (!viewName.startsWith("/")) {
            viewName = "/" + viewName;
        }
        return innerPageManager.handleInnerPageModelView(modelAndView, viewName);
    }

    /**
     * 零食很忙商品定制
     */
    private void setLshmField(Map<String, Object> modelMap, ItemKeyDto itemKeyDto, AppSimpleDto app) {
        try {
            if(!lshmConfig.getAppIds().contains(app.getId())){
                return;
            }
            if (itemKeyDto == null || itemKeyDto.getAppItem() == null){
                return;
            }
            String config = WhiteAccessUtil.selectWhiteListJsonConfig(LSHM_GOODS_RELATION_WHITE);
            List<JSONObject> configList = JSONObject.parseArray(config, JSONObject.class);
            if (configList == null || configList.isEmpty()){
                return;
            }
            Long appItemId = itemKeyDto.getAppItem().getId();
            // 双向关联
            configList.stream()
                    .filter(x->appItemId.equals(x.getLong("appItemId")))
                    .findFirst()
                    .ifPresent(x->modelMap.put("lshmRelateGoods",x.getLong("relateAppItemId")));
            configList.stream()
                    .filter(x->appItemId.equals(x.getLong("relateAppItemId")))
                    .findFirst()
                    .ifPresent(x->modelMap.put("lshmRelateGoods",x.getLong("appItemId")));
        }catch (Exception e){
            log.warn("零食很忙 商品定制 error",e);
        }

    }

    /**
     * 开发者自定义金额虚拟商品
     */
    private void selfCustomExchangeVirtualHandler(ItemKeyDto itemKeyDto,Map<String, Object> modelMap) {
        AppItemDto appItem = itemKeyDto.getAppItem();
        if (appItem == null){
            return;
        }
        Long appItemId = appItem.getId();
        String jsonListStr = WhiteAccessUtil.selectWhiteListJsonConfig(CUSTOM_EXCHANGE_VIRTUAL_WHITE);
        List<Long> whiteAppIds = JSONObject.parseArray(jsonListStr, CustomExchangeVirtualWhite.class).stream()
                .map(CustomExchangeVirtualWhite::getAppId)
                .collect(Collectors.toList());
        // 非白名单跳过
        if (!whiteAppIds.contains(appItem.getAppId())){
            return;
        }
        try {
            Map<Long, Boolean> selfVirtualCustomExchangeMap = remoteItemNewExtraService.findAppItemEntriesByItemAndPropNameApi(Lists.newArrayList(appItemId), "selfVirtualCustomExchange")
                    .stream()
                    .collect(Collectors.toMap(KeyValueEntity::getBizId, y-> Boolean.parseBoolean(y.getPropValue()),(o,n)->n));
            modelMap.put("selfVirtualCustomExchange",selfVirtualCustomExchangeMap.getOrDefault(appItemId, false));
        }catch (Exception e){
            log.error("开发者自定义金额虚拟商品异常 appItemId={}",appItemId,e);
        }
    }

    /**
     * 印鸽虚拟定制处理
     * @param appId 应用id
     * @param itemKeyDto appKeyDto
     * @param modelMap 返回前端的数据
     */
    private void yingeVirtualHandle(Long appId,ItemKeyDto itemKeyDto,Map<String, Object> modelMap) {
        try {
            // 不是兑吧商品跳过
            ItemDto item = itemKeyDto.getItem();
            if (item == null){
                return;
            }
            // 不是印鸽虚拟商品跳过
            if (!ItemDto.TypeVirtual.equals(item.getType()) || !VirtualItemChannelEnum.YINGE_TEST.getCode().equals(item.getMerchantCoding())){
                return;
            }
            // 到这里就说明是印鸽虚拟商品了
            YingeMessage yingeMessage = new YingeMessage();

            // 获取关联的appItem信息
            AppItemDto appItem = itemKeyDto.getAppItem();
            if (appItem == null) {
                log.warn("印鸽 商品itemExtraDto缺失 ItemKeyDto={}",JSON.toJSONString(itemKeyDto));
                return;
            }
            // 关联的item、appItem信息
            String bindItemId = appItem.getAppItemSkuDtoList().get(0).getMerchantCoding();
            ItemKeyDto bindItemKeyDto = remoteItemKeyService.getItemKey(appId,Long.parseLong(bindItemId)).getResult();
            if (bindItemKeyDto == null || bindItemKeyDto.getItem() == null || bindItemKeyDto.getAppItem() == null){
                log.warn("印鸽 商品关联itemKey信息缺失 ItemKeyDto={} bindItemId={}",JSON.toJSONString(itemKeyDto),bindItemId);
                return;
            }

            // 封装返回对象
            yingeMessage.setYinge(true);
            yingeMessage.setBindAppItemId(bindItemKeyDto.getAppItem().getId());
            yingeMessage.setBindItemPrice(bindItemKeyDto.getItem().getMarketPrice());
            yingeMessage.setBindItemName(bindItemKeyDto.getAppItem().getTitle());
            yingeMessage.setBindItemId(bindItemKeyDto.getItem().getId());
            modelMap.put("yingeMessage",yingeMessage);
        }catch (Exception e){
            log.error("印鸽虚拟商品定制异常 ItemKeyDto={}",JSON.toJSONString(itemKeyDto),e);
        }
    }

    public void setLaiYiFenField(Map<String, Object> modelMap, Long appId,String appidStr) {
        if (!laiYiFenConfig.getAppIds().contains(appId)) {
            return;
        }
        List<String> miniProExchange = Collections.singletonList("isMiniProExchange");
        Boolean isMiniProExchange = laiYiFenService.isMiniProExchange(appidStr,miniProExchange);
        modelMap.put("isMiniProExchange",isMiniProExchange);
    }


    public void setHuaXiZiField(Map<String, Object> modelMap, Long appId,ItemKeyDto itemKeyDto) {
        if (!WhiteAccessUtil.matchWhiteList(appId, EXCHANGE_REDEEMED_SHOW)){
            return;
        }

        Map<String, Boolean> extraMap = huaXiZiService.getItemNewExtra(itemKeyDto.getAppItem().getId());
        if (MapUtils.isNotEmpty(extraMap)){
            modelMap.putAll(extraMap);
        }else {
            modelMap.put(AppItemNewExtraPropKeyConstant.SHOW_EXCHANGE_NUM, Boolean.TRUE);
            modelMap.put(AppItemNewExtraPropKeyConstant.SHOW_REMAIN_NUM, Boolean.TRUE);
        }

        org.apache.commons.lang3.tuple.Pair<Long, Long> stockPair = huaXiZiService.getItemRedeemedStockAndRemainingStock(itemKeyDto, appId);

        modelMap.put("redeemedStock", stockPair.getLeft());
        modelMap.put("remainStock",  stockPair.getRight());

    }
    public void setWandaField(Map<String, Object> modelMap, AppSimpleDto app,ItemKeyDto itemKeyDto) {

        if (!wandaConfig.ifWandaApp(app.getId())) {
            return;
        }
        /**
         * 增加员工价字段 innerPrice
         * 增加分销商品标识 distribution 0关闭，1开启
         * 增加分销员id ：disNo
         * 增加分销员uid：disUid
         * 增加分销员关联酒店信息：relevanceInfo
         */
        /**
         * innerPrice 1.cookie判断是否是员工，2.判断是否开启了员工价  （staff = 1 员工   0 或者不传  不是员工）3.查询员工折扣
         *
         *
         */
        List<KeyValueEntity> keyValueEntities = remoteItemNewExtraService.findAppItemAllApi(itemKeyDto.getAppItem().getId());
        if(CollectionUtils.isEmpty(keyValueEntities)){
            return;
        }

        Map<String, KeyValueEntity> appItemExtraMap = keyValueEntities.stream().filter(u -> Objects.equals(u.getPropValue(), wandaConfig.OPEN)).collect(Collectors.toMap(KeyValueEntity::getPropName, Function.identity()));
        log.info("万达酒店定制:appItemExtraMap:{},userId:{}",RequestLocal.getCid(), JSON.toJSONString(appItemExtraMap));
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(RequestLocal.getCid()).getResult();



        if (consumerExtraDto != null && StringUtils.isNotBlank(consumerExtraDto.getJson())) {
            String dcustomExtra = consumerExtraDto.getJson();
            String staff = null;
            if (StringUtils.isNotBlank(dcustomExtra)){
                staff = JSON.parseObject(dcustomExtra).getString("staff");
            }
            if (Objects.equals(staff, "1")
                    && appItemExtraMap.get(AppItemNewExtraPropKeyConstant.WANDA_CUSTOM_DISCOUNT) !=null
                    &&  Objects.equals( appItemExtraMap.get(AppItemNewExtraPropKeyConstant.WANDA_CUSTOM_DISCOUNT).getPropValue(),wandaConfig.OPEN)) {
                // todo 员工折扣 数据库取
                modelMap.put("customDiscount", wandaService.getCustomDiscountValue(app.getId()));
            }
        }
        // 分销是否开启
        KeyValueEntity distributionEntity = appItemExtraMap.get(AppItemNewExtraPropKeyConstant.WANDA_DISTRIBUTION);
        // todo 是否开启分销
        if (distributionEntity != null && Objects.equals(wandaConfig.OPEN,distributionEntity.getPropValue())){
            // 设置分销信息
            wandaService.setDisInfo(modelMap,RequestLocal.getPartnerUserId(),app.getId());
        }
        modelMap.put("creditsRate", app.getCreditsRate());
    }


        /**
         * 校验商品是否被用户标签限制
         *
         * @param consumer
         * @param appItemId
         * @param app
         * @param exchangeButtonControlInfoVO
         * @param equityId
         */
    private void checkAppItemLimitTag(ConsumerDto consumer, Long appItemId, AppSimpleDto app, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO, String equityId) {
        // 权益类型不限制
        if (StringUtils.isNotEmpty(equityId) || Objects.isNull(appItemId)) {
            return;
        }
        // 已经限制了
        if (!exchangeButtonControlInfoVO.getExchangeEnable()) {
            return;
        }

        boolean limitResult = goodsItemDataService.limitGoodsTag(app.getId(), appItemId, consumer.getId());
        if (limitResult) {
            exchangeButtonControlInfoVO.setExchangeText(GoodsItemStatusService.StatusTypeEnum.GOODS_TAG_LIMIT_EXCHANGE.getName());
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.StatusTypeEnum.GOODS_TAG_LIMIT_EXCHANGE.getValue());
            exchangeButtonControlInfoVO.setLock(true);
        }
    }

    /**
     * 福建中行定制字段
     *
     * @param modelMap   模型图
     * @param app        应用程序
     * @param itemKeyDto 项目关键dto
     */
    private void setBocFuJianField(Map<String, Object> modelMap, AppSimpleDto app, ItemKeyDto itemKeyDto) {
        // 属于福建中行app
        Boolean whiteBool = WhiteAccessUtil.matchWhiteList(app.getId(), FUJIAN_WHITE_KEY);
        if (!whiteBool) {
            modelMap.put("fujianExchange", false);
            return;
        }
        if (itemKeyDto == null || itemKeyDto.getAppItem() == null) {
            modelMap.put("fujianExchange", false);
            return;
        }
        // 属于虚拟商品
        Boolean itemTypeBool = Objects.equals(ItemBaseDto.TypeVirtual, itemKeyDto.getAppItem().getType());
        // 属于数币商品
        Boolean numCoinItem = false;
        for (AppItemSkuDto appItemSkuDto : itemKeyDto.getAppItem().getAppItemSkuDtoList()) {
            if (StringUtils.isNotBlank(appItemSkuDto.getMerchantCoding()) && appItemSkuDto.getMerchantCoding().contains(FUJIAN_ICON)) {
                numCoinItem = true;
            }
        }
        if (itemTypeBool && numCoinItem) {
            modelMap.put("fujianExchange", true);
        } else {
            modelMap.put("fujianExchange", false);
        }
    }

    private void canMixPay(Long appItemId, Map<String, Object> modelMap, AppSimpleDto app, ConsumerDto consumer) {
        //混合支付的字段已经放入商品属性中，通过商品的属性判断
        AppNewExtraDto appNewExtraDto = remoteAppNewExtraService.findByAppId(app.getId()).getResult();
        List<KeyValueEntity> keyValues = remoteItemNewExtraService.findAppItemAllApi(appItemId);
        Map<String, String> propMap = keyValues.stream().collect(Collectors.toMap(KeyValueEntity::getPropName, obj -> ObjectUtils.defaultIfNull(obj.getPropValue(), StringUtils.EMPTY), (oldv, newv) -> newv));
        modelMap.put(WilteListConstants.IS_MIX_PAY, Objects.nonNull(propMap.get(WilteListConstants.IS_MIX_PAY)) && Objects.equals(propMap.get(WilteListConstants.IS_MIX_PAY), "1"));
        boolean isGuaranteed = Objects.nonNull(propMap.get(WilteListConstants.IS_GUARANTEED)) && BooleanUtil.toBoolean(propMap.get(WilteListConstants.IS_GUARANTEED));
        modelMap.put(WilteListConstants.IS_GUARANTEED, isGuaranteed);
        boolean carBind = bjxdCarBind(consumer);
        if (isGuaranteed) {
            boolean ownerNotSubToResPrice = Objects.nonNull(propMap.get(WilteListConstants.OWNER_NOT_SUB_TO_RESPRICE)) && BooleanUtil.toBoolean(propMap.get(WilteListConstants.OWNER_NOT_SUB_TO_RESPRICE));
            //车主用户不受混合低价限制
            if (ownerNotSubToResPrice && carBind) {
                modelMap.put(WilteListConstants.IS_GUARANTEED, false);
            }
        }
        modelMap.put("nonOwnerSwOrigPriceBuy", Objects.nonNull(propMap.get("nonOwnerSwOrigPriceBuy")) && Objects.equals(propMap.get("nonOwnerSwOrigPriceBuy"), "true") ? 1 : 0);
        modelMap.put("isCardBind", carBind ? 1 :0);
        modelMap.put("deductionType", appNewExtraDto.getDeductionType());
        modelMap.put("roundingType", appNewExtraDto.getRoundingType());
    }

    private Pair<Boolean, String> getAccountLimitInfo(ItemKeyDto itemKeyDto) {
        String itemType = Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getType).orElse(StringUtils.EMPTY);
        if (!itemType.equals(ItemDto.TypeVirtual)) {
            return Pair.from(false, StringUtils.EMPTY);
        }
        String accountLimitScope = Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getAccountLimitScope).orElse(null);
        Integer accountLimitCount = Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getAccountLimitCount).orElse(null);
        if (StringUtils.isEmpty(accountLimitScope) || Objects.isNull(accountLimitCount)) {
            return Pair.from(false, StringUtils.EMPTY);
        }

        boolean accountLimit = false;
        String accountLimitDesc = null;
        if (accountLimitScope.equals(ItemDto.LimitTypeForever)) {
            accountLimitDesc = "同一账号限充值" + accountLimitCount + "次";
            accountLimit = true;
        } else if (accountLimitScope.startsWith(ItemDto.LimitTypePeriod)) {
            int index = accountLimitScope.indexOf('_') + 1;
            if (index < accountLimitScope.length()) {
                int day = Integer.parseInt(accountLimitScope.substring(index));
                accountLimitDesc = "同一账号限每" + day + "天充值" + accountLimitCount + "次";
                accountLimit = true;
            }
        } else if (accountLimitScope.equals(ItemDto.LimitTypeEveryday)) {
            accountLimitDesc = "同一账号每天限充值" + accountLimitCount + "次";
            accountLimit = true;
        } else if (accountLimitScope.equals(ItemDto.LimitTypeWeek)) {
            accountLimitDesc = "同一账号每周限充值" + accountLimitCount + "次";
            accountLimit = true;
        } else if (accountLimitScope.equals(ItemDto.LimitTypeMonth)) {
            accountLimitDesc = "同一账号每月限充值" + accountLimitCount + "次";
            accountLimit = true;
        }
        return Pair.from(accountLimit, accountLimitDesc);
    }

    private void handleYangshiPin(Map<String, Object> modelMap, Long appId, AppItemDto appItemDto, ConsumerDto consumerDto) {
        if (!yangShiPinConfig.getAppIds().contains(appId)
                || !ItemDto.TypeObject.equals(appItemDto.getType())) {
            return;
        }

        modelMap.put("charityVipLimit", yangshiPinService.snowVipOpen(appItemDto));
        modelMap.put("userIsCharityVip", yangshiPinService.judgeUserSnowVip(consumerDto));
    }

    /**
     * 设置拼团商品详情页信息
     *
     * @param request
     * @param itemKeyDto
     * @param modelMap
     */
    private void setGroupBuyInfo(AppSimpleDto app, HttpServletRequest request, Long consumerId, ItemKeyDto itemKeyDto, Map<String, Object> modelMap) {
        try {
            modelMap.put("hasGroupBuyAct", false);
            modelMap.put("groupBuyAct", null);
            List<GroupBuyActConfDto> groupBuyActList = remoteGroupBuyActConfService.getGroupBuyActConfByAppItemIds(app.getId(), Lists.newArrayList(itemKeyDto.getAppItem().getId()));
            if (CollectionUtils.isEmpty(groupBuyActList) || groupBuyActList.size() > 1) {
                return;
            }
            // 拼团活动信息
            GroupBuyActConfDto groupBuyActConfDto = groupBuyActList.get(0);
            // 活动结束/强制失效
            if (Objects.equals(groupBuyActConfDto.getEnable(), 0) || groupBuyActConfDto.getActEndTime().before(new Date())) {
                return;
            }
            GroupBuyActInfoVO actInfoVO = BeanUtils.copy(groupBuyActConfDto, GroupBuyActInfoVO.class);
            actInfoVO.setActId(groupBuyActConfDto.getId());
            // 预告时间 < 当前时间 < 活动开始时间
            if (groupBuyActConfDto.getForeShowDuration() != null) {
                Date foreShowDurationDate = DateUtils.minutesAddOrSub(groupBuyActConfDto.getActStartTime(), -groupBuyActConfDto.getForeShowDuration());
                if (foreShowDurationDate.before(new Date()) && groupBuyActConfDto.getActStartTime().after(new Date())) {
                    actInfoVO.setForeshowDurtion(foreShowDurationDate.before(groupBuyActConfDto.getActStartTime()) ? foreShowDurationDate : groupBuyActConfDto.getActStartTime());
                }
                // 预告未开始
                if (foreShowDurationDate.after(new Date())) {
                    return;
                }
            }
            // 无预告，活动未开始
            else if (groupBuyActConfDto.getActStartTime().after(new Date())) {
                return;
            }
            List<GroupBuyActConfDto.SkuInfo> skuInfoList = JSON.parseArray(groupBuyActConfDto.getSkuInfoJson(), GroupBuyActConfDto.SkuInfo.class);
            if (CollectionUtils.isEmpty(skuInfoList)) {
                log.warn("拼团活动异常-无拼团商品sku信息, actId:{}, appItemId:{}", groupBuyActConfDto.getId(), itemKeyDto.getAppItem().getId());
                return;
            }
//            skuInfoList = skuInfoList.stream().sorted().collect(Collectors.toList());
            actInfoVO.setGroupBuyPrice(skuInfoList.get(0).getGroupBuyPrice());
            // 当前拼团商品销量
            if (groupBuyActConfDto.getActStartTime().before(new Date())) {
                Integer saleNum = remoteGroupBuyOrderService.countSaleGroupBuyOrder(app.getId(), groupBuyActConfDto.getId());
                Integer baseNum = groupBuyActConfDto.getBaseNum();
                if (Objects.equals(groupBuyActConfDto.getShowSaleNum(), 1) && baseNum != null) {
                    Integer totalNum = NumberUtils.add(saleNum, baseNum);
                    actInfoVO.setSaleAppItemNum(baseNum >= 0 ? totalNum : (totalNum >= 0 ? saleNum : null));
                }
            }
            String groupIdStr = request.getParameter("groupId");
            // 用户是否已经参团，拼团有效期过期活动未结束，重新拼团
            actInfoVO.setGroupStatus(GroupBuyDetailStatusEnum.WAIT_GROUP_BUY.getCode());
            GroupBuyOrderQueryParam param = new GroupBuyOrderQueryParam();
            param.setStatusList(Lists.newArrayList(GroupBuyOrderStatusEnum.GROUP_BUY_ING.getCode()));
            param.setConsumerId(consumerId);
            param.setActId(groupBuyActConfDto.getId());
            if (StringUtils.isNotBlank(groupIdStr)) {
                param.setGroupId(Long.valueOf(groupIdStr));
            }
            GroupBuyOrderDto userGroupBuyOrder = remoteGroupBuyOrderService.selectByCondition(param);
            if (Objects.nonNull(userGroupBuyOrder)) {
                actInfoVO.setGroupId(userGroupBuyOrder.getGroupId());
                actInfoVO.setGroupStatus(GroupBuyDetailStatusEnum.GROUP_BUY_ING.getCode());
            } else if (StringUtils.isNotBlank(groupIdStr)) {
                GroupBuyDetailDto groupBuyDetailDto = remoteGroupBuyDetailService.selectById(Long.valueOf(groupIdStr));
                // 未拼团，可发起拼团/参与好友拼团，当前时间 < 参团有效时间
                if (groupBuyDetailDto == null) {

                } else if (Objects.equals(groupBuyDetailDto.getStatus(), GroupBuyGroupStatusEnum.GROUP_BUY_ING.getCode()) && groupBuyDetailDto.getGroupCloseTime().after(new Date())) {
                    actInfoVO.setGroupId(groupBuyDetailDto.getId());
                } else if (Objects.equals(groupBuyDetailDto.getStatus(), GroupBuyDetailStatusEnum.GROUP_BUY_SUCCESS.getCodeInt())) {
                    // 好友团已满，这个注意一下
                    actInfoVO.setGroupStatus(GroupBuyDetailStatusEnum.GROUP_BUY_LIMIT.getCode());
                } else {
                    actInfoVO.setGroupStatus(GroupBuyDetailStatusEnum.getByCodeInt(groupBuyDetailDto.getStatus()).getCode());
                }
            }
            // 用户已达拼团限购
            if (groupBuyActConfDto.getLimitNum() != null && groupBuyActConfDto.getLimitNum() > 0) {
                GroupBuyOrderQueryParam queryParam = new GroupBuyOrderQueryParam();
                queryParam.setAppItemId(itemKeyDto.getAppItem().getId());
                queryParam.setActId(groupBuyActConfDto.getId());
                queryParam.setConsumerId(consumerId);
                queryParam.setStatusList(Lists.newArrayList(GroupBuyOrderStatusEnum.CREATE.getCode(), GroupBuyOrderStatusEnum.GROUP_BUY_ING.getCode(), GroupBuyOrderStatusEnum.SUCCESS.getCode()));
                Integer buyCount = remoteGroupBuyOrderService.countGroupBuyOrderByCondition(queryParam);
                if (buyCount >= groupBuyActConfDto.getLimitNum()) {
                    actInfoVO.setGroupBuyExchange(false);
                }
            }
            // 当前时间
            actInfoVO.setNowTime(new Date());
            modelMap.put("hasGroupBuyAct", true);
            modelMap.put("groupBuyAct", actInfoVO);
        } catch (Exception e) {
            log.warn("拼团商品详情页异常，appId:{}, appItemId:{}", app.getId(), itemKeyDto.getAppItem().getId(), e);
        }
    }

    /**
     * @param request
     * @param response note:查询商品是否在限制区域
     * @return ModelAndView
     */
    @GetMapping("/checkAppItemAddr")
    @ResponseBody
    public Result<Boolean> checkAppItemAddr(HttpServletRequest request, HttpServletResponse response) {
        String appidStr = request.getParameter("appItemId");
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        app = unitNameCustomService.customUnitName(app);
        Double latitude = null, longitude = null;
        if (StringUtils.isNotBlank(request.getParameter("latitude"))) {
            latitude = Double.valueOf(request.getParameter("latitude"));
        }
        if (StringUtils.isNotBlank(request.getParameter("longitude"))) {
            longitude = Double.valueOf(request.getParameter("longitude"));
        }
        try {
            ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appidStr, null, app);
            ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
            itemViewService.addrLimitByGpsOrIp(itemKeyDto, app, exchangeButtonControlInfoVO, latitude, longitude);
            return ResultBuilder.success(exchangeButtonControlInfoVO.getExchangeEnable());
        } catch (Exception e) {
            return ResultBuilder.fail(ErrorCode.E1100017.getCode(), ErrorCode.E1100017.getDesc());
        }
    }

    private void wrapStatDetail(HttpServletRequest request, ConsumerDto consumer, AppSimpleDto app, ItemKeyDto itemKeyDto, VipLimitViewInfoVO vipLimitViewInfoVO) {
        if (!StringUtils.equals(request.getParameter("_duibaPerf"), "1")) {
            StatDetailAccessLog.addExtPair(itemKeyDto, app, consumer, vipLimitViewInfoVO.getVipFlag() ? 1 : 0);
        }
    }

    private Boolean canExchangeMJVipNew(AppItemDto appItem, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (null == appItem) {
            return true;
        }
        return canExchangeMJVip(appItem.getId(), exchangeButtonControlInfoVO);
    }

    public Boolean canExchangeMJVip(Long appItemId, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (null == appItemId) {
            return true;
        }
        if (!exchangeButtonControlInfoVO.getExchangeEnable()) {
            return false;
        }
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        if (mjConfig.getAppItemIds().contains(appItemId) && mjConfig.getAppIds().contains(consumerDto.getAppId())) {
            //锦鲤会员判断
            MJJingLiVipDto mJJingLiVipDto = remoteMJJingLiVipService.selectByConsumerId(consumerDto.getId());
            if (mJJingLiVipDto == null || !mJJingLiVipDto.getVipState()) {
                return false;
            }
        }
        return true;
    }

    /**
     * @param modelMap
     * @param itemKeyDto
     * @param equityId
     * @return
     */
    private Map<String, Object> setEquiteWhiteList(Map<String, Object> modelMap, ItemKeyDto itemKeyDto, String equityId) {
        try {
            EquityConfigDto equityConfigDto = null;
            if (StringUtils.isNotBlank(equityId)) {
                equityConfigDto = remoteEquityConfigService.selectById(Long.valueOf(equityId));
            }
            if (equityConfigDto != null && Objects.equals(equityConfigDto.getGeneralFlag(), 1)) {
                //通兑权益不需要白名单，非实物的商品，直接在此页面兑换了，前端会根据beLongWhiteList字段，走不同的兑换接口
                modelMap.put("beLongWhiteList", false);
            } else {
                modelMap.put("beLongWhiteList", equiltWhiltListAppConfig.getWhiteListAppIds().contains(RequestLocal.getAppId()));
            }
        } catch (Exception e) {
            modelMap.put("beLongWhiteList", false);
            log.warn("setEquiteWhiteList error", e);
        }
        //判断是否网点核销商品
        try {
            modelMap.put("needBoundStation", false);
            //判断是否为优惠券商品
            if (equiltWhiltListAppConfig.getEquityStationAppIds().contains(RequestLocal.getAppId()) && ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType()) && itemKeyDto.isSelfAppItemMode()) {
                StationStockFindRequest stationStockFindRequest = new StationStockFindRequest();
                stationStockFindRequest.setAppId(RequestLocal.getAppId());
                stationStockFindRequest.setAppItemId(itemKeyDto.getAppItem().getId());
                stationStockFindRequest.setDeleted(EquityValidateEnum.VALIDA.getCode());
                List<StationStockDto> stationStockDtos = remoteStationStockService.getByCondition(stationStockFindRequest);
                modelMap.put("needBoundStation", CollectionUtils.isNotEmpty(stationStockDtos));
            }
        } catch (Exception e) {
            modelMap.put("needBoundStation", false);
            log.warn("setEquiteWhiteList-needBoundStation error", e);
        }
        return modelMap;
    }

    private void hideShoppingCart(AppSimpleDto app, ItemKeyDto itemKeyDto, Map<String, Object> modelMap) {
        //海底捞会员日参加活动的实物商品隐藏购物车按钮
        if (ItemDto.TypeObject.equals(itemKeyDto.getItemType()) && itemKeyDto.getAppItem() != null) {
            modelMap.put("hiddenCart", hidilaoMembershipConfig.isValidItem(itemKeyDto.getAppItem().getId()));
        }
    }

    @Nullable
    private ModelAndView doFake(HttpServletResponse response, AppSimpleDto app, ItemKeyDto itemKeyDto, ItemKeyVO keyVO, DomainConfigDto domainConfigDto) {
        AppItemDto appItem = itemKeyDto.getAppItem();
        String link = "";
        if (null != appItem && null != appItem.getSourceType()
                && AppItemSourceTypeEnum.SourceTypeNormal.getCode() != appItem.getSourceType()) {
            DuibaShareDto appExtra = remoteAppExtraServiceNew.findDuibaShareByAppId(app.getId()).getResult();
            link = ShareLandUrlUtils.getShareLandUrl(appExtra, keyVO.getLink(domainConfigDto), appItem.getSourceRelationId(), domainConfigDto);
        } else {
            link = keyVO.getLink(domainConfigDto);
        }
        sendRedirect(response, link);
        return null;
    }

    private void sendRedirect(HttpServletResponse response, String link) {
        try {
            response.sendRedirect(UrlUtils.addScheme(link));
        } catch (IOException e) {
            log.error("redirect error! url=" + link, e);
        }
    }

    private void buildSkuInfo(ItemKeyDto itemKeyDto, CItemVO citem, Map<String, Object> modelMap) {
        List<AppItemSkuDto> skuList = remoteAppItemSkuService.findSkuListByAppItemId(itemKeyDto.getAppItem().getId());
        if (null != skuList && !skuList.isEmpty()) {
            citem.setSkuList(remoteAppItemSkuService.getSkuList(skuList));
            modelMap.put("attributeList", remoteAppItemSkuService.getAttribute(skuList));
        }
    }

    //商品重构sku
    private void buildV1SkuInfo(ItemKeyDto itemKeyDto, CItemVO citem, AppSimpleDto app, Map<String, Object> modelMap, boolean preview, ConsumerDto consumer) {
        if (itemKeyDto.isV1()) {
            AppItemDto appItemDto = itemKeyDto.getAppItem();
            if (appItemDto == null) {
                if (preview) {//兑吧未入库商品预览
                    setPreviewItemSku(itemKeyDto, citem, modelMap);
                }
                return;
            }
            //在多 sku 积分配置下，marketingItemCreditsDto 的 credits 值 是所有 sku 里面最小的
            MarketingItemCreditsDto marketingItemCreditsDto = appItemDto.getMarketingItemCreditsDto();
            List<AppItemSkuDto> skuList = appItemDto.getAppItemSkuDtoList();
            //北京现代「非车主」用户不支持通过积分+汇率兑换商品，仅能根据原价支付现金兑换该商品
            boolean bjxdNonOwnerSwOrigPriceBuy = false;
            if (null != skuList && !skuList.isEmpty()) {
                List<AppItemSkuDto> skuListFilter = skuList.stream().filter(AppItemSkuDto -> AppItemSkuDto.getSaleStatus() == null || AppItemSkuDto.getSaleStatus().intValue() == 1).collect(Collectors.toList());
                List<MarketingItemCreditsSkuDto> marketingItemCreditsSkuList = appItemDto.getMarketingItemCreditsSkuDtoList();

                List<CreditsSkuDto> creditsSkuDtoList;
                //定制如果是北京现代
                if (beiJinHyundaiConfig.getAppIds().contains(app.getId())) {
                    boolean carBind = bjxdCarBind(consumer);
                    List<KeyValueEntity> keyValues = remoteItemNewExtraService.findAppItemAllApi(appItemDto.getId());
                    Map<String, String> propMap = keyValues.stream().collect(Collectors.toMap(KeyValueEntity::getPropName, obj -> ObjectUtils.defaultIfNull(obj.getPropValue(), org.apache.commons.lang.StringUtils.EMPTY), (oldv, newv) -> newv));
                    //勾选非车主用户
                    boolean nonOwnerSwOrigPriceBuy = Objects.nonNull(propMap.get("nonOwnerSwOrigPriceBuy")) && Objects.equals(propMap.get("nonOwnerSwOrigPriceBuy"), "true");
                    if (!carBind && nonOwnerSwOrigPriceBuy && marketingItemCreditsDto.getExchangeType().intValue() == ExchangeTypeEnum.AUTOMATIC.getCode().intValue()) {
                        bjxdNonOwnerSwOrigPriceBuy = true;
                    }
                    //勾选非车主用户
                    creditsSkuDtoList = MarketingSkuUtils.getBeiJingXianDaiCustomizedCreditsSkuList(skuListFilter, marketingItemCreditsSkuList, marketingItemCreditsDto.getExchangeType(), app.getCreditsRate(), carBind,nonOwnerSwOrigPriceBuy);

                } else {
                    creditsSkuDtoList = MarketingSkuUtils.getCreditsSkuList(skuListFilter, marketingItemCreditsSkuList, marketingItemCreditsDto.getExchangeType(), app.getCreditsRate());
                }
                List<CreditsSkuVO> creditsSkuVOList = getCreditsSkuVOs(app, creditsSkuDtoList, marketingItemCreditsDto, itemKeyDto);

                //设置兑吧直播供应商价格
                if (null != citem.getDistributorId() && SupplyTypeEnumUtil.DUIBA_LIVE_SUPPLIER_PLATFORM.equals(citem.getDistributorId())) {
                    this.setDuibaLivePrice(itemKeyDto, creditsSkuVOList);
                }

                citem.setCreditsSkuList(creditsSkuVOList);

                modelMap.put("attributeList", remoteAppItemSkuService.getAttribute(skuListFilter));
            }
            //定制如果是北京现代
            if (bjxdNonOwnerSwOrigPriceBuy) {
                modelMap.put("originalPriceBuy", true);
            } else {
                modelMap.put("originalPriceBuy", marketingItemCreditsDto.getOriginalPriceBuy());
            }

        } else {
            if (itemKeyDto.isSeries()) { //老sku商品
                buildOldSkuInfo(itemKeyDto, citem, app, modelMap);
            } else { //老模型组装成假sku
                setOldGoodsSku(itemKeyDto, citem, app, modelMap);
            }
        }
    }

    private boolean bjxdCarBind(ConsumerDto consumer) {
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(consumer.getId()).getResult();
        boolean carBind = false;
        if (consumerExtraDto != null && StringUtils.isNotBlank(consumerExtraDto.getJson()) && JSONValidator.from(consumerExtraDto.getJson()).validate()) {
            JSONObject object = JSON.parseObject(consumerExtraDto.getJson());
            //carBind 0就是非车主，1就是车主
            String channelStr = object.getString("carBind");
            carBind = StringUtils.equals(channelStr, "1");
        }
        return carBind;
    }

    private void setDuibaLivePrice(ItemKeyDto itemKeyDto, List<CreditsSkuVO> creditsSkuVOList) {
        ItemDto itemDto = itemKeyDto.getItem();
        DuibaLiveSupplierGoodsDto goodsDto = remoteDuibaLiveSupplierGoodsService.getByItemId(itemDto.getId());
        if (Objects.isNull(goodsDto)) {
            return;
        }
        // 供应商不存在或者共饮上不是pop类型，直接return
        DuibaLiveSupplierDto supplierDto = remoteDuibaLiveSupplierService.getById(goodsDto.getDistributorId());
        if (Objects.isNull(supplierDto) || DuibaLiveSupplierTypeEnum.pop != DuibaLiveSupplierTypeEnum.fromValue(supplierDto.getSupplierType())) {
            return;
        }

        //1.直播供应商获取服务费率(注:0.01%以1存储)
        //1.1 DB
        int rate;
        List<ItemClassifyDto> itemClassifyDtos = new ArrayList<>();
        if (Objects.equals(itemDto.getType(), ItemDto.TypeObject)) {
            //实物按照itemClassifyId查询
            itemClassifyDtos = remoteItemClassifyBackendService.findAllByIds(Lists.newArrayList(itemDto.getItemClassifyId())).getResult();
        } else if (Objects.equals(itemDto.getType(), ItemDto.TypeCoupon)) {
            //优惠券按照类型查
            List<String> paramList = new ArrayList<>();
            if (itemDto.getSubType() != null && Objects.equals(itemDto.getSubType(), ItemDto.SubTypeCryptoCard)) {
                paramList.add(ItemClassifyTypeEnum.SUPPLIER_CRYPTO_CARD.getType());
            } else {
                paramList.add(ItemClassifyTypeEnum.SUPPLIER_COUPON.getType());
            }
            itemClassifyDtos = remoteItemClassifyBackendService.findAllByTypeList(paramList);
        }
        //1.2 check
        if (CollectionUtils.isEmpty(itemClassifyDtos) || itemClassifyDtos.get(0).getRate() == null) {
            return;
        }
        rate = itemClassifyDtos.get(0).getRate();
        //2.计算直播供应商价格
        for (CreditsSkuVO sku : creditsSkuVOList) {
            long costPrice = null == sku.getCostPrice() ? 0L : sku.getCostPrice();

            Long price = new BigDecimal(costPrice)
                    .multiply(new BigDecimal(1.0 + rate / 10000.0))
                    .setScale(2, BigDecimal.ROUND_HALF_UP).longValue();
            sku.setDuibaLiveSupplierPrice(price);
        }

    }


    //老sku
    private void buildOldSkuInfo(ItemKeyDto itemKeyDto, CItemVO citem, AppSimpleDto app, Map<String, Object> modelMap) {
        AppItemDto appItemDto = itemKeyDto.getAppItem();
        if (appItemDto == null) {
            return;
        }
        boolean isRMB = app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint);
        Long stock = goodsItemStockService.findStock(itemKeyDto, app.getId());
        CreditsSkuVO itemCreditsVO = getSingleCreditsSkuVO(citem, isRMB, stock);
        List<CreditsSkuVO> creditsSkuVOList = getOldCreditsSkuVOs(app, appItemDto.getAppItemSkuDtoList(), itemCreditsVO);
        citem.setCreditsSkuList(creditsSkuVOList);
        //标记为多规格
        citem.setMultiSku(Boolean.TRUE);
        modelMap.put("attributeList", remoteAppItemSkuService.getAttribute(appItemDto.getAppItemSkuDtoList()));
        modelMap.put("originalPriceBuy", false);
    }

    private void setPreviewItemSku(ItemKeyDto itemKeyDto, CItemVO citem, Map<String, Object> modelMap) {
        ItemDto item = itemKeyDto.getItem();
        if (item == null) {
            return;
        }
        List<ItemSkuDto> itemSkuDtoList = item.getItemSkuDtoList();
        List<CreditsSkuVO> creditsSkuVOList = new ArrayList<>();
        List<AppItemSkuDto> skuList = new ArrayList<>();
        for (ItemSkuDto itemSkuDto : itemSkuDtoList) {
            CreditsSkuVO creditsSkuVO = new CreditsSkuVO();
            creditsSkuVO.setSkuId(itemSkuDto.getId());
            creditsSkuVO.setMarketingCredits(0d);
            creditsSkuVO.setMarketingPrice(itemSkuDto.getSalePrice());
            creditsSkuVO.setFacePrice(itemSkuDto.getFacePrice());
            creditsSkuVO.setSalePrice(itemSkuDto.getSalePrice());
            creditsSkuVO.setAttributeValue(itemSkuDto.getAttributeJson());
            creditsSkuVO.setImgUrl(itemSkuDto.getImgUrl());
            creditsSkuVO.setRemaining(itemSkuDto.getRemaining());
            creditsSkuVO.setStartDay(itemSkuDto.getStartDay());
            creditsSkuVO.setEndDay(itemSkuDto.getEndDay());
            if (itemSkuDto.getEndDay() != null) {
                creditsSkuVO.setLeftDays(String.valueOf(DateUtil.getDifferDay(itemSkuDto.getEndDay())));
            }
            creditsSkuVO.setCurrentStock(itemSkuDto.getCurrentStock());
            creditsSkuVO.setCurrentTotalStock(itemSkuDto.getCurrentTotalStock());
            creditsSkuVOList.add(creditsSkuVO);

            AppItemSkuDto appItemSkuDto = new AppItemSkuDto();
            appItemSkuDto.setAttributeJson(itemSkuDto.getAttributeJson());
            skuList.add(appItemSkuDto);
        }
        citem.setCreditsSkuList(creditsSkuVOList);
        modelMap.put("attributeList", remoteAppItemSkuService.getAttribute(skuList));
        modelMap.put("originalPriceBuy", false);
    }

    private void setOldGoodsSku(ItemKeyDto itemKeyDto, CItemVO citem, AppSimpleDto app, Map<String, Object> modelMap) {
        List<RedefineAttributeDto> redefineAttributeDtoList = new ArrayList<>();
        List<CreditsSkuVO> creditsSkuVOList = new ArrayList<>();
        boolean isRMB = app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint);
        Long stock = goodsItemStockService.findStock(itemKeyDto, app.getId());
        if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType()) || ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType())) {
            CreditsSkuVO creditsSkuVO = getSingleCreditsSkuVO(citem, isRMB, stock);
            if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())) {
                setOldCouponBatch(itemKeyDto, creditsSkuVO);
            }
            creditsSkuVOList.add(creditsSkuVO);
        } else if (ItemDto.TypeVirtual.equals(itemKeyDto.getItemDtoType())) {
            PriceDegreeDto pd = new PriceDegreeDto(itemKeyDto.getAppItem().getCustomPrice());
            TreeMap<String, Map<String, String>> priceDegreeTree = pd.getCustomDegreeMap();
            if (pd.isSingleDegree()) {
                CreditsSkuVO creditsSkuVO = getSingleCreditsSkuVO(citem, isRMB, stock);
                for (Entry<String, Map<String, String>> entry : priceDegreeTree.entrySet()) {
                    creditsSkuVO.setDegreeId(entry.getKey());
                }
                creditsSkuVOList.add(creditsSkuVO);
            } else {
                setMultiVirtualSku(citem, redefineAttributeDtoList, creditsSkuVOList, isRMB, stock, priceDegreeTree);
            }
        }
        citem.setCreditsSkuList(creditsSkuVOList);
        modelMap.put("attributeList", redefineAttributeDtoList);
        modelMap.put("originalPriceBuy", false);//不能原价购买
    }

    private void setOldCouponBatch(ItemKeyDto itemKeyDto, CreditsSkuVO creditsSkuVO) {
        GoodsBatchDto batch = couponService.getGoodsUsingBatchWithStock(itemKeyDto);
        if (batch == null) {
            return;
        }
        creditsSkuVO.setStartDay(batch.getStartDay());
        creditsSkuVO.setEndDay(batch.getEndDay());
        if (creditsSkuVO.getEndDay() != null) {
            creditsSkuVO.setLeftDays(String.valueOf(DateUtil.getDifferDay(creditsSkuVO.getEndDay())));
        }
        creditsSkuVO.setCurrentStock(batch.getStock());
        creditsSkuVO.setCurrentTotalStock(batch.getTotalStock());
    }

    /**
     * 老模型多档虚拟商品sku组装
     *
     * @param citem
     * @param redefineAttributeDtoList
     * @param creditsSkuVOList
     * @param isRMB
     * @param stock
     * @param priceDegreeTree
     */
    private void setMultiVirtualSku(CItemVO citem, List<RedefineAttributeDto> redefineAttributeDtoList, List<CreditsSkuVO> creditsSkuVOList, boolean isRMB, Long stock, TreeMap<String, Map<String, String>> priceDegreeTree) {
        citem.setMultiSku(true);
        long index = 0l;
        RedefineAttributeDto redefineAttributeDto = new RedefineAttributeDto();
        redefineAttributeDto.setAttributeId(0L);
        redefineAttributeDto.setAttributeName("档位");
        List<RedefineAttributeValueDto> value = new ArrayList<>();
        for (Entry<String, Map<String, String>> entry : priceDegreeTree.entrySet()) {
            ++index;
            //假sku拼装
            CreditsSkuVO creditsSkuVO = new CreditsSkuVO();
            creditsSkuVO.setSkuId(index);
            String credits = entry.getValue().get(PriceDegreeDto.CREDITS_KEY);
            double creditsD = Double.parseDouble(credits);
            if (isRMB) {
                creditsSkuVO.setMarketingCredits(creditsD / 100.0);
            } else {
                creditsSkuVO.setMarketingCredits(creditsD);
            }
            if (citem.getPrice() != null) {
                creditsSkuVO.setFacePrice(citem.getPrice().longValue() * 100);
            }
            creditsSkuVO.setSalePrice(citem.getSalePrice());
            creditsSkuVO.setAttributeValue("0:" + index);
            creditsSkuVO.setRemaining(stock.intValue());
            creditsSkuVO.setDegreeId(entry.getKey());
            creditsSkuVOList.add(creditsSkuVO);

            //档位信息
            RedefineAttributeValueDto redefineAttributeValueDto = new RedefineAttributeValueDto();
            redefineAttributeValueDto.setValueId(index);
            redefineAttributeValueDto.setValueText(entry.getValue().get(PriceDegreeDto.TITLE_KEY));
            value.add(redefineAttributeValueDto);
        }
        redefineAttributeDto.setValue(value);
        redefineAttributeDtoList.add(redefineAttributeDto);
    }

    @NotNull
    private CreditsSkuVO getSingleCreditsSkuVO(CItemVO citem, boolean isRMB, Long stock) {
        CreditsSkuVO creditsSkuVO = new CreditsSkuVO();
        creditsSkuVO.setSkuId(1L);
        if (isRMB) {
            creditsSkuVO.setMarketingCredits(citem.getCredits() / 100.0);
        } else {
            creditsSkuVO.setMarketingCredits((double) citem.getCredits());
        }
        creditsSkuVO.setMarketingPrice(citem.getSalePrice());
        if (citem.getPrice() != null) {
            creditsSkuVO.setFacePrice(citem.getPrice().longValue() * 100);
        }
        creditsSkuVO.setSalePrice(citem.getSalePrice());
        creditsSkuVO.setAttributeValue("0:0");
        creditsSkuVO.setRemaining(stock.intValue());
        return creditsSkuVO;
    }

    private List<CreditsSkuVO> getCreditsSkuVOs(AppSimpleDto app, List<CreditsSkuDto> creditsSkuDtoList, MarketingItemCreditsDto marketingItemCreditsDto, ItemKeyDto itemKey) {
        boolean isRMB = app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint);
        List<CreditsSkuVO> creditsSkuVOList = new ArrayList<>();
        if (creditsSkuDtoList == null || creditsSkuDtoList.isEmpty()) {
            return creditsSkuVOList;
        }
        for (CreditsSkuDto creditsSkuDto : creditsSkuDtoList) {
            CreditsSkuVO creditsSkuVO = new CreditsSkuVO();
            creditsSkuVO.setSkuId(creditsSkuDto.getSkuId());
            if (isRMB && creditsSkuDto.getMarketingCredits() != null) {
                creditsSkuVO.setMarketingCredits(creditsSkuDto.getMarketingCredits() / 100.0);
            } else {
                creditsSkuVO.setMarketingCredits((double) creditsSkuDto.getMarketingCredits());
            }
            creditsSkuVO.setMarketingPrice(creditsSkuDto.getMarketingPrice());
            creditsSkuVO.setFacePrice(creditsSkuDto.getFacePrice());
            creditsSkuVO.setSalePrice(creditsSkuDto.getSalePrice());
            creditsSkuVO.setAttributeValue(creditsSkuDto.getAttributeValue());
            creditsSkuVO.setImgUrl(creditsSkuDto.getImgUrl());
            creditsSkuVO.setRemaining(creditsSkuDto.getRemaining());
            creditsSkuVO.setStartDay(creditsSkuDto.getStartDay());
            creditsSkuVO.setEndDay(creditsSkuDto.getEndDay());
            if (creditsSkuDto.getEndDay() != null) {
                creditsSkuVO.setLeftDays(String.valueOf(DateUtil.getDifferDay(creditsSkuDto.getEndDay())));
            }
            creditsSkuVO.setCurrentStock(creditsSkuDto.getCurrentStock());
            creditsSkuVO.setCurrentTotalStock(creditsSkuDto.getCurrentTotalStock());
            creditsSkuVO.setCostPrice(creditsSkuDto.getCostPrice());
            // 北现定制
            bjHyundai(app, creditsSkuVO, creditsSkuDto, marketingItemCreditsDto, itemKey);

            creditsSkuVOList.add(creditsSkuVO);
        }
        return creditsSkuVOList;
    }

    /**
     * 北现自定义支付方式
     *
     * @param creditsSkuVO
     * @param marketingItemCreditsDto
     */
    private void bjHyundai(AppSimpleDto app, CreditsSkuVO creditsSkuVO, CreditsSkuDto creditsSkuDto, MarketingItemCreditsDto marketingItemCreditsDto, ItemKeyDto itemKey) {
        if (!beiJinHyundaiConfig.isSelfVirtual(app.getId(), itemKey.isSelfAppItemMode(), itemKey.getItemType())) {
            return;
        }
        creditsSkuVO.setPureCredits(creditsSkuDto.getConfigPureCredits());
        String[] strings = creditsSkuDto.getAttributeValue().split(":");
        if (strings.length > 1) {
            creditsSkuVO.setValueId(Long.valueOf(strings[1]));
        }
        if (!marketingItemCreditsDto.getOriginalPriceBuy()) {
            creditsSkuVO.setSalePrice(null);
        }
        if (Objects.equals(marketingItemCreditsDto.getExchangeType(), 1)) {
            creditsSkuVO.setMarketingPrice(0L);
            creditsSkuVO.setMarketingCredits(0D);
        }
    }

    /**
     * 设置老sku积分信息，所有sku共用商品积分/价格/库存信息
     *
     * @param app
     * @param skuList
     * @param itemCreditsVO
     * @return
     */
    private List<CreditsSkuVO> getOldCreditsSkuVOs(AppSimpleDto app, List<AppItemSkuDto> skuList, CreditsSkuVO itemCreditsVO) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyList();
        }
        List<CreditsSkuVO> creditsSkuVOList = new ArrayList<>();
        for (AppItemSkuDto sku : skuList) {
            CreditsSkuVO creditsSkuVO = new CreditsSkuVO();
            creditsSkuVO.setSkuId(sku.getId());
            creditsSkuVO.setMarketingCredits(itemCreditsVO.getMarketingCredits());
            creditsSkuVO.setMarketingPrice(itemCreditsVO.getMarketingPrice());
            creditsSkuVO.setFacePrice(itemCreditsVO.getFacePrice());
            creditsSkuVO.setSalePrice(itemCreditsVO.getSalePrice());
            creditsSkuVO.setAttributeValue(MarketingSkuUtils.getAttributeValue(sku.getAttributeJson()));
            creditsSkuVO.setImgUrl(sku.getImgUrl());
            creditsSkuVO.setRemaining(itemCreditsVO.getRemaining());
            creditsSkuVO.setStartDay(itemCreditsVO.getStartDay());
            creditsSkuVO.setEndDay(itemCreditsVO.getEndDay());
            creditsSkuVO.setLeftDays(itemCreditsVO.getLeftDays());
            creditsSkuVO.setCurrentStock(itemCreditsVO.getCurrentStock());
            creditsSkuVO.setCurrentTotalStock(itemCreditsVO.getCurrentTotalStock());
            creditsSkuVOList.add(creditsSkuVO);
        }
        return creditsSkuVOList;
    }

    /**
     * 购物车，满包邮相关活动展示
     *
     * @param itemKeyDto
     * @param modelMap
     * @param app
     */
    private void hasMall(ItemKeyDto itemKeyDto, Map<String, Object> modelMap, AppSimpleDto app) {
        modelMap.put("hasMall", Boolean.FALSE);
        modelMap.put("showFreeShipping", Boolean.FALSE);
        //是否有购物车按钮
        if (newShoppingCartConfig.getAppIds().contains(app.getId())) {
            if (itemKeyDto.isV1() && ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType()) && !isJdItem(itemKeyDto)) {//新版本实物且不是京东商品允许加入购物车
                modelMap.put("hasMall", shoppingTralleryService.hasOpen(app));
            }
        } else if (itemKeyDto.isV1() && ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType()) && itemKeyDto.isSelfAppItemMode()) {
            modelMap.put("hasMall", shoppingTralleryService.hasOpen(app));
            //是否有满包邮活动
            FreeShippingDto freeShipping = shoppingTralleryService.getFreeShippingInfo(app.getDeveloperId());
            modelMap.put("showFreeShipping", freeShipping.getShowFreeShipping());
            modelMap.put("freeShippingLimit", freeShipping.getFreeShippingLimit());
            // 供应商满包邮
            setSupplierFullMall(itemKeyDto, modelMap, app, freeShipping);
        }

    }

    /**
     *  设置供应商满减包邮活动 匹配顺序 供应商满包邮，全局满包邮
     * @param itemKeyDto
     * @param modelMap
     * @param app
     * @param freeShipping
     */
    private void setSupplierFullMall(ItemKeyDto itemKeyDto, Map<String, Object> modelMap, AppSimpleDto app,FreeShippingDto freeShipping){
        if (!supplierFullMallService.isSupplierFullMall(app.getId())){
            return;
        }
        // 查询商品相关供应商满包邮信息
        List<SupplierFullMallItemDto> supplierFullMallInfoList = supplierFullMallService.getSupplierFullMallInfo(app.getId(), Lists.newArrayList(itemKeyDto.getAppItem().getId()));
        // 如果商品匹配供应商满包邮则命中供应商满包邮，如果开启全局满包邮则命中全局满包邮
        Boolean showFreeShipping = freeShipping.getShowFreeShipping();
        Long freeShippingLimit = freeShipping.getFreeShippingLimit();
        if (CollectionUtils.isNotEmpty(supplierFullMallInfoList)){
            showFreeShipping = true;
            freeShippingLimit = supplierFullMallInfoList.get(0).getDiscountSill();
        }
        modelMap.put("showFreeShipping", showFreeShipping);
        modelMap.put("freeShippingLimit", freeShippingLimit);
    }

    private boolean isJdItem(ItemKeyDto itemKeyDto) {
        long supplyType = SupplyTypeEnum.JD.getCode();
        boolean isJdItem = itemKeyDto.getItem() != null && Objects.equals(itemKeyDto.getItem().getDistributorId(), supplyType) && ItemDto.TypeObject.equals(itemKeyDto.getItem().getType());

        return isJdItem;
    }

    /**
     * 海底捞定制，需要商品数量
     *
     * @param itemKeyDto
     * @param modelMap
     * @return
     */
    private Map<String, Object> setSaleNum(ItemKeyDto itemKeyDto, Map<String, Object> modelMap) {
        if (itemKeyDto.getAppItem() == null
                || !remoteSaasGrantService.belongToHaiDiLaoDeveloper(RequestLocal.getConsumerAppDO().getDeveloperId())) {
            modelMap.put("saleNum", 0);
            return modelMap;
        }
        modelMap.put("saleNum", haidilaoService.getSaleNumWithCache(itemKeyDto.getAppItem().getId()));
        return modelMap;
    }


    /**
     * 是否有满减活动
     *
     * @param itemKeyDto
     * @param modelMap
     */
    private void hasPromotion(ItemKeyDto itemKeyDto, Map<String, Object> modelMap) {
        boolean hasPromotion = remoteSaasGrantService.belongToHaiDiLaoDeveloper(RequestLocal.getConsumerAppDO().getDeveloperId());
        if (hasPromotion && itemKeyDto.getAppItem() != null) {
            AppItemActivityRelationDto appItemActivityRelationDto =
                    remoteAppItemActivityRelationService.selectByItemId(itemKeyDto.getAppItem().getId());
            if (appItemActivityRelationDto != null) {
                MarketActivityDto marketActivityDto = remoteMarketActivityService.selectById(appItemActivityRelationDto.getMarketActivityId());
                //是否参加营销活动
                modelMap.put("hasMarketActivity", true);
                //对应的营销活动id
                modelMap.put("marketActivityId", marketActivityDto.getId());
                //营销折扣内容
                modelMap.put("discountContent", "满" + marketActivityDto.getDiscountSill() / 100 + "减" + marketActivityDto.getDiscountAmount() / 100 + "元");
            }
        }
    }

    //是否秒杀
    private boolean isSkill(ItemKeyDto itemKey) {
        //秒杀（限时可兑）
        if ((itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit))
                || (itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit))) {
            return true;
        }
        return false;
    }

    //是否是兑吧加钱购
    private boolean isDuibaAmb(ItemKeyDto itemKey) {
        ItemDto item = itemKey.getItem();
        if (itemKey.isDuibaAppItemMode() && item.isOpTypeItem(ItemDto.OpTypeIsAmb)) { // 在这两种都不为空的情况下
            return true;
        }
        return false;
    }

    /**
     * 构造按钮相关参数方法
     * note:添加了经纬度的入参
     *
     * @param consumer
     * @param app
     * @param itemKeyDto
     * @param type
     * @param buttonTest
     * @param credits
     * @param latitude
     * @param longitude
     * @return
     */
    private ExchangeButtonControlInfoVO getExchangeButtonControlInfoVO(ConsumerDto consumer, AppSimpleDto app, ItemKeyDto itemKeyDto, String type, String buttonTest, Long credits, Double latitude, Double longitude) {
        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
        if (type != null && "preview".equals(type)) {
            exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
            exchangeButtonControlInfoVO.setExchangeText("预览商品");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setLock(true);
        } else {
            exchangeButtonControlInfoVO = itemViewService.buildButtonControlInfo(itemKeyDto, consumer, app, exchangeButtonControlInfoVO,
                    buttonTest, credits, latitude, longitude);
        }
        return exchangeButtonControlInfoVO;
    }

    private void checkConsumerLabel(ConsumerDto consumer, AppSimpleDto app, ItemKeyDto itemKeyDto, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (!WhiteAccessUtil.matchWhiteList(app.getId(), ExtrangeBaseProcessor.WHITE_ACCESS_LABEL_KEY)) {
            return;
        }
        //白名单校验
        Long appItemId = ItemKeyUtil.getAppItemId(itemKeyDto);
        if (appItemId != null) {
            LabelPassGoodsFilterParam goodsFilterParam = new LabelPassGoodsFilterParamBuilder()
                    .setAppId(app.getId())
                    .setUserId(consumer.getPartnerUserId())
                    .setStrategyType(ConsumerLabelType.GOODS_FILTER.getType())
                    .setAppItemIds(ItemKeyUtil.getAppItemId(itemKeyDto))
                    .setCid(consumer.getId())
                    .builder();
            LabelPassResult result = remoteConsumerLabelService.isPass(goodsFilterParam);
            if (CollectionUtils.isEmpty(result.getPassAppItemId())) {
                exchangeButtonControlInfoVO.setExchangeText(exchangeText(app.getId(), result));
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setLock(true);
            }
        }
    }

    private String exchangeText(Long appId, LabelPassResult result) {
        if (commonConfig.getConsumerLableXinJiangAppIds().contains(appId)) {
            return "暂时无领取权限";
        }

        if (StringUtils.isNotBlank(result.getNotExchangeText())) {
            return result.getNotExchangeText();
        }
        return result.getSuccess() ? "非白名单内用户，不可领取" : "不可领取，请联系工作人员";
    }

    private void catLog(ItemKeyDto itemKeyDto) {
        try {
            if (ItemDto.TypeObject.equals(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsObjectDetail");
            } else if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsCouponDetail");
            } else if (ItemDto.TypeVirtual.equals(itemKeyDto.getItemDtoType())) {
                Cat.logMetricForCount("goodsVirtualDetail");
            }
        } catch (Exception e) {
            log.error("cat error", e);
        }
    }

    private ModelAndView getAppitemModelAndView(ConsumerDto consumer, AppSimpleDto app, ItemKeyDto itemKeyDto, String loginProgram, Map<String, Object> modelMap) {
        Map<String, Object> downloadConfig = qqMarketDownloadConfig(itemKeyDto.getAppItem());
        modelMap.put("qqMarketDownloadConfig", downloadConfig);
        String view = getGoodsView(itemKeyDto, app);
        ModelAndView model = new ModelAndView(view);
        if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType())) {
            if (!itemKeyDto.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                GoodsBatchDto batch = couponService.getGoodsUsingBatch(itemKeyDto);
                modelMap.put("batch", batch);
            }
        } else if (ItemDto.TypeVirtual.equals(itemKeyDto.getItemDtoType())) {
            setVirtualInfo(app, itemKeyDto, modelMap);
        }
        addCouponSeparateParamter(app, consumer, modelMap, itemKeyDto);
        buildReturnData(model, loginProgram, modelMap, getShareProgram(app.getId()));
        model.addObject("downloadConfig", downloadConfig);
        if (app.isColorSwitch()) {
            //全局色
            model.addObject("themeColor", app.getColor());
        }
        return model;
    }

    private void setVirtualInfo(AppSimpleDto app, ItemKeyDto itemKeyDto, Map<String, Object> modelMap) {
        if (!itemKeyDto.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
            PriceDegreeDto pd = new PriceDegreeDto(itemKeyDto.getAppItem().getCustomPrice());
            String degreeType = pd.isSingleDegree() ? "singleDegree" : "multiDegree";
            modelMap.put("degreeType", degreeType);
            modelMap.put("priceDegree", virtualDegree(app, pd.getCustomDegreeMap()));
        }
        DubboResult<AppItemExtraDto> result = remoteGoodsAppItemExtraService.findByAppItemId(itemKeyDto.getAppItem().getId());
        if (result.isSuccess() && result.getResult() != null) {
            AppItemExtraDto appItemExtraDto = result.getResult();
            JSONObject json = appItemExtraDto.getCustomPromptsJson();
            if (json.get(AppItemExtraDto.ACCOUNT_PROMPT) != null) {
                modelMap.put("accountPrompt",
                        json.get(AppItemExtraDto.ACCOUNT_PROMPT));
            }
            if (json.get(AppItemExtraDto.ACCOUNT_TITLE) != null) {
                modelMap.put("accountTitle",
                        json.get(AppItemExtraDto.ACCOUNT_TITLE));
            }
            if (json.get(AppItemExtraDto.ACCOUNT_FORMAT) != null) {
                modelMap.put("accountFormat",
                        json.getInteger(AppItemExtraDto.ACCOUNT_FORMAT));
            }
        }
        modelMap.put("isVirtualGoBindQQApp", AppIdConstant.isVirtualGoBindQQApp(app.getId()));
    }

    /**
     * 应用宝下载逻辑,appId=39935L  老逻辑-通过url上的download字段结合商品标签进行判断
     * <p>
     * appId = 61921 新逻辑 只要配置了商品标签 就需要进行解析
     *
     * @param appItem
     * @return
     */
    private Map<String, Object> qqMarketDownloadConfig(AppItemDto appItem) {
        Map<String, Object> config = new HashMap<>();
        config.put(QQMARKET_DOWNLOAD, false);
        if (appItem == null) {
            return config;
        }
        //应用宝下载逻辑，通过url传递download,通过商品配置appid,packagename
        String label = ExtraInfoUtils.getValue(appItem.getExtraInfo(), ExtraInfoUtils.LABEL);
        if (StringUtils.isBlank(label)) {
            return config;
        }
        String qqMarketDownLoad = RequestLocal.getRequest().getParameter(QQMARKET_DOWNLOAD);
        boolean isYingYongBaoNewAppIds = AppIdConstant.isYingYongBaoNewAppIds(appItem.getAppId());
        boolean download = Objects.equals(qqMarketDownLoad, "1") ||
                // 是新应用宝app并且downLoad参数不为0，前端会在下载成功后刷新手机端页面，会在链接上拼接一个download=0过来
                (isYingYongBaoNewAppIds && !Objects.equals(qqMarketDownLoad, "0"));
        config.put(QQMARKET_DOWNLOAD, download);
        if (!download) {
            return config;
        }
        try {
            String[] configArr = label.split("\\|");
            if (configArr.length != 2 && configArr.length != 4) {
                return config;
            }
            config.put(QQMARKET_PACKAGENAME, configArr[0]);
            config.put(QQMARKET_APPID, configArr[1]);

            // 新应用宝增加了两个参数 如果有配置 使用配置值  如果没有配置 使用默认值
            if (isYingYongBaoNewAppIds && configArr.length == 4) {
                config.put(QQMARKET_SCENE, configArr[2]);
                config.put(QQMARKET_SLOT_ID, configArr[3]);
            } else if (isYingYongBaoNewAppIds) {
                config.put(QQMARKET_SCENE, "30291");
                config.put(QQMARKET_SLOT_ID, "01_001");
            }

            config.put(QQMARKET_DOWNLOAD, true);
        } catch (Exception e) {
            log.info("应用宝商品label解析错误, label={}", label, e);
            config.put(QQMARKET_DOWNLOAD, false);
        }
        return config;
    }

    private Map<String, Object> addViLimitViewInfoVO(Map<String, Object> modelMap, VipLimitViewInfoVO vipLimitViewInfoVO) {
        modelMap.put("limitRemark", vipLimitViewInfoVO.getLimitRemark());
        modelMap.put("isCanConsumerExchange", vipLimitViewInfoVO.getIsCanConsumerExchange());
        modelMap.put("ruleUrl", vipLimitViewInfoVO.getRuleUrl());
        modelMap.put("ifHidden", vipLimitViewInfoVO.getIfHidden());
        modelMap.put("isVipLimit", vipLimitViewInfoVO.isVipLimit());
        modelMap.put("vipFlag", vipLimitViewInfoVO.getVipFlag());
        modelMap.put("vipGoodsIcon", vipLimitViewInfoVO.getVipGoodsIcon());
        modelMap.put("upgradeUrl", vipLimitViewInfoVO.getUpgradeUrl());
        modelMap.put("entryText", vipLimitViewInfoVO.getEntryText());
        modelMap.put("consumerLimitRemark", vipLimitViewInfoVO.getConsumerLimitRemark());
        modelMap.put("vipRuleUrl", vipLimitViewInfoVO.getVipRuleUrl());
        modelMap.put("extJson", vipLimitViewInfoVO.getExtJson());
        modelMap.put("hiddenVipLimitSwitch", vipLimitViewInfoVO.getHiddenVipLimitSwitch());
        modelMap.put("vipLimitNum", vipLimitViewInfoVO.getVipLimitNum());
        return modelMap;
    }

    private Map<String, Object> addExchangeButtonControlInfoVO(Map<String, Object> modelMap,
                                                               ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        modelMap.put("exchangeEnable", exchangeButtonControlInfoVO.getExchangeEnable());
        modelMap.put("exchangeStatus", exchangeButtonControlInfoVO.getExchangeStatus());
        modelMap.put("exchangeText", exchangeButtonControlInfoVO.getExchangeText());
        modelMap.put("lock", exchangeButtonControlInfoVO.getLock());
        modelMap.put("exchangeButtonControlInfoVO", exchangeButtonControlInfoVO);
        return modelMap;
    }

    private Map<String, Object> buildItemDetailEmbedInfo(Long appId, Long consumerId, ItemKeyDto key, String ip,
                                                         String os) {
        Map<String, Object> embedMap = Maps.newHashMap();
        embedMap.put("app_id", appId);
        embedMap.put("consumer_id", consumerId);
        embedMap.put("login_type", appId);// 原文既是appid
        embedMap.put("button_type", "80001");//兑换详情页马上兑换
        if (key.isSelfAppItemMode()) {
            embedMap.put("info_type", 18);
            if (key.getAppItem() != null) {
                embedMap.put("info", key.getAppItem().getId());
                embedMap.put("app_item_id", key.getAppItem().getId());
            }
        } else {
            embedMap.put("info_type", 1);
            if (key.getItem() != null) {
                embedMap.put("info", key.getItem().getId());
                embedMap.put("item_id", key.getItem().getId());
            }
            if (key.getAppItem() != null) {
                embedMap.put("app_item_id", key.getAppItem().getId());
            }
        }
        if (!(Environment.isAliyun() || Environment.isDaily())) {
            embedMap.put("isEmbed", false);
        }
        embedMap.put("ip", ip);
        embedMap.put("os", os);
        return embedMap;
    }

    /**
     * @param request
     * @param response
     * @return JsonRender
     */
    @RequestMapping("/orderStatusQuery")
    @ApiOperation(value = "查询订单状态", notes = "查询订单状态", httpMethod = "GET")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "orderId", value = "orderId", dataType = "Long", required = true, paramType = "query")})
    @ResponseBody
    public JsonRender orderStatusQuery(HttpServletRequest request, HttpServletResponse response) {
        String orderId = request.getParameter("orderId");
        String domain = request.getHeader("Origin");
        if (domain != null && domain.contains(CrossDomainHelper.getCrossDomain())) {
            // 设置允许跨域
            DomainCrossUtils.crossDomain(request, response, domain);
        }
        if (StringUtils.isBlank(orderId) || !StringUtils.isNumeric(orderId)) {
            JSONObject json = new JSONObject();
            json.put(STATUS, "fail");
            json.put(MESSAGE, "错误的订单号：" + orderId);
            return JsonRender.successResult(json);
        }
        OrdersDto ord = remoteConsumerOrderSimpleService.findById(Long.valueOf(orderId), RequestLocal.getCid()).getResult();
        if (ord == null) {
            JSONObject json = new JSONObject();
            json.put(STATUS, "fail");
            json.put(MESSAGE, "订单不存在");
            return JsonRender.successResult(json);
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            JSONObject json = new JSONObject();
            json.put(STATUS, "fail");
            json.put(MESSAGE, "无权访问");
            return JsonRender.successResult(json);
        }

        if (OrdersDto.FlowworkStageOrderCreateStart.equals(ord.getFlowworkStage())) {
            JSONObject json = new JSONObject();
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
            return JsonRender.successResult(json);
        } else if ("Create-tofail".equals(ord.getFlowworkStage()) || "Fail-started".equals(ord.getFlowworkStage())
                || "Fail-complete".equals(ord.getFlowworkStage())) {
            JSONObject json = new JSONObject();
            json.put(STATUS, "fail");
            String message = StringUtils.isEmpty(ord.getError4Consumer()) ? "兑换失败！" : ord.getError4Consumer();
            json.put(MESSAGE, message);
            return JsonRender.successResult(json);
        } else {
            JSONObject json = new JSONObject();
            json.put(STATUS, SUCCESS);
            json.put(MESSAGE, "兑换提交成功！待处理");
            return JsonRender.successResult(json);
        }
    }

    /**
     * @param request
     * @param response
     * @return JsonRender
     */
    @RequestMapping("/orderAmbStatusQuery4Pptv")
    @ApiOperation(value = "查询订单状态", notes = "查询订单状态", httpMethod = "GET")
    @ApiImplicitParams(value = {@ApiImplicitParam(name = "orderId", value = "orderId", dataType = "Long", required = true, paramType = "query")})
    @ResponseBody
    public JsonRender orderAmbStatusQuery4Pptv(HttpServletRequest request, HttpServletResponse response) {
        String domain = request.getHeader("Origin");
        if (domain != null && domain.contains(CrossDomainHelper.getCrossDomain())) {
            // 设置允许跨域
            DomainCrossUtils.crossDomain(request, response, domain);
        }

        Long orderId = Long.parseLong(request.getParameter("orderId"));
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        OrdersDto ord = ords.getResult();
        if (ord == null) {
            return JsonRender.failResult("订单不存在");
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            return JsonRender.failResult("无权访问");
        }
        Boolean isAmb = false;
        JSONObject json = new JSONObject();
        if (ord.getConsumerPayPrice() > 0) {
            isAmb = true;
        }
        if (OrdersDto.FlowworkStageOrderCreateStart.equals(ord.getFlowworkStage())) {
            json.put("isAmb", isAmb);
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
        } else if ("Create-tofail".equals(ord.getFlowworkStage()) || "Fail-started".equals(ord.getFlowworkStage())
                || "Fail-complete".equals(ord.getFlowworkStage())) {
            json.put(STATUS, "fail");
            String message = StringUtils.isEmpty(ord.getError4Consumer()) ? "兑换失败！" : ord.getError4Consumer();
            json.put(MESSAGE, message);
            json.put("kind", "fail");
        } else if (OrdersDto.StatusConsumeSuccess.equals(ord.getStatus())) {
            json.put(STATUS, "consume_success");
            json.put("isAmb", isAmb);
            json.put(MESSAGE, "兑换成功！");
            json.put("kind", "consume_success");
        } else if (OrdersDto.StatusSuccess.equals(ord.getStatus())) {
            // kind=success参数用于兑换完后显示文案，恭喜兑换成功。
            json.put(STATUS, SUCCESS);
            json.put(MESSAGE, "兑换成功！");
            json.put("kind", SUCCESS);
        } else if (OrdersDto.AuditStatusWait.equals(ord.getAuditStatus())) {
            json.put(STATUS, SUCCESS);
            json.put(MESSAGE, "兑换成功！待审批");
            json.put("kind", SUCCESS);
        } else {
            json.put("isAmb", isAmb);
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
        }
        JsonRender render = JsonRender.successResult();
        return render.addResult("data", json);
    }

    /**
     * taglib转为类私有方法
     *
     * @throws
     * <AUTHOR>
     * @modified
     * @datetime 2015年8月25日 下午4:30:57
     */
    private String getRemainText(ItemKeyDto itemKey, Long stock) {
        try {
            if (goodsItemTimeLimitService.timeLimitSwitch(itemKey) && !goodsItemTimeLimitService.canTakeOrder(itemKey)) {
                return "已兑完";
            }
        } catch (Exception e) {
            log.error("", e);
        }
        Integer remaining = 0;
        if (itemKey.getAppItem() != null && itemKey.isSelfAppItemMode()) {
            if (itemKey.getItemDtoType().equals(ItemDto.TypeTurntable)
                    || itemKey.getItemDtoType().equals(ItemDto.TypeSingleLottery)
                    || itemKey.getItemDtoType().equals(ItemDto.TypeManualLottery)
                    || itemKey.getItemDtoType().equals(ItemDto.TypeHdtollLottery)) {
                return "无限";
            }
            remaining = itemKey.getAppItem().getRemaining();
        }
        if (itemKey.getItem() != null
                && (ItemDto.TypeCoupon.equals(itemKey.getItemDtoType()) || ItemDto.TypeObject.equals(itemKey.getItemDtoType()))) {
            remaining = itemKey.getItem().getRemaining();
            // 如果是定向商品或预分配商品,调用查库存
            if (remaining > 0
                    && (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeSpecify) || itemKey.getItem().isOpTypeItem(ItemDto.OpTypePreStockSwith))) {
                remaining = stock.intValue();
            }
        }
        return getFormatRemaining(remaining);
    }

    public static String getFormatRemaining(Integer remaining) {
        if (remaining <= 0) {
            return "已兑完";
        } else if (remaining >= 100) {
            return "抢兑中";
        } else {
            return "少量";
        }
    }

    /**
     * @param request
     * @param consumer
     * @param itemKeyDto
     * @param app
     * @param developer
     * @return ModelAndView
     */
    public ModelAndView couponObjectDetail(HttpServletRequest request, ConsumerDto consumer, ItemKeyDto itemKeyDto,
                                           AppSimpleDto app, DeveloperDto developer, HttpServletResponse response) {//NOSONAR
        boolean preview = "preview".equals(request.getParameter("type"));
        String appItemId = request.getParameter("appItemId");
        // 部分展示信息的封装
        CItemVO citem = consumerItemRenderService.getCItemVO(itemKeyDto, app);
        setExpressPrice(consumer, citem);
        String ip = RequestTool.getIpAddr(request);
        String osType = RequestTool.getOSNew(RequestLocal.getRequest());
        ItemDto item = itemKeyDto.getItem();
        AppItemDto appItem = itemKeyDto.getAppItem();
        // 分享功能用的content
        String shareContent = shareContentService.getItemShareContent(itemKeyDto, citem, request.getScheme(), app);
        boolean isShareOpen = app.isAppSwitch(AppSimpleDto.SwitchOpenShare);
        String loginProgram = remoteAppExtraServiceNew.findCallLoginProgramByAppId(app.getId()).getResult();
        // 构造埋点信息
        Map<String, Object> embedInfo = buildItemDetailEmbedInfo(app.getId(), consumer.getId(), itemKeyDto, ip, osType);
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        Map<String, Object> modelMap = new JSONObject();
        //营销类信息
        if (itemKeyDto.isV1() && itemKeyDto.getAppItem() != null) {
            SecKillActConfVO secKillActConfVO = actConfService.getCachedSeckillActConfWithUserData(itemKeyDto.getAppItem().getId());
            if (ActConfServiceImpl.beforeSecKillStart30Min(secKillActConfVO)) {//秒杀开始前30分钟强制跳转到秒杀页面
                sendRedirect(response, UrlUtils.addScheme(domainConfigDto.getSeckillDomain() + ActConfServiceImpl.SECKILL_URL + secKillActConfVO.getId()));
                return null;
            }
            modelMap.put("secKillActConf", secKillActConfVO);
        }

        if (itemKeyDto.isV1() || commonConfig.isNewGoodsDetailApp(app)) {//增加sku
            buildV1SkuInfo(itemKeyDto, citem, app, modelMap, preview, consumer);
        }
        replaceDomain(domainConfigDto, itemKeyDto);
        replaceDomain(domainConfigDto, citem);
        Pair<Boolean, String> accountLimitInfo = getAccountLimitInfo(itemKeyDto);
        modelMap.put("accountSource", request.getParameter("accountSource"));
        modelMap.put("accountLimit", accountLimitInfo.getKey());
        modelMap.put("accountLimitDesc", accountLimitInfo.getValue());
        modelMap.put("poweredBy", !app.isAppSwitch(AppSimpleDto.SwitchPoweredBy));
        modelMap.put("shareContent", shareContent);
        modelMap.put("isShareOpen", isShareOpen);
        modelMap.put("geeType", GoodsTypeEnum.DUIBA.getGtype());
        modelMap.put("stInfo", EmdCommonUtil.emdExporFromMap(embedInfo, domainConfigDto.getEmbedDomain()));
        modelMap.put("itemKey", itemKeyDto);
        modelMap.put("itemType", itemKeyDto.isAmbItemKey());// 是否加钱购
        modelMap.put("consumer", consumer);
        //判断 购物车 满包邮
        hasMall(itemKeyDto, modelMap, app);
        modelMap.put("app", this.getAppWithEarnCreditsLetter(app,consumer));
        modelMap.put("citem", citem);
        modelMap.put("title", citem.getTitle());
        modelMap.put("over", false);
        modelMap.put("appClient", domainConfigDto.getAppDomain());
        modelMap.put("isLogin", !(consumer.isNotLoginUser()));
        modelMap.put("isWeibo", AppIdConstant.isWeibo(app.getId()));
        modelMap.put("isDownloadUrlShowText", AppIdConstant.isDownloadUrlShowText(app.getId()));
        modelMap.put("openLogin",
                app.isAppSwitch(AppSimpleDto.SwitchUseLoginCode) && StringUtils.isNotBlank(loginProgram));
        modelMap.put("recommendDomain", domainConfigDto.getActivityDomain());
        modelMap.put("vipLevel", consumer.getVipLevel());
        modelMap.put("vipFlag", Boolean.FALSE);

        // 设置兑换来源信息
        setSourceInfo(modelMap, request, app, itemKeyDto, citem);

        // 虚拟商品,输入账号开关
        setVirtualInputAccount(modelMap, itemKeyDto);

        //京东描述字段很大，清除前端没用到的字段
        clearDesc(itemKeyDto);

        // 印鸽虚拟商品定制
        yingeVirtualHandle(app.getId(),itemKeyDto,modelMap);


        //判断是否支持混合支付
        if (itemKeyDto.getAppItem() != null) {
            canMixPay(itemKeyDto.getAppItem().getId(), modelMap, app, consumer);
        }
        // 加入分享代码
        modelMap.put("isHdShareOpen", app.isAppSwitch(AppSimpleDto.SwitchShareProgramType));
        // 优惠券需批次信息
        setBatch(itemKeyDto, app, modelMap);
        boolean ismoney = app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint);
        if (ItemDto.TypePhonebillDingzhi.equals(item.getType())) {
            boundPhone(modelMap, app, consumer);
            modelMap.put("unit", ismoney ? "金额" : app.getUnitName());
            modelMap.put("unitA", ismoney ? "元" : app.getUnitName());
        }
        String buttonTest = "马上兑换";
        if (appItem != null) {
            buttonTest = ExtraInfoUtils.getValue(appItem.getExtraInfo(), ExtraInfoUtils.BUTTON_TEXT);
        }

        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
        exchangeButtonControlInfoVO = itemViewService.buildButtonControlInfo(itemKeyDto, consumer, app,
                exchangeButtonControlInfoVO, buttonTest, citem.getCredits(), null, null);
        //摩拜单车定制
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        modelMap.put("isMobike", isMoBike);
        if (preview) {
            ModelAndView view = previewCouponAndObject(modelMap, appItem, app, item, consumer, itemKeyDto, domainConfigDto.getCdnDomain());
            return view;
        }
        VipLimitViewInfoVO vipLimitViewInfoVO = goodsItemVipLimitService.buildViPlimitViewInfo(itemKeyDto, consumer,
                app, domainConfigDto);
        //会员商品信息
        if (itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeVipGoods)) {
            vipGoodsService.buildVipGoodsInfo(itemKeyDto, citem, app, consumer, exchangeButtonControlInfoVO, vipLimitViewInfoVO);
        }
        checkConsumerLabel(consumer, app, itemKeyDto, exchangeButtonControlInfoVO);
        // 兑换按钮定制
        exchangeTextByCustomized(consumer,itemKeyDto,app,exchangeButtonControlInfoVO);
        // 标签兑换限制最低优先级
        checkAppItemLimitTag(consumer, Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getId).orElse(null), app, exchangeButtonControlInfoVO, null);
        // 组装返回信息
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        replaceDomain(domainConfigDto, vipLimitViewInfoVO);
        // 会员等级兑换限制
        addViLimitViewInfoVO(modelMap, vipLimitViewInfoVO);
        Long minCredits = 0L;
        // 是否为赠品
        boolean isFree = OrderSourceTypeEnum.ORDER_BONUS.getCode().equals(modelMap.get("sourceType"));
        if (!isFree) {
            // 是否支持0积分配置
            Boolean canZeroCredits = WhiteAccessUtil.matchWhiteList(app.getId(), WhiteListUniqueKeyConstants.CUSTOM_ZERO_CREDIT_WHITELIST_KEY);
            DubboResult<Long> cret;
            if (canZeroCredits) {
                cret = remotePreStockService.calculateCreditsByItemKeyAndDegreeForFree(itemKeyDto,
                        null,
                        app.getCreditsRate().longValue());
            } else {
                cret = remotePreStockService.calculateCreditsByItemKeyAndDegree(itemKeyDto,
                        null,
                        app.getCreditsRate().longValue());
            }
            if (!cret.isSuccess()) {
                throw new GoodsWebException(cret.getMsg());
            }
            minCredits = cret.getResult();
        }
        // 下架的商品
        if (item.getDuibaType() == 0 && !item.getAutoRecommend()
                && (appItem == null || !item.getEnable() || appItem.getDeleted() || appItem.getStatus().equals(AppItemDto.StatusOff))) {
            ModelAndView modelAndView = zhichongOff(consumer, itemKeyDto, app, item, appItem, loginProgram, modelMap, vipLimitViewInfoVO, minCredits, domainConfigDto);
            if(modelAndView == null){
                throw new GoodsWebException(ErrorCode.********.getDesc());
            }
            return modelAndView;
        }
        // 拼团活动信息
        setGroupBuyInfo(app, request, consumer.getId(), itemKeyDto, modelMap);
        //央视频公益会员身份判断定制
        handleYangshiPin(modelMap, RequestLocal.getAppId(), itemKeyDto.getAppItem(), RequestLocal.getConsumerDO());
        //来伊份定制
        setLaiYiFenField(modelMap,RequestLocal.getAppId(),appItemId);
        // 花西子定制
        setHuaXiZiField(modelMap,RequestLocal.getAppId(),itemKeyDto);

        // todo 万达定制
        setWandaField(modelMap,app,itemKeyDto);
        // 功能权限
        modelMap.put("authorityVO", getUnitAuthorityVO());
        addCouponSeparateParamter(app, consumer, modelMap, itemKeyDto);
        modelMap.put("canExchangeMJVip", canExchangeMJVipNew(appItem, exchangeButtonControlInfoVO));
        ModelAndView model = limit(consumer, itemKeyDto, app, ip, item, loginProgram, modelMap, ismoney, exchangeButtonControlInfoVO);
        if (model != null) {
            return model;
        }
        return getModelAndView(app, itemKeyDto, loginProgram, modelMap);
    }

    private void replaceDomain(DomainConfigDto systemDomain, ItemKeyDto itemKeyDto) {
        AppItemDto appItem = itemKeyDto.getAppItem();
        if (appItem != null) {
            appItem.setLogo(ReplaceCdnUtil.replaceCdn(systemDomain, appItem.getLogo()));
            appItem.setSmallImage(ReplaceCdnUtil.replaceCdn(systemDomain, appItem.getSmallImage()));
            appItem.setImage(ReplaceCdnUtil.replaceCdn(systemDomain, appItem.getImage()));
            appItem.setMultiImage(ReplaceCdnUtil.replaceCdn(systemDomain, appItem.getMultiImage()));
            appItem.setWhiteImage(ReplaceCdnUtil.replaceCdn(systemDomain, appItem.getWhiteImage()));
            ItemDescConfigDto itemDescConfigDto = appItem.getItemDescConfigDto();
            if (itemDescConfigDto != null) {
                itemDescConfigDto.setMultiImage(ReplaceCdnUtil.replaceCdn(systemDomain, itemDescConfigDto.getMultiImage()));
            }
        }

        ItemDto item = itemKeyDto.getItem();
        if (item != null) {
            item.setImage(ReplaceCdnUtil.replaceCdn(systemDomain, item.getImage()));
            item.setSmallImage(ReplaceCdnUtil.replaceCdn(systemDomain, item.getSmallImage()));
            item.setMultiImage(ReplaceCdnUtil.replaceCdn(systemDomain, item.getMultiImage()));
            item.setLogo(ReplaceCdnUtil.replaceCdn(systemDomain, item.getLogo()));
            item.setWhiteImage(ReplaceCdnUtil.replaceCdn(systemDomain, item.getWhiteImage()));
            ItemDescConfigDto itemDescConfigDto = item.getItemDescConfigDto();
            if (itemDescConfigDto != null) {
                itemDescConfigDto.setMultiImage(ReplaceCdnUtil.replaceCdn(systemDomain, itemDescConfigDto.getMultiImage()));
            }
        }
    }

    private void replaceDomain(DomainConfigDto systemDomain, CItemVO citem) {
        if (citem == null) {
            return;
        }
        citem.setImage(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getImage()));
        citem.setMultiImage(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getMultiImage()));
        citem.setSmallImage(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getSmallImage()));
        citem.setDescription(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getDescription()));
        citem.setLogo(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getLogo()));
        citem.setBannerImage(ReplaceCdnUtil.replaceCdn(systemDomain, citem.getBannerImage()));
    }

    private void replaceDomain(DomainConfigDto systemDomain, VipLimitViewInfoVO vipLimitViewInfoVO) {
        vipLimitViewInfoVO.setVipGoodsIcon(ReplaceCdnUtil.replaceCdn(systemDomain, vipLimitViewInfoVO.getVipGoodsIcon()));
    }

    private void setVirtualInputAccount(Map<String, Object> modelMap, ItemKeyDto itemKey) {
        if (!ItemDto.TypeVirtual.equals(itemKey.getItemDtoType())) {
            return;
        }
        AppItemDto appItemDto = itemKey.getAppItem();
        if (Objects.isNull(appItemDto) || !appItemDto.isOpTypeAppItem(ItemDto.OpTypeNeedUserName)) {
            return;
        }
        VirtualInputAccountBo bo = new VirtualInputAccountBo();
        modelMap.put("virtualInputAccount", bo);
        try {
            bo.setVirtualInputShow(1);
            AppItemExtraDto dto = remoteGoodsAppItemExtraService.findByAppItemId(appItemDto.getId()).getResult();
            if (Objects.isNull(dto)) {
                return;
            }
            JSONObject object = dto.getCustomPromptsJson();

            String prompt = object.getString(AppItemExtraDto.ACCOUNT_PROMPT);
            String title = object.getString(AppItemExtraDto.ACCOUNT_TITLE);
            Integer format = object.getInteger(AppItemExtraDto.ACCOUNT_FORMAT);

            if (StringUtils.isNotBlank(prompt)) {
                bo.setVirtualInputPlaceholderText(prompt);
            }
            if (StringUtils.isNotBlank(title)) {
                bo.setVirtualInputTitle(title);
            }
            if (format != null) {
                bo.setVirtualInputFormat(format);
            }
        } catch (Exception e) {
            log.info("查询虚拟商品提示信息错误", e);
        }
    }

    private void clearDesc(ItemKeyDto itemKeyDto) {
        if (itemKeyDto != null) {
            if (itemKeyDto.getAppItem() != null) {
                itemKeyDto.getAppItem().setDescription("");
                ItemDescConfigDto itemDescConfigDto = itemKeyDto.getAppItem().getItemDescConfigDto();
                if (itemDescConfigDto != null) {
                    itemDescConfigDto.setDescription("");
                }
            }
            if (itemKeyDto.getItem() != null) {
                itemKeyDto.getItem().setDescription("");
                ItemDescConfigDto itemDescConfigDto = itemKeyDto.getItem().getItemDescConfigDto();
                if (itemDescConfigDto != null) {
                    itemDescConfigDto.setDescription("");
                }
            }
        }
    }

    private void setExpressPrice(ConsumerDto consumer, CItemVO citem) {
        if (ItemDto.ExpressTypeTemplate.equals(citem.getExpressType())) {
            citem.setExpressPrice(ambExpressTemplateService.matchExpressPrice(consumer.getId(),
                    citem.getExpressTemplateId()));
        }
    }

    private void setBatch(ItemKeyDto itemKeyDto, AppSimpleDto app, Map<String, Object> modelMap) {
        if (ItemDto.TypeCoupon.equals(itemKeyDto.getItemDtoType()) && !itemKeyDto.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
            GoodsBatchDto batch = couponService.getGoodsUsingBatch(itemKeyDto);
            modelMap.put("batch", batch);
        }
    }

    private ModelAndView limit(ConsumerDto consumer, ItemKeyDto itemKeyDto, AppSimpleDto app, String ip, ItemDto item, String loginProgram, Map<String, Object> modelMap, boolean ismoney, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        // 日预算，月预算，余额验证
        if (item.getActualPrice() > 0) {
            AppBudgetDto appBudgetDto = remoteAppServiceNew.getAppBudget(app.getId()).getResult();
            checkBudget(itemKeyDto, appBudgetDto, exchangeButtonControlInfoVO);

            if (!exchangeButtonControlInfoVO.getExchangeEnable()) {
                //应用宝定制文案
                YingYongBaoBo.setCustomizeButtonText(app, itemKeyDto, exchangeButtonControlInfoVO);
                addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
                return getModelAndView(app, itemKeyDto, loginProgram, modelMap);
            }
        }
        //应用宝定制文案
        YingYongBaoBo.setCustomizeButtonText(app, itemKeyDto, exchangeButtonControlInfoVO);
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);

        if (itemKeyDto.isV1()) {//新版积分前端判断
            return null;
        }

        // 是否为赠品
        boolean isFree = OrderSourceTypeEnum.ORDER_BONUS.getCode().equals(modelMap.get("sourceType"));
        Long minCredit = 0L;
        if (!isFree) {
            // 积分不足
            // 是否支持0积分配置
            Boolean canZeroCredits = WhiteAccessUtil.matchWhiteList(app.getId(), WhiteListUniqueKeyConstants.CUSTOM_ZERO_CREDIT_WHITELIST_KEY);
            DubboResult<Long> cret;
            if (canZeroCredits) {
                cret = remotePreStockService.calculateCreditsByItemKeyAndDegreeForFree(itemKeyDto,
                        null,
                        app.getCreditsRate().longValue());
            } else {
                cret = remotePreStockService.calculateCreditsByItemKeyAndDegree(itemKeyDto,
                        null,
                        app.getCreditsRate().longValue());
            }
            if (!cret.isSuccess()) {
                throw new GoodsWebException(cret.getMsg());
            }
            minCredit = cret.getResult();
        }
        if (consumer.getCredits() < minCredit) {
            modelMap.put("exchangeEnable", false);
            if (ismoney) {
                modelMap.put("exchangeText", "金额不足");
            } else {
                modelMap.put("exchangeText", app.getUnitName() + "不足");
            }
            modelMap.put("exchangeStatus", GoodsItemStatusService.IS_LOW_CREDITS);


            // 积分不足时，判断是否开启了已抢光按钮,且在可兑换范围内
            if (goodsItemTimeLimitService.timeLimitSwitch(itemKeyDto)
                    && goodsItemTimeLimitService.canTakeOrder(itemKeyDto)) {
                modelMap.put("over", true);
            }

            return getModelAndView(app, itemKeyDto, loginProgram, modelMap);
        }
        return null;
    }

    private ModelAndView getModelAndView(AppSimpleDto app, ItemKeyDto itemKey, String loginProgram, Map<String, Object> modelMap) {
        modelMap.put("cfg", detailCfg(modelMap, app));
        Map<String, Object> downloadConfig = qqMarketDownloadConfig(itemKey.getAppItem());
        modelMap.put("qqMarketDownloadConfig", downloadConfig);
        String view = getGoodsView(itemKey, app);
        if (ItemDto.TypePhonebillDingzhi.equals(itemKey.getItem().getType())) {
            view = "consumer/mobile/phonebilldetailDingzhi";
        }
        ModelAndView model = new ModelAndView(view);
        buildReturnData(model, loginProgram, modelMap, getShareProgram(app.getId()));
        model.addObject("downloadConfig", downloadConfig);
        return model;
    }

    @NotNull
    private String getGoodsView(ItemKeyDto itemKey, AppSimpleDto app) {
        String view;
        if (ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())) {
            view = "consumer/mobile/refactor/coupondetail";
            if (!itemKey.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                view = "consumer/mobile/coupondetail";
            } else if (itemKey.isCryptoCard()) {
                view = "consumer/mobile/refactor/carddetail";
            }
        } else if (ItemDto.TypeObject.equals(itemKey.getItemDtoType())) {
            view = "consumer/mobile/refactor/objectdetail";
            if (!itemKey.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                view = "consumer/mobile/objectdetail";
            }
        } else if (ItemDto.TypeVirtual.equals(itemKey.getItemDtoType())) {
            view = "consumer/mobile/refactor/virtualdetail";
            if (!itemKey.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                view = "consumer/mobile/virtual";
            }
        } else {
            view = ERROR;
        }
        return view;
    }

    private RemainingMoneyDto getConsumerRemainingMoneyDO() {
        RemainingMoneyDto remainingMoney = appSimpleQueryService.findRemainingMoneyByDeveloperIdWithCache(RequestLocal.getConsumerAppDO().getDeveloperId());
        if (remainingMoney == null) {
            remainingMoney = new RemainingMoneyDto();
            remainingMoney.setMoney(0);
        }
        return remainingMoney;
    }

    /**
     * @param request
     * @param consumer
     * @param itemKeyDto
     * @param app
     * @param developer
     * @return ModelAndView
     */
    public ModelAndView zhichongDetail(HttpServletRequest request, ConsumerDto consumer, ItemKeyDto itemKeyDto,
                                       AppSimpleDto app, DeveloperDto developer) {
        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
        String appItemId = request.getParameter("appItemId");
        String limitTip = null;
        // 部分展示信息的封装
        CItemVO citem = consumerItemRenderService.getCItemVO(itemKeyDto, app);
        ItemDto item = itemKeyDto.getItem();
        AppItemDto ai = itemKeyDto.getAppItem();
        // 分享功能用的content
        String shareContent = shareContentService.getItemShareContent(itemKeyDto, citem, request.getScheme(), app);
        boolean isShareOpen = app.isAppSwitch(AppSimpleDto.SwitchOpenShare);
        String loginProgram = remoteAppExtraServiceNew.findCallLoginProgramByAppId(app.getId()).getResult();
        Map<String, Object> modelMap = new HashMap<>();
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        VipLimitViewInfoVO vipLimitViewInfoVO = goodsItemVipLimitService.buildViPlimitViewInfo(itemKeyDto, consumer,
                app, domainConfigDto);
        replaceDomain(domainConfigDto, vipLimitViewInfoVO);
        // 会员等级兑换限制
        addViLimitViewInfoVO(modelMap, vipLimitViewInfoVO);
        //会员商品信息
        if (itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeVipGoods)) {
            //万达定制
            if (wandaConfig.ifWandaApp(consumer.getAppId())) {
                VipGoodsConfigDto vipGoods = remoteVipGoodsConfigService.findByAppItemId(itemKeyDto.getAppItem().getId());
                modelMap.put("extJson", vipGoods.getExtJson());
            }
        }
        // 设置兑换来源相关信息
        setSourceInfo(modelMap, request, app, itemKeyDto, citem);

        // 是否为赠品
        boolean isFree = OrderSourceTypeEnum.ORDER_BONUS.getCode().equals(modelMap.get("sourceType"));
        Long minCredits = 0L;
        if (!isFree) {
            DubboResult<Long> cret = remotePreStockService.calculateCreditsByItemKeyAndDegree(itemKeyDto,
                    null,
                    app.getCreditsRate().longValue());
            if (!cret.isSuccess()) {
                throw new GoodsWebException(cret.getMsg());
            }
            minCredits = cret.getResult();
        }

        String ip = RequestTool.getIpAddr(request);
        String osType = RequestTool.getOSNew(RequestLocal.getRequest());
        // 构造埋点信息
        Map<String, Object> embedInfo = buildItemDetailEmbedInfo(app.getId(),
                consumer.getId(),
                itemKeyDto,
                ip,
                osType);
        modelMap.put("stInfo", EmdCommonUtil.emdExporFromMap(embedInfo, domainConfigDto.getEmbedDomain()));

        modelMap.put("recommendDomain", domainConfigDto.getActivityDomain());
        if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
            modelMap.put("isRmbOpen", true);
        } else {
            modelMap.put("isRmbOpen", false);
        }
        replaceDomain(domainConfigDto, itemKeyDto);
        replaceDomain(domainConfigDto, citem);
        modelMap.put("poweredBy", !app.isAppSwitch(AppSimpleDto.SwitchPoweredBy));
        modelMap.put("isShareOpen", isShareOpen);
        modelMap.put("shareContent", shareContent);
        modelMap.put("citem", citem);
        modelMap.put("title", citem.getTitle());
        modelMap.put("consumer", consumer);
        modelMap.put("itemKey", itemKeyDto);
        modelMap.put("app", this.getAppWithEarnCreditsLetter(app,consumer));
        modelMap.put("limitTip", limitTip);
        modelMap.put("geeType", GoodsTypeEnum.DUIBA.getGtype());
        modelMap.put("isLogin", !(consumer.isNotLoginUser()));
        modelMap.put("openLogin",
                app.isAppSwitch(AppSimpleDto.SwitchUseLoginCode) && StringUtils.isNotBlank(loginProgram));
        modelMap.put("consumerCreditsShow",
                userCreditsShowDeal(consumer.getCredits(), app));
        boolean ismoney = app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint);
        modelMap.put("unit", ismoney ? "金额" : app.getUnitName());
        modelMap.put("unitA", ismoney ? "元" : app.getUnitName());
        modelMap.put("minCredits", minCredits);
        modelMap.put("minCreditsShow", PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(minCredits, app));
        modelMap.put("showCredits", AppIdConstant.showCredits(app.getId()));
        modelMap.put("vipLevel", consumer.getVipLevel());
        modelMap.put("vipFlag", Boolean.FALSE);
        modelMap.put("accountSource", request.getParameter("accountSource"));
        //判断是否支持混合支付
        canMixPay(itemKeyDto.getAppItem().getId(), modelMap, app, consumer);

        //摩拜单车定制
        boolean isMoBike = cn.com.duiba.developer.center.api.utils.AppIdConstant.isMoBike(app.getId());
        modelMap.put("isMobike", isMoBike);
        if ("preview".equals(request.getParameter("type"))) {
            ModelAndView view = previewzhichong(modelMap, ai, app, item, consumer, itemKeyDto, loginProgram);
            if (view != null) {
                return view;
            }
        }
        //来伊份定制\
        setLaiYiFenField(modelMap,RequestLocal.getAppId(),appItemId);
        // 花西子定制
        setHuaXiZiField(modelMap, RequestLocal.getAppId(), itemKeyDto);
        // 功能权限
        modelMap.put("authorityVO", getUnitAuthorityVO());
        // 下架的商品
        if (item.getDuibaType() == 0 && !item.getAutoRecommend()
                && (ai == null || !item.getEnable() || ai.getDeleted() || ai.getStatus().equals(AppItemDto.StatusOff))) {
            return zhichongOff(consumer, itemKeyDto, app, item, ai, loginProgram, modelMap, vipLimitViewInfoVO, minCredits, domainConfigDto);
        }
        setExchangeButtonControlInfoVO(consumer, itemKeyDto, app, exchangeButtonControlInfoVO, item, modelMap, minCredits);
        // 标签兑换限制最低优先级
        checkConsumerLabel(consumer, app, itemKeyDto, exchangeButtonControlInfoVO);
        // 标签兑换限制最低优先级
        checkAppItemLimitTag(consumer, Optional.ofNullable(itemKeyDto).map(ItemKeyDto::getAppItem).map(AppItemDto::getId).orElse(null), app, exchangeButtonControlInfoVO, null);
        // 组装返回信息
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        return getZhichongModelAndView(consumer, itemKeyDto, app, exchangeButtonControlInfoVO, item, ai, loginProgram, modelMap, vipLimitViewInfoVO, domainConfigDto.getCdnDomain());
    }

    /**
     * 用户剩余积分显示处理
     *
     * @param credits
     * @param app
     * @return {@link String}
     */
    private String userCreditsShowDeal(Long credits, AppSimpleDto app) {
        if (credits == null) {
            return "";
        }
        // 积分小数点显示定制处理
        String creditsStr = credits.toString();
        if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
            BigDecimal creditsDecimal = new BigDecimal(creditsStr);
            creditsStr = PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(creditsDecimal.longValue(), app);
        }
        return creditsStr;
    }

    private void setSourceInfo(Map<String, Object> modelMap, HttpServletRequest request, AppSimpleDto app, ItemKeyDto itemKeyDto, CItemVO citem) {
        OrderSourceTypeEnum orderSourceTypeEnum = null;
        String sourceType = request.getParameter("sourceType");
        if (StringUtils.isNotBlank(sourceType) && Objects.equals(Integer.valueOf(sourceType), OrderSourceTypeEnum.ORDER_BONUS.getCode())) {
            orderSourceTypeEnum = OrderSourceTypeEnum.ofCode(Integer.valueOf(sourceType));
            citem.setPrice(0D);
            citem.setCredits(0L);
            if (citem.getCreditsSkuList() != null) {
                citem.getCreditsSkuList().forEach(o -> {
                    o.setMarketingCredits(0D);
                    o.setMarketingPrice(0L);
                });
            }
        } else {
            FulcreditsActConfDto fulcreditsActConfDto = fulcreditsService.getFulcreditsActConfDto(itemKeyDto, app);
            if (fulcreditsActConfDto != null && CollectionUtils.isNotEmpty(fulcreditsActConfDto.getFulcreditsActOptionsDtoList())) {
                orderSourceTypeEnum = OrderSourceTypeEnum.ORDER_GIVING_ACT;
                DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
                List<FulcreditsActOptionsDto> optionsDtoList = fulcreditsActConfDto.getFulcreditsActOptionsDtoList();
                optionsDtoList.sort((o1, o2) -> o2.getPayload().compareTo(o1.getPayload()));
                List<BonusItemVO> bonusItems = BonusItemVO.toDTOList(optionsDtoList, domainConfigDto);
                modelMap.put("bonusAct", new BonusActVO(fulcreditsActConfDto.getId(), bonusItems));
            }
        }

        if (orderSourceTypeEnum != null) {
            modelMap.put("sourceType", orderSourceTypeEnum.getCode());
            modelMap.put("sourceId", request.getParameter("sourceId"));
        }
    }

    private void setExchangeButtonControlInfoVO(ConsumerDto consumer, ItemKeyDto itemKeyDto, AppSimpleDto app, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO, ItemDto item, Map<String, Object> modelMap, Long minCredits) {
        // 暂不可用
        if (item.getShutDown()) {
            exchangeButtonControlInfoVO.setExchangeText("暂不可用");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setLock(true);
        } else {
            // 日预算，月预算，余额验证
            if (item.getActualPrice() > 0) {
                AppBudgetDto appBudgetDto = remoteAppServiceNew.getAppBudget(app.getId()).getResult();
                checkBudget(itemKeyDto, appBudgetDto, exchangeButtonControlInfoVO);
            }
        }

        if (consumer.getCredits() < minCredits) {
            if (app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)) {
                exchangeButtonControlInfoVO.setExchangeText("金额不足");
            } else {
                exchangeButtonControlInfoVO.setExchangeText(app.getUnitName() + "不足");
            }
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setLock(true);
        }
        //wey 定制内容
        addExchangeTextByCustomized(consumer,itemKeyDto,app, exchangeButtonControlInfoVO);

        if (WhiteAccessUtil.matchWhiteList(app.getId(), HUIFENG_ACCESS_KEY)) {
            exchangeButtonControlInfoVO = itemViewService.buildButtonControlInfo(itemKeyDto, consumer, app,
                    exchangeButtonControlInfoVO, exchangeButtonControlInfoVO.getExchangeText(), minCredits, null, null);
        }
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
    }

    private void addExchangeTextByCustomized(ConsumerDto consumer, ItemKeyDto itemKeyDto,AppSimpleDto app,ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (weyOwnerConfig.getFinalAppIds().contains(consumer.getAppId())) {
            ConsumerExtraDto extraDto = remoteConsumerExtraService.findByConsumerId(consumer.getId()).getResult();
            if (extraDto == null || StringUtils.isBlank(extraDto.getJson()) || Objects.equals(weyOwnerConfig.getOwnerInfo(), Optional.ofNullable(JSONObject.parseObject(extraDto.getJson()).getLong(weyOwnerConfig.getOwner())).orElse(0L))) {
                exchangeButtonControlInfoVO.setExchangeText("非认证车主无法兑换");
                exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.TEMP_NOT_AVAILABLE);
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setLock(true);
            }
        }
        // 兑换按钮定制
        exchangeTextByCustomized(consumer,itemKeyDto,app,exchangeButtonControlInfoVO);
    }

    /**
     * 兑换按钮定制
     */
    private void exchangeTextByCustomized(ConsumerDto consumer, ItemKeyDto itemKeyDto,AppSimpleDto app,ExchangeButtonControlInfoVO exchangeButtonControlInfoVO){
        if (WhiteAccessUtil.matchWhiteList(consumer.getAppId(),DCJJ_WHITE)){
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = sra.getRequest();
            String drawInfo = request.getParameter("drawInfo");
            if (drawInfo == null){
                exchangeButtonControlInfoVO.setExchangeText("暂不可领");
                exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.TEMP_NOT_AVAILABLE);
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setLock(true);
                return;
            }
            try {
                // 查询大成基金接口
                DcjjPreCheckParam checkParam = new DcjjPreCheckParam();
                checkParam.setDrawInfo(drawInfo);
                checkParam.setCustNo(consumer.getPartnerUserId());
                checkParam.setAppItemId(itemKeyDto.getAppItem().getId());
                checkParam.setTimestamp(System.currentTimeMillis());
                checkParam.setAppKey(app.getAppKey());
                Map<String, String> signParam = BeanUtils.transBeanToMap(checkParam)
                        .entrySet()
                        .stream()
                        .filter(x->x.getValue()!=null)
                        .collect(Collectors.toMap(Map.Entry::getKey, y->y.getValue().toString()));
                signParam.put("appSecret",app.getAppSecret());
                checkParam.setSign(SignTool.sign(signParam));
                signParam.remove("appSecret");
                DcjjResult<String> result = remoteDcjjService.checkLimitUrl(checkParam);
                // 查询接口,返回失败
                if (!result.isSuccess()){
                    exchangeButtonControlInfoVO.setExchangeText(getMessageByCode(result.getCode()));
                    exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.TEMP_NOT_AVAILABLE);
                    exchangeButtonControlInfoVO.setExchangeEnable(false);
                    exchangeButtonControlInfoVO.setLock(true);
                }
            }catch (Exception e){
                log.warn("addExchangeTextByCustomized 大成基金 error consumerId={}",consumer.getId(),e);
                exchangeButtonControlInfoVO.setExchangeText("暂不可领");
                exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.TEMP_NOT_AVAILABLE);
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setLock(true);
            }
        }
    }

    /**
     * 大成基金根据错误码获取按钮文案
     */
    private String getMessageByCode(String  code){
        String buttonText = "暂不可领";
        switch (code){
            case "200101":
                buttonText = "权益不存在";
                break;
            case "200102":
                buttonText = "暂不可领";
                break;
            case "200103":
                buttonText = "已达领取限制";
                break;
            case "200104":
                buttonText = "已过期";
                break;
            case "200105":
                buttonText = "暂无库存";
                break;
            default:
                break;
        }
        return buttonText;
    }

    private ModelAndView getZhichongModelAndView(ConsumerDto consumer, ItemKeyDto itemKeyDto, AppSimpleDto app, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO, ItemDto item, AppItemDto ai, String loginProgram, Map<String, Object> modelMap,
                                                 VipLimitViewInfoVO vipLimitViewInfoVO, String cdnDomain) {
        String route = commonConfig.isNewGoodsDetailApp(app) ? "/refactor/directcharge" : "";
        if (ItemDto.TypePhonebill.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(ai.getCustomPrice());
            // 下架10元以下的话费
            pd.getCustomDegreeMap().entrySet().removeIf(entry -> Integer.parseInt(entry.getKey()) < 10);
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            Integer facePrice = pd.getMinDegreeInt() * 100;
            if (facePrice > getConsumerRemainingMoneyDO().getMoney()) {
                log.warn("客户资产余额不足导致无法兑换 app = {}，appitemId = {}", consumer.getAppId(), ai.getId());
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setExchangeText("已兑完");
                exchangeButtonControlInfoVO.setLock(true);
                addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
            }
            boundPhone(modelMap, app, consumer);

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);

            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/phonebilldetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypePhoneflow.equals(item.getType())) {

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);
            ModelAndView model = new ModelAndView("consumer/mobile/phoneflow");
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeAlipay.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(itemKeyDto.getAppItem().getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            Integer facePrice = pd.getMinDegreeInt() * 100;

            checkExchangeButton(facePrice, exchangeButtonControlInfoVO, modelMap);

            boundAlipay(modelMap, app, consumer);

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);

            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/alipaydetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.AplipaySku.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(ai.getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            Integer facePrice = item.getFacePrice();

            checkExchangeButton(facePrice, exchangeButtonControlInfoVO, modelMap);

            boundAlipay(modelMap, app, consumer);
            ModelAndView model = new ModelAndView("consumer/mobile/alipaydetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            modelMap.put("exchangeText", exchangeButtonControlInfoVO.getExchangeText());
            modelMap.put("exchangeEnable", exchangeButtonControlInfoVO.getExchangeEnable());
            modelMap.put("exchangeStatus", exchangeButtonControlInfoVO.getExchangeStatus());

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);

            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeQB.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(itemKeyDto.getAppItem().getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            Integer facePrice = pd.getMinDegreeInt() * 100;

            checkExchangeButton(facePrice, exchangeButtonControlInfoVO, modelMap);

            boundQQ(modelMap, app, consumer);

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);

            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/qbdetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeVirtual.equals(item.getType()) && item.isOpTypeItem(ItemBaseDto.OpTypeLimitVPro)) {
            PriceDegreeDto pd = new PriceDegreeDto(itemKeyDto.getAppItem().getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            /*if (pd != null) {
                Integer facePrice = pd.getMinDegreeInt() * 100;
                checkExchangeButton(facePrice, exchangeButtonControlInfoVO, modelMap);
            }*/
            String view = "consumer/mobile/refactor/virtualdetail";
            if (!itemKeyDto.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                view = "consumer/mobile/virtual";
            }

            customExchangeLimit(itemKeyDto, consumer, exchangeButtonControlInfoVO, app, modelMap);

            ModelAndView model = new ModelAndView(view);
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else {
            return null;
        }
    }


    /**
     * 定制兑换限制
     *
     * @param key
     * @param consumer
     * @param exchangeButtonControlInfoVO
     * @param app
     * @param modelMap
     */
    public void customExchangeLimit(ItemKeyDto key, ConsumerDto consumer, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO, AppSimpleDto app, Map<String, Object> modelMap) {
        // 目前仅智慧芽app定制
        if (wisdomBudsConfig.verifyAppId(app.getId())) {
            exchangeCheckRegistry.getHandlerAndCheck(new CustomExchangeParam(key, consumer, exchangeButtonControlInfoVO, false, app));
            addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        }
    }


    public void checkExchangeButton(Integer facePrice, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO, Map<String, Object> modelMap) {
        if (facePrice > getConsumerRemainingMoneyDO().getMoney()) {
            log.warn("客户资产余额不足导致无法兑换 app = {}", RequestLocal.getAppId());
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeText("已兑完");
            exchangeButtonControlInfoVO.setLock(true);
            addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        }

    }

    private ModelAndView zhichongOff(ConsumerDto consumer, ItemKeyDto itemKeyDto, AppSimpleDto app, ItemDto item, AppItemDto appItemDto, String loginProgram, Map<String, Object> modelMap,
                                     VipLimitViewInfoVO vipLimitViewInfoVO, Long minCredits, DomainConfigDto systemDomain) {
        String limitTip;
        if (appItemDto == null) {
            appItemDto = new AppItemDto();
            appItemDto.setAppId(app.getId());
            appItemDto.setItemId(item.getId());
            appItemDto.setStatus(AppItemDto.StatusOff);
            appItemDto.setAddTime(new Date());
            appItemDto.setTitle(item.getName());
            appItemDto.setSubtitle(item.getSubtitle());
            appItemDto.setCustomPrice(item.getCustomPrice());
            appItemDto.setCredits(null);
            appItemDto.setMinFacePrice(item.getMinFacePrice());
            appItemDto.setLogo(item.getLogo());
            appItemDto.setDeleted(false);
            appItemDto.setType(item.getType());
            appItemDto.setExchangeTipTemplate("模板");
            appItemDto.setIsOwner(false);
            appItemDto.setOperationsType(0);
        }
        ItemKeyDto pkey = new ItemKeyDto(appItemDto, item);
        CItemVO pitem = consumerItemRenderService.getCItemVO(itemKeyDto, app);

        Long offMinCredits = null;
        if (StringUtils.isNotBlank(appItemDto.getCustomPrice())) {
            PriceDegreeDto offpd = new PriceDegreeDto(appItemDto.getCustomPrice());
            offMinCredits = (long) (offpd.getMinDegreeInt() * app.getCreditsRate());
        } else {
            offMinCredits = minCredits;
        }
        limitTip = "此商品已下架";
        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
        exchangeButtonControlInfoVO.setExchangeText("已下架");
        exchangeButtonControlInfoVO.setExchangeEnable(false);
        exchangeButtonControlInfoVO.setLock(true);

        replaceDomain(systemDomain, pitem);
        replaceDomain(systemDomain, pkey);
        modelMap.put("citem", pitem);
        modelMap.put("itemKey", pkey);
        modelMap.put("limitTip", limitTip);
        modelMap.put("over", false);
        modelMap.put("minCredits", offMinCredits);
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        String route = commonConfig.isNewGoodsDetailApp(app) ? "/refactor/directcharge" : "";
        if (ItemDto.TypePhonebill.equals(item.getType())) {
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/phonebilldetail");
            PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypePhoneflow.equals(item.getType())) {
            ModelAndView model = new ModelAndView("consumer/mobile/phoneflow");
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeAlipay.equals(item.getType())) {
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/alipaydetail");
            PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeQB.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/qbdetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeVirtual.equals(item.getType()) && item.isOpTypeItem(ItemBaseDto.OpTypeLimitVPro)) {
            String view = "consumer/mobile/refactor/virtualdetail";
            if (!pkey.isV1() && !commonConfig.isNewGoodsDetailApp(app)) {
                view = "consumer/mobile/virtual";
            }
            ModelAndView model = new ModelAndView(view);
            PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, vipLimitViewInfoVO.isVipLimit(),
                            vipLimitViewInfoVO.getIsCanConsumerExchange(),
                            vipLimitViewInfoVO.getLimitRemark()));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else {
            return null;
        }
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/queueQuery")
    @ApiOperation(value = "秒杀队列", notes = "秒杀队列", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "queueId", value = "queueId", dataType = "Long", required = true, paramType = "query"),
    })
    public ModelAndView queueQuery(HttpServletRequest request, HttpServletResponse response) {
        Long queueId = Long.valueOf(request.getParameter("queueId"));
        TakeOrderQueueRecordDto record = takeOrderQueueService.findRecordByQueueId(queueId);
        if (record != null) {
            Long orderId = record.getOrderId();
            OrdersDto order = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid()).getResult();
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
            try {
                if (order == null) {
                    response.sendRedirect(request.getScheme() + ":" + domainConfigDto.getTradeDomain() + "/crecord/recordDetail?orderId=" + orderId + "&after=1" + "&dpm=" + app.getId() + ".23.1.0&dbnewopen");
                    return null;
                }
                return getRedirectModelAndView(request, response, orderId, order, app, domainConfigDto);
            } catch (IOException e) {
                log.error("秒杀队列出错", e);
            }

        } else {
            int ret = takeOrderQueueService.checkTakeOrderSuccess(queueId);
            if (ret == TakeOrderQueueService.SECONDKILLFAIL) {
                //失败
                ModelAndView model = new ModelAndView("consumer/mobile/seckill_fail");
                return model;
            } else {
                //排队中
                ModelAndView model = new ModelAndView("consumer/mobile/seckill_inqueue");
                model.addObject("queueId", queueId);
                return model;
            }
        }
        ModelAndView model = new ModelAndView("consumer/mobile/seckill_inqueue");
        return model;
    }

    private ModelAndView getRedirectModelAndView(HttpServletRequest request, HttpServletResponse response, Long orderId, OrdersDto order, AppSimpleDto app, DomainConfigDto domainConfigDto) throws IOException {
        //加钱购
        if (order.getConsumerPayPrice() > 0) {
            response.sendRedirect("/ambPay/ambPay?orderId=" + orderId
                    + "&dpm=" + app.getId() + ".23.1.0&dbnewopen");
            return null;
        } else {
            //成功
            if (StringUtils.equals(ItemDto.TypeObject, order.getType())) {
                response.sendRedirect(request.getScheme() + ":" + domainConfigDto.getTradeDomain() + "/crecord/recordDetailNew?orderId=" + orderId + "&after=1" + "&dpm=" + app.getId() + ".23.1.0&dbnewopen");
            } else {
                response.sendRedirect(request.getScheme() + ":" + domainConfigDto.getTradeDomain() + "/crecord/recordDetail?orderId=" + orderId + "&after=1" + "&dpm=" + app.getId() + ".23.1.0&dbnewopen");
            }
            return null;
        }
    }

    /**
     * @param orderId
     * @param request
     * @return Message
     */
    @RequestMapping(value = "/orderAmbStatusQuery")
    @ResponseBody
    @ApiOperation(value = "查询订单状态")
    public Message orderAmbStatusQuery(@ApiParam(value = "订单编号", required = true) @RequestParam Long orderId,
                                       HttpServletRequest request, HttpServletResponse response) {
        DubboResult<OrdersDto> ords = remoteConsumerOrderSimpleService.findById(orderId, RequestLocal.getCid());
        OrdersDto ord = ords.getResult();
        if (ord == null) {
            return Message.error("订单不存在");
        }
        if (!ord.getConsumerId().equals(RequestLocal.getCid())) {
            return Message.error("无权访问");
        }
        Boolean isAmb = false;
        JSONObject json = new JSONObject();
        if (ord.getConsumerPayPrice() > 0) {
            isAmb = true;
        }
        if (OrdersDto.FlowworkStageOrderCreateStart.equals(ord.getFlowworkStage())) {
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
        } else if ("Create-tofail".equals(ord.getFlowworkStage()) || "Fail-started".equals(ord.getFlowworkStage())
                || "Fail-complete".equals(ord.getFlowworkStage())) {
            json.put(STATUS, "fail");
            String message = StringUtils.isEmpty(ord.getError4Consumer()) ? "兑换失败！" : ord.getError4Consumer();
            json.put(MESSAGE, message);
            json.put("kind", "fail");
        } else if (OrdersDto.StatusCreate.equals(ord.getStatus())) {
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
        } else if (OrdersDto.StatusConsumeSuccess.equals(ord.getStatus())) {
            json.put(STATUS, "consume_success");
            json.put(MESSAGE, "兑换成功！");
            json.put("kind", "consume_success");
        } else if (OrdersDto.StatusSuccess.equals(ord.getStatus())) {
            // kind=success参数用于兑换完后显示文案，恭喜兑换成功。
            json.put(STATUS, SUCCESS);
            json.put(MESSAGE, "兑换成功！");
            json.put("kind", SUCCESS);
        } else if (OrdersDto.AuditStatusWait.equals(ord.getAuditStatus())) {
            json.put(STATUS, SUCCESS);
            json.put(MESSAGE, "兑换成功！待审批");
            json.put("kind", SUCCESS);
        } else {
            json.put(STATUS, "processing");
            json.put(MESSAGE, "兑换正在处理中...");
        }
        json.put("isAmb", isAmb);
        return Message.success(json);
    }

    /**
     * @return
     */
    @RequestMapping("/appBulletin")
    @ApiOperation(value = "兑吧商品展示", notes = "兑吧商品展示", httpMethod = "GET")
    public ModelAndView appBulletin() {
        ModelAndView model = new ModelAndView("consumer/mobile/bulletin_info");
        try {
            AppSimpleDto app = RequestLocal.getConsumerAppDO();
            if (!app.isAppSwitch(AppSimpleDto.SwitchOpenBulletin)) {
                throw new GoodsWebException("应用并没有开启公示详情页");
            }
            AppBulletinDto bulletin = remoteAppBulletinService.findAppBulletinDtoByAppId(app.getId()).getResult();
            if (bulletin == null) {
                log.error("app:" + app.getId() + "公示配置丢失");
                throw new GoodsWebException("应用并没有开启公示详情页");
            }
            String text = remoteAppBulletinService.findAppBulletinDetailByAppId(app.getId()).getResult();
            model.addObject("html", text);
        } catch (Exception e) {
            log.error("用户异常进入兑吧公示详情页", e);
        }
        return model;
    }

    /**
     * @return
     */
    @RequestMapping("/earnCreditsPage")
    @ApiOperation(value = "获取积分预览页面", notes = "获取积分预览页面", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "url", value = "url", dataType = "String", paramType = "query"),
    })
    public ModelAndView earnCreditsPage(HttpServletRequest request) {
        ModelAndView model = new ModelAndView("consumer/mobile/coupon_static");
        model.addObject("url", request.getParameter("url"));
        model.addObject("creditsName", request.getParameter("creditsName"));
        model.addObject("earnCreditsLetter", request.getParameter("earnCreditsLetter"));
        return model;
    }

    /**
     * @param itemKeyDto
     * @param appBudgetDto
     * @param exchangeButtonControlInfoVO
     */
    private void checkBudget(ItemKeyDto itemKeyDto, AppBudgetDto appBudgetDto, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (!appRequestService.isDayBudgetEnough(itemKeyDto, appBudgetDto, null)) {
            exchangeButtonControlInfoVO.setExchangeText("今天已兑完");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeStatus(StatusTypeEnum.IS_REMAINING_0.getValue());
            exchangeButtonControlInfoVO.setLock(true);
        } else if (!appRequestService.isMonthBudgetEnough(itemKeyDto, appBudgetDto)) {
            exchangeButtonControlInfoVO.setExchangeText("已兑完");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeStatus(StatusTypeEnum.IS_REMAINING_0.getValue());
            exchangeButtonControlInfoVO.setLock(true);
        } else {
            Long price = goodsItemActualPriceCalculateService.calculateMinActualPrice(itemKeyDto).longValue();
            // 2016年1月19加入加钱购商品余额逻辑
            if (itemKeyDto.getItem() != null && itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeIsAmb)) {
                price = price.longValue() - itemKeyDto.getItem().getSalePrice();
            }
            Long money = getConsumerRemainingMoneyDO().getMoney().longValue();
            if (price > money) {
                exchangeButtonControlInfoVO.setExchangeText("已兑完");
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setExchangeStatus(StatusTypeEnum.IS_REMAINING_0.getValue());
                exchangeButtonControlInfoVO.setLock(true);
            }
        }

    }

    private ModelAndView previewCouponAndObject(Map<String, Object> modelMap, AppItemDto ai, AppSimpleDto app, ItemDto item,
                                                ConsumerDto consumer, ItemKeyDto itemKeyDto, String cdnDomain) {
        // 预览页面信息
        if (ai == null) {
            ai = getAppItemDto(app, item);
        }
        // 使用预览专用key代替itemkKey传值
        ItemKeyDto pkey = new ItemKeyDto(ai, item);
        DomainConfigDto systemDomain = domainService.getSystemDomain(RequestLocal.getAppId());
        replaceDomain(systemDomain, pkey);
        modelMap.put("itemKey", pkey);
        modelMap.put("exchangeEnable", false);
        modelMap.put("exchangeText", "预览商品");
        modelMap.put("limitTip", "此商品为预览商品");
        modelMap.put("exchangeStatus", GoodsItemStatusService.StatusTypeEnum.IS_OFF_SHELVES.getValue());
        modelMap.put("lock", true);
        modelMap.put("cfg", detailCfg(modelMap, app));
        Map<String, Object> downloadConfig = qqMarketDownloadConfig(ai);
        modelMap.put("qqMarketDownloadConfig", downloadConfig);
        modelMap.put("showCredits", AppIdConstant.showCredits(app.getId()));
        ModelAndView model;
        String view = getGoodsView(itemKeyDto, app);
        if (ItemDto.TypeCoupon.equals(item.getType())) {
            addCouponSeparateParamter(app, consumer, modelMap, itemKeyDto);
            model = new ModelAndView(view);
            model.addAllObjects(modelMap);
            model.addObject("data", JSONObject.toJSONString(modelMap));
        } else if (ItemDto.TypeObject.equals(item.getType())) {
            model = new ModelAndView(view);
            model.addAllObjects(modelMap);
            model.addObject("data", JSONObject.toJSONString(modelMap));
        } else if (ItemDto.TypePhonebillDingzhi.equals(item.getType())) {
            model = new ModelAndView("consumer/mobile/phonebilldetailDingzhi");
            model.addAllObjects(modelMap);
            model.addObject("data", JSONObject.toJSONString(modelMap));
        } else if (ItemDto.TypeVirtual.equals(item.getType()) && item.isOpTypeItem(ItemDto.OpTypeLimitVPro)) {
            model = new ModelAndView(view);
            model.addAllObjects(modelMap);
            model.addObject("data", JSONObject.toJSONString(modelMap));
        } else {
            return new ModelAndView(ERROR);
        }
        model.addObject("downloadConfig", downloadConfig);
        return model;
    }

    private AppItemDto getAppItemDto(AppSimpleDto app, ItemDto item) {
        AppItemDto ai;
        ai = new AppItemDto();
        ai.setAppId(app.getId());
        ai.setItemId(item.getId());
        ai.setStatus(AppItemDto.StatusOff);
        ai.setAddTime(new Date());
        ai.setTitle(item.getName());
        ai.setSubtitle(item.getSubtitle());
        ai.setCustomPrice(item.getCustomPrice());
        ai.setCredits(null);
        ai.setMinFacePrice(item.getMinFacePrice());
        ai.setLogo(item.getLogo());
        ai.setDeleted(false);
        ai.setType(item.getType());
        ai.setOwner(false);
        ai.setOperationsType(0);
        ai.setExchangeTipTemplate("模板");
//        ai.setDescription(item.getDescription());
        return ai;
    }

    private ModelAndView previewzhichong(Map<String, Object> modelMap, AppItemDto ai, AppSimpleDto app, ItemDto item,
                                         ConsumerDto consumer, ItemKeyDto itemKeyDto, String loginProgram) {
        String customePrice = itemKeyDto.getAppItem() != null ? itemKeyDto.getAppItem().getCustomPrice() : itemKeyDto.getItem().getCustomPrice();
        // 预览页面信息
        if (ai == null) {
            ai = getAppItemDto(app, item);
        } else {
            ai.setItemId(item.getId());
            ai.setCredits(null);
            ai.setStatus(AppItemDto.StatusOff);
            ai.setExchangeTipTemplate("模板");
            ai.setOperationsType(0);
        }
        ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
        exchangeButtonControlInfoVO.setExchangeEnable(false);
        exchangeButtonControlInfoVO.setExchangeText("预览商品");
        exchangeButtonControlInfoVO.setLock(true);
        addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
        String route = commonConfig.isNewGoodsDetailApp(app) ? "/refactor/directcharge" : "";
        if (ItemDto.TypePhonebill.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(customePrice);
            // 下架10元以下的话费
            pd.getCustomDegreeMap().entrySet().removeIf(entry -> Integer.parseInt(entry.getKey()) < 10);
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            boundPhone(modelMap, app, consumer);
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/phonebilldetail");
            model.addObject("degreeInfo", consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, false, null, null));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypePhoneflow.equals(item.getType())) {
            ModelAndView model = new ModelAndView("consumer/mobile/phoneflow");
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeAlipay.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(customePrice);
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            boundAlipay(modelMap, app, consumer);
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/alipaydetail");
            model.addObject("degreeInfo", consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, false, null, null));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else if (ItemDto.TypeQB.equals(item.getType())) {
            PriceDegreeDto pd = new PriceDegreeDto(customePrice);
            modelMap.put("priceDegreeMap", PlatFormCouponServiceImpl.parseDegreeCredits(pd, app));
            Integer facePrice = pd.getMinDegreeInt() * 100;
            if (facePrice > getConsumerRemainingMoneyDO().getMoney()) {
                log.warn("客户资产余额不足导致无法兑换 app = {}", consumer.getAppId());
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setExchangeText("已兑完");
                exchangeButtonControlInfoVO.setLock(true);
                addExchangeButtonControlInfoVO(modelMap, exchangeButtonControlInfoVO);
            }
            boundQQ(modelMap, app, consumer);
            ModelAndView model = new ModelAndView("consumer/mobile" + route + "/qbdetail");
            model.addObject("degreeInfo",
                    consumerExchangeLimitService.degreeText(consumer, itemKeyDto, app, pd, false,
                            null,
                            null));
            return buildReturnData(model, loginProgram, modelMap, null);
        } else {
            return null;
        }
    }

    private void boundQQ(Map<String, Object> modelMap, AppSimpleDto app, ConsumerDto consumer) {
        String qq = consumer.getLastQq();
        // 需要判断是否需要账号绑定
        if (app.isAppSwitch(AppSimpleDto.BoundQQ)) {
            modelMap.put("accountBound", true);
            if (consumer.getQq() != null && consumer.getQq().trim().length() > 0) {
                qq = consumer.getQq();
                modelMap.put(HAS_ACCOUNT, true);
            } else {
                modelMap.put(HAS_ACCOUNT, false);
            }
        }
        modelMap.put("qq", qq);
    }

    private void boundAlipay(Map<String, Object> modelMap, AppSimpleDto app, ConsumerDto consumer) {
        String alipay = null;
        String realname = consumer.getLastRealname();
        // 需要判断是否需要账号绑定
        if (app.isAppSwitch(AppSimpleDto.BoundAlipay)) {
            modelMap.put("accountBound", true);
            if (consumer.getAlipay() != null && consumer.getAlipay().split(":").length > 1) {
                alipay = consumer.getAlipay().split(":")[0];
                realname = consumer.getAlipay().split(":")[1];
                modelMap.put(HAS_ACCOUNT, true);
            } else {
                modelMap.put(HAS_ACCOUNT, false);
            }
        } else {
            // 若APP不需要用户绑定支付宝，继续正常流程
            String lastAlipay = consumer.getLastAlipay();
            modelMap.put("lastAlipay", lastAlipay);
        }
        modelMap.put("alipay", alipay);
        modelMap.put("realname", realname);
    }

    private void boundPhone(Map<String, Object> modelMap, AppSimpleDto app, ConsumerDto consumer) {
        String phone = consumer.getLastPhone();
        // 需要判断是否需要账号绑定
        if (app.isAppSwitch(AppSimpleDto.BoundPhone)) {
            modelMap.put("accountBound", true);
            if (consumer.getPhone() != null && consumer.getPhone().trim().length() > 0) {
                phone = consumer.getPhone();
                modelMap.put(HAS_ACCOUNT, true);
            } else {
                modelMap.put(HAS_ACCOUNT, false);
            }
        }
        modelMap.put("phone", phone);
    }

    /**
     * 兑换项详细页JSON信息封装
     *
     * @throws StatusException
     */
    private String detailCfg(Map<String, Object> map, AppSimpleDto app) {
        ItemKeyDto itemKeyDto = (ItemKeyDto) map.get("itemKey");
        map.put("faicePirce", itemViewService.getFacePrice(itemKeyDto, app));

        long stock = goodsItemStockService.findStock(itemKeyDto, app.getId());
        map.put("remain", getRemainText(itemKeyDto, stock));
        if (map.get("remainStock") == null) {
            map.put("remainStock", stock);
        }
        map.put("showRemainStock", app.isAppSwitch(AppSimpleDto.SwitchOpenRemainStock));

        ConsumerDto consumer = (ConsumerDto) map.get("consumer");
        Map<String, Object> cfgMap = Maps.newHashMap();
        Integer status;
        if (consumer.isNotLoginUser()) {
            status = GoodsItemStatusService.IS_NOTLOGIN;
        } else {
            status = (Integer) map.get("exchangeStatus");
        }
        cfgMap.put(STATUS, status);
        String limitDate = getLimitDate(itemKeyDto);
        String startDay = "";
        if (limitDate != null && !limitDate.equals("no")) {
            startDay = limitDate.split(",")[0];
        }
        cfgMap.put("limitdaystart", goodsItemTimeLimitService.limitDayIsStart(itemKeyDto));
        cfgMap.put("limitdayend", goodsItemTimeLimitService.limitDayIsOver(itemKeyDto));
        cfgMap.put("startday", startDay);
        Boolean ifTimeLimit = (itemKeyDto.getItem() != null && itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit))
                || (itemKeyDto.getAppItem() != null && itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit));
        cfgMap.put("timedown", ifTimeLimit);
        String startTime = "";
        String endTime = "";
        if (itemKeyDto.getItem() != null && itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit)) {
            startTime = itemKeyDto.getItem().getLimitTimeBetween().split("-")[0];
            endTime = itemKeyDto.getItem().getLimitTimeBetween().split("-")[1];
        } else if (itemKeyDto.getAppItem() != null && itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit)) {
            startTime = itemKeyDto.getAppItem().getLimitTimeBetween().split("-")[0];
            endTime = itemKeyDto.getAppItem().getLimitTimeBetween().split("-")[1];
        }
        cfgMap.put("startTime", startTime);
        cfgMap.put("endTime", endTime);
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        cfgMap.put("serverTime", sdf.format(new Date()));
        cfgMap.put("mycredits", consumer.getCredits());
        CItemVO citem = (CItemVO) map.get("citem");
        cfgMap.put("needcredits", PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(citem.getCredits(), app));
        if (itemKeyDto.getAppItem() != null) {
            cfgMap.put("needAccount", itemKeyDto.getAppItem().isOpTypeAppItem(ItemDto.OpTypeNeedUserName));
        } else {
            cfgMap.put("needAccount", false);
        }
        return JSONArray.toJSONString(cfgMap);
    }

    private String getLimitDate(ItemKeyDto itemKeyDto) {
        String limitDate = null;
        if (itemKeyDto.isItemMode()) {
            limitDate = itemKeyDto.getItem().getLimitDate();
        }
        if (itemKeyDto.isDuibaAppItemMode()) {
            if (itemKeyDto.getItem().getLimitDate() != null && !itemKeyDto.getItem().getLimitDate().equals("no")) {
                limitDate = itemKeyDto.getItem().getLimitDate();
            } else if (itemKeyDto.getAppItem().getLimitDate() != null) {
                limitDate = itemKeyDto.getAppItem().getLimitDate();
            }
        } else if (itemKeyDto.isSelfAppItemMode()) {
            limitDate = itemKeyDto.getAppItem().getLimitDate();
        }
        return limitDate;
    }


    private Map<String, Object> addCouponSeparateParamter(AppSimpleDto app, ConsumerDto consumer, Map<String, Object> modelMap,
                                                          ItemKeyDto itemKeyDto) {
        modelMap.put("isShow3SendObject", AppIdConstant.isShow3SendObject(consumer.getId()));
        modelMap.put("showCredits", AppIdConstant.showCredits(app.getId()));
        modelMap.put("isSwitchCloseCredits", app.isAppSwitch(AppSimpleDto.SwitchCloseCreditsType));
        modelMap.put("isOpTypeCredits", ItemKeyUtils.isOpTypeSwitchOn(itemKeyDto, ItemDto.OpTypeCredits));
        modelMap.put("creditsName", app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint) ? "金额" : app.getUnitName());
        return modelMap;
    }


    private ModelAndView buildReturnData(ModelAndView model, String loginProgram, Map<String, Object> modelMap, String hdShareProgram) {
        // login program有js代码 不能转义
        model.addObject("data", JSONObject.toJSONString(modelMap));
        modelMap.put("loginProgram", loginProgram);
        modelMap.put("hdShareProgram", hdShareProgram);
        model.addAllObjects(modelMap);
        AppSimpleDto appSimpleDto = RequestLocal.getConsumerAppDO();
        //全局色
        if (appSimpleDto != null && appSimpleDto.isColorSwitch()) {
            model.addObject("themeColor", appSimpleDto.getColor());
        }
        return model;
    }

    /**
     * 虚拟商品档位
     */
    private Map<String, Map<String, String>> virtualDegree(AppSimpleDto app, Map<String, Map<String, String>> map) {
        for (Entry<String, Map<String, String>> entry : map.entrySet()) {
            Map<String, String> hashMap = entry.getValue();
            hashMap.put("credits",
                    PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(Long.valueOf(hashMap.get("credits")), app));
        }
        return map;
    }

    private String getShareProgram(final Long appId) {
        try {
            return appShearProgramCache.get(appId, new Callable<String>() {
                @Override
                public String call() throws Exception {
                    return getShareProgramByRpc(appId);
                }
            });
        } catch (Exception e) {
            log.warn("getShareProgram", e);
            return getShareProgramByRpc(appId);
        }
    }

    private String getShareProgramByRpc(Long appId) {
        DubboResult<String> programRet = remoteAppExtraServiceNew.findShareProgramByAppId(appId);
        if (programRet.isSuccess() && StringUtils.isNotEmpty(programRet.getResult())) {
            return programRet.getResult();
        }
        return "";
    }

    private Boolean isZhichong(String type) {
        if (ItemDto.TypeQB.equals(type) || ItemDto.TypePhonebill.equals(type) ||
                ItemDto.TypeAlipay.equals(type) || ItemDto.TypePhoneflow.equals(type) ||
                ItemDto.TypePhonebillDingzhi.equals(type) || ItemDto.TypeAlipayCode.equals(type) || ItemDto.TypeAlipayFast.equals(type)) {
            return true;
        }
        return false;
    }

    /**
     * 获取有积分文案的app实体
     *
     * @param appSimpleDto
     * @return
     */
    private AppVO getAppWithEarnCreditsLetter(AppSimpleDto appSimpleDto,ConsumerDto consumerDto) {
        AppVO appVO = new AppVO(appSimpleDto);
        appVO.setEarnCreditsLetter(this.getEarnCreditsLetter(appSimpleDto.getId()));
        // 少年得到如果免登带了赚积分文案和链接，优先取免登中的
        try {
            if(!WhiteAccessUtil.selectWhiteListConfig(APP_CREDITS_SHOW).contains(appSimpleDto.getId().toString())){
                return appVO;
            }
            if(consumerDto == null){
                log.info("consumerDto is null");
                return appVO;
            }
            ConsumerExtraDto result = remoteConsumerExtraService.findByConsumerId(consumerDto.getId()).getResult();
            if (result == null){
                log.info("ConsumerDtoResult is null");
                return appVO;
            }
            String json = result.getJson();
            if (StringUtils.isNotBlank(json)){
                JSONObject extraJson = JSONObject.parseObject(json);
                String btnText = extraJson.getString("btnText");
                String btnLink = extraJson.getString("btnLink");
                if (StringUtils.isNotBlank(btnText)) {
                    appVO.setEarnCreditsLetter(btnText);
                }
                if (StringUtils.isNotBlank(btnLink)) {
                    appVO.setEarnCreditsUrl(btnLink);
                }
            }
        }catch (Exception e){
            log.warn("获取app的earnCreditsLetter异常");
        }
        return appVO;
    }

    /**
     * 获取"获取积分文案"
     *
     * @param appId
     * @return
     */
    private String getEarnCreditsLetter(Long appId) {
        AppNewExtraDto appNewExtraDto = remoteAppNewExtraService.findByAppId(appId).getResult();
        return appNewExtraDto.getEarnCreditsLetter();
    }

    @RequestMapping("/findYingyongbaoItems")
    @ApiOperation(value = "应用宝内容定制", notes = "应用宝内容定制", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "appId", value = "appId", dataType = "Long", required = true, paramType = "query"),

    })
    @ResponseBody
    public JSONObject findYingyongbaoSumByAppIdAndDayAndOrderBy(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObject = new JSONObject();
        try {
            String domain = request.getHeader("Origin");
            if (domain != null && domain.contains(CrossDomainHelper.getCrossDomain())) {
                // 设置允许跨域
                DomainCrossUtils.crossDomain(request, response, domain);
            }
            Long appId = RequestLocal.getAppId();
            List<ItemKeyDto> list = goodsItemDataService.findYingyongbaoSumByAppIdAndDay(appId);
            DomainConfigDto domainConfigDto = domainService.getSystemDomain(Long.valueOf(appId));
            List vo = getYingyongbaoInfos(list, domainConfigDto);
            jsonObject.put("data", JSONObject.toJSON(vo));
            jsonObject.put(SUCCESS, true);
        } catch (Exception e) {
            log.info("MobileController call findYingyongbaoSumByAppIdAndDayAndOrderBy error", e);
            jsonObject.put("message", "获取列表错误");
            jsonObject.put(SUCCESS, false);
        }
        return jsonObject;
    }

    /**
     * 返回的参数封装
     *
     * @return
     */

    private List getYingyongbaoInfos(List<ItemKeyDto> itemKeyDtoList, DomainConfigDto domainConfigDto) {
        List list = new ArrayList();
        for (ItemKeyDto dto : itemKeyDtoList) {
            AppItemDto appItemDto = dto.getAppItem();
            if (null == appItemDto) {
                continue;
            }
            String url = domainConfigDto.getGoodsDomain() + "/mobile/appItemDetail?appItemId=" + appItemDto.getId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", url);
            jsonObject.put("smallImage", ItemKeyUtil.getSmallImage(dto));
            jsonObject.put("type", appItemDto.getType());
            jsonObject.put("appId", dto.getAppId());
            jsonObject.put("appItemId", appItemDto.getId());
            jsonObject.put("itemId", null != dto.getItem() ? dto.getItem().getId() : null);
            jsonObject.put("title", ItemKeyUtil.getTitle(dto));
            jsonObject.put("credits", null == appItemDto.getCredits() ? ItemKeyUtil.getCredits(dto, RequestLocal.getConsumerAppDO()) : appItemDto.getCredits());
            jsonObject.put("salePrice", ItemKeyUtil.getSalePrice(dto));
            jsonObject.put("domain", domainConfigDto.getEmbedDomain());
            list.add(jsonObject);
        }
        return list;
    }

    /**
     * @param request
     * @param response note:查询商品是否在限制区域：优先走GPS，后校验ip
     * @return ModelAndView
     */
    @GetMapping("/checkItemAddr")
    @ResponseBody
    public Result<Boolean> checkItemAddr(HttpServletRequest request, HttpServletResponse response) {
        String appidStr = request.getParameter("appItemId");
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        app = unitNameCustomService.customUnitName(app);
        if (!gpsConfig.getGpsOrIpSet().contains(app.getId())) {
            return ResultBuilder.fail(ErrorCode.E1000055.getCode(), ErrorCode.E1000055.getDesc());
        }
        Double latitude = null, longitude = null;
        if (StringUtils.isNotBlank(request.getParameter("latitude"))) {
            latitude = Double.valueOf(request.getParameter("latitude"));
        }
        if (StringUtils.isNotBlank(request.getParameter("longitude"))) {
            longitude = Double.valueOf(request.getParameter("longitude"));
        }
        try {
            ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appidStr, null, app);
            ExchangeButtonControlInfoVO exchangeButtonControlInfoVO = new ExchangeButtonControlInfoVO();
            itemViewService.checkItemAddrByGpsOrIp(itemKeyDto, exchangeButtonControlInfoVO, latitude, longitude);
            return ResultBuilder.success(exchangeButtonControlInfoVO.getExchangeEnable());
        } catch (Exception e) {
            return ResultBuilder.fail(ErrorCode.E1000041.getCode(), ErrorCode.E1000041.getDesc());
        }
    }

    /**
     * 提供AppID白名单（GPS or IP）
     *
     * @return
     */
    @GetMapping("/getGpsOrIpSetAppID")
    @ResponseBody
    public Result<Set<Long>> getGpsOrIpSetAppID() {
        return ResultBuilder.success(gpsConfig.getGpsOrIpSet());
    }


    /**
     * 提供手机端的app配置
     * 通用
     *
     * @return
     */
    @GetMapping("/getAppConfiger")
    @ResponseBody
    public Result<Boolean> getAppConfiger(String propName) {
        if (StringUtils.isBlank(propName)) {
            return ResultBuilder.fail(ErrorCode.E1000007.getCode(), ErrorCode.E1000007.getDesc());
        }
        Long appId = RequestLocal.getAppId();
        try {
            String cache = appConfigerPropNameCache.get(appId, () -> {
                String propValue = remoteAppNewExtraService.findByAppIdAndPropName(appId, propName);
                String ret = Boolean.FALSE.toString();
                if (StringUtils.isNotBlank(propValue)) {
                    ret = propValue;
                }
                return ret;
            });
            return ResultBuilder.success(Boolean.valueOf(cache));
        } catch (Exception e) {
            log.warn("查询失败", e);
        }
        return ResultBuilder.fail(ErrorCode.E1100023.getCode(), ErrorCode.E1100023.getDesc());
    }

    /**
     * 功能权限
     *
     * @return
     */
    @GetMapping("/getUnitAuthority")
    @ResponseBody
    public Result getUnitAuthority() {
        return ResultBuilder.success(getUnitAuthorityVO());
    }

    @NotNull
    private UnitAuthorityVO getUnitAuthorityVO() {
        try {
            UnitAuthorityVO vo = new UnitAuthorityVO();
            Long appId = RequestLocal.getAppId();
            Conditions.expectNotNull(appId, "应用未登录");
            //版本和单独售卖 开通的信息
            List<VersionResourceDto> versionResourceDtoList = CACHE_APP_VERSION.get(appId, key -> remoteResourcesService.versionResourceInfo(appId));
            Conditions.expectFalse(CollectionUtils.isEmpty(versionResourceDtoList), "应用未开通套餐版本");
            Set<Long> resourceIds = Sets.newHashSet();
            for (VersionResourceDto versionResourceDto : versionResourceDtoList) {
                resourceIds.addAll(versionResourceDto.getResourceIds());
            }
            vo.setOrderGiving(resourceIds.contains(unitPayFlagConfig.getFulCreditsResourceId()));
            vo.setEvaluateManager(WhiteAccessUtil.matchWhiteList(appId,CLOSE_ITEM_COMMENT) ? false: resourceIds.contains(unitPayFlagConfig.getEvaluateManagerResourceId()));
            return vo;
        } catch (Exception e) {
            log.warn("营销功能权限异常，appId:{}", RequestLocal.getAppId(), e);
            return null;
        }
    }

    /**
     *
     */
    @GetMapping("/queryAppItemPriceAndCredits")
    @ResponseBody
    public Result<JSONObject> queryAppItemPriceAndSku(Long skuId, Long appItemId) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        if (Objects.isNull(consumer)) {
            return ResultBuilder.fail(ErrorCode.E1100023.getCode(), ErrorCode.E1100023.getDesc());
        }
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        app = unitNameCustomService.customUnitName(app);
        AppItemSkuDto skuDto = remoteAppItemSkuService.findSkuById(skuId);
        if (Objects.isNull(skuDto)) {
            return ResultBuilder.fail(ErrorCode.E1000034.getCode(), ErrorCode.E1000034.getDesc());
        }
        JSONObject js = new JSONObject();
        js.put("rate", app.getCreditsRate());
        Long credits = new BigDecimal(app.getCreditsRate()).multiply(new BigDecimal(skuDto.getSalePrice())).divide(new BigDecimal(100), BigDecimal.ROUND_UP).longValue();
        ;
        js.put("credits", credits);
        js.put("price", skuDto.getSalePrice());

        List<KeyValueEntity> keyValues = remoteItemNewExtraService.findAppItemAllApi(appItemId);
        Map<String, String> propMap = keyValues.stream().collect(Collectors.toMap(KeyValueEntity::getPropName, obj -> ObjectUtils.defaultIfNull(obj.getPropValue(), StringUtils.EMPTY), (oldv, newv) -> newv));
        Long reservePrice = 0L;
        if (Objects.nonNull(propMap.get(WilteListConstants.IS_GUARANTEED)) && BooleanUtil.toBoolean(propMap.get(WilteListConstants.IS_GUARANTEED))) {
            MarketingItemCreditsSkuDto marketingItemCreditsSkuDto = remoteMarketingItemCreditsSkuService.findBySkuId(skuId);
            if (Objects.nonNull(marketingItemCreditsSkuDto)) {
                reservePrice = marketingItemCreditsSkuDto.getReservePrice();
            }
        }
        js.put("guaranteed", reservePrice);
        return ResultBuilder.success(js);
    }


    /**
     * 提供手机端的app配置
     * 通用
     *
     * @return
     */
    @RequestMapping(value = "ningboJump", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> ningboJump(HttpServletRequest request) {
        try {
            String appKey = request.getParameter("appKey");
            AppSimpleDto app = remoteAppServiceNew.getAppByAppKey(appKey).getResult();
            String uid = request.getParameter("uid");
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("uid", uid);
            paramMap.put("credits", request.getParameter("credits"));
            paramMap.put("appKey", app.getAppKey());
            paramMap.put("timestamp", request.getParameter("timestamp"));
            paramMap.put("sign", request.getParameter("sign"));
            if (!SignTool.signVerify(app.getAppSecret(), paramMap)) {
                return ResultBuilder.fail("验签失败");
            }
            String orderId = request.getParameter("orderId");
            String dcustom = getConsumerExtra(app, uid);
            if (StringUtils.isNotBlank(dcustom)) {
                paramMap.put("dcustom", dcustom.substring(0, dcustom.length() - 1));
            }
            DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(app.getId(), domainService);
            String redirectUrl = "https:" + domainConfig.getActivityDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + orderId;
            paramMap.put("redirect", redirectUrl);
            paramMap.remove("sign");
            //重新加签
            paramMap.put("appSecret", app.getAppSecret());
            String sign = SignTool.sign(paramMap);
            paramMap.remove("appSecret");
            paramMap.put("sign", sign);
            String url = "https:" + domainConfig.getActivityDomain() + "/autoLogin/autologin?";
            String loginUrl = AssembleTool.assembleUrl(url, paramMap);
            log.info("宁波银行支付成功后跳转免登成功,url={}", loginUrl);
            return ResultBuilder.success(loginUrl);
        } catch (Exception e) {
            log.warn("宁波银行支付成功后跳转免登失败,request={}", request.getQueryString(), e);
        }
        return ResultBuilder.success(StringUtils.EMPTY);
    }

    @NotNull
    private String getConsumerExtra(AppSimpleDto app, String uid) {
        ConsumerDto consumerDto = remoteConsumerService.findByAppAndPartnerUserId(app.getId(), uid);
        StringBuilder dcustomSb = new StringBuilder();
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(consumerDto.getId()).getResult();
        if (consumerExtraDto != null) {
            String json = consumerExtraDto.getJson();
            if (StringUtils.isNotBlank(json)) {
                JSONObject jo = JSON.parseObject(json);
                for (Entry<String, Object> entry : jo.entrySet()) {
                    dcustomSb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
            }
        }
        return dcustomSb.toString();
    }

}
