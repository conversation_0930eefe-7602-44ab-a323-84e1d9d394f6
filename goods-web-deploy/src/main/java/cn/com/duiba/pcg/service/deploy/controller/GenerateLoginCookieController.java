package cn.com.duiba.pcg.service.deploy.controller;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;

/**
 * Created by xiaoxuda on 2016/12/20.
 */
@Controller
@RequestMapping(value = "/test")
public class GenerateLoginCookieController {
    @Autowired
    private RemoteConsumerService remoteConsumerService;

    @Value("${pcgweb.consumer.encrypt.key}")
    private String consumerEncryptKey;

    /**
     * 种cookie方便PC测试时登录
     * @param response
     * @return ModelAndView
     */
    @RequestMapping(value = "/inner4me")
    public ModelAndView inner4me(HttpServletResponse response) {
        ConsumerDto c = remoteConsumerService.findByAppAndPartnerUserId(1L, "1");
        RequestLocal.injectConsumerInfoIntoCookie(c);

        return new ModelAndView("redirect:/");
    }

}
