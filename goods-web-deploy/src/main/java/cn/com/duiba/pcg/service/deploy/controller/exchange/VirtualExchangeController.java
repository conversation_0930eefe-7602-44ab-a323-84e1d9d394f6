package cn.com.duiba.pcg.service.deploy.controller.exchange;

import cn.com.duiba.anticheat.center.api.result.goods.ACResultDto;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.token.RemoteTokenService;
import cn.com.duiba.pcg.constant.AppIdConstant;
import cn.com.duiba.pcg.service.biz.manager.ExtrangeProcessorManager;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.impl.VirtualParam;
import cn.com.duiba.pcg.service.biz.processor.extrange.ExtrangeBaseProcessor;
import cn.com.duiba.pcg.service.biz.service.CaptchaManager;
import cn.com.duiba.pcg.service.biz.service.FormTokenService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemCouponService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.NECaptchaService;
import cn.com.duiba.pcg.service.biz.service.order.OrderCreateService;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.vo.NECaptchaResultVO;
import cn.com.duiba.pcg.service.biz.vo.OrderCreatedVO;
import cn.com.duiba.pcg.tool.JsonRender;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duibabiz.component.domain.DomainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ClassName: ObjectExchangeController <br/>
 * date: 2017年1月22日 上午10:08:01 <br/>
 *
 * <AUTHOR>
 */
@Controller
@Api(tags = "虚拟物品兑换")
public class VirtualExchangeController {

    Logger                              log = LoggerFactory.getLogger(VirtualExchangeController.class);

    @Autowired
    private GoodsItemDataService        goodsItemDataService;
    @Autowired
    private ExtrangeProcessorManager    extrangeProcessorManager;
    @Autowired
    private CaptchaManager              captchaManager;
    @Autowired
    private FormTokenService            formTokenService;
    @Autowired
    private GoodsItemCouponService      goodsItemCouponService;
    @Autowired
    private NECaptchaService              nECaptchaService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private RemoteTokenService remoteTokenService;

    @Autowired
    private OrderCreateService orderCreateService;



    /**
     * 新下单接口
     */
    @ResponseBody
    @RequestMapping("/gaw/virtualExchange/create")
    public Result<OrderCreatedVO> create(@RequestBody VirtualParam param, HttpServletRequest request) {
        return orderCreateService.createVirtual(param, request);
    }

    /**
     * @param request
     * @param response
     * @return JsonRender
     */
    @RequestMapping("/virtualExchange/exchange")
    @ApiOperation(value = "虚拟物品下单", notes = "虚拟物品下单", httpMethod = "POST")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "itemId", value = "itemId", dataType = "Long", required = false, paramType = "query"),
            @ApiImplicitParam(name = "appItemId", value = "appItemId", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "validate", value = "validate", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "token", value = "token", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "skuId", value = "skuId", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "originalPriceBuy", value = "originalPriceBuy", dataType = "String", paramType = "query")
    })
    @ResponseBody
    public JsonRender exchange(HttpServletRequest request, HttpServletResponse response) {
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        ConsumerDto consumer = RequestLocal.getConsumerDO();

        DomainConfigDto domainConfigDto = domainService.getSystemDomain(app.getId());
        DomainCrossUtils.crossDomain(request, response, domainConfigDto.getHomeDomain());

        String appItemIdstr = request.getParameter("appItemId");
        String itemIdstr = request.getParameter("itemId");

        String ip = RequestTool.getIpAddr(request);
        if (!formTokenService.checkAndInvalidConsumerToken(consumer.getId(), request.getParameter("token"))) {
            log.info("virtual exchange token check fail , cid={}, ip={}, appItemId={}, itemId={},token={},tokenId={}",
                consumer.getId(), ip, appItemIdstr, itemIdstr, request.getParameter("token"),RequestLocal.getTokenId());
            return JsonRender.failResult("token已失效");
        }

        // 获取itemKey
        ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemIdstr, itemIdstr, app);


        // 权限验证
        if (itemKeyDto.getAppItem() != null && !itemKeyDto.getAppItem().getAppId().equals(app.getId())) {
            return JsonRender.failResult("无权访问");
        }

        String ignoreCaptchaCheck = System.getProperty("ignoreCaptchaCheck");
        //vpc跳过滑块校验
        if (!"true".equals(ignoreCaptchaCheck)) {
            if (!AppIdConstant.isAnticheatWhiteApp(app.getId())) {
                JsonRender x = validate(request, app, consumer, ip, itemKeyDto);
                if (x != null) {
                    return x;
                }
            }
        }

        ExtrangeBaseProcessor processor = extrangeProcessorManager.getProcessorByType(ItemDto.TypeVirtual);

        return processor.extrange(request, itemKeyDto, app, consumer);
    }



    private JsonRender validate(HttpServletRequest request, AppSimpleDto app, ConsumerDto consumer, String ip, ItemKeyDto itemKeyDto) {
        String validate = request.getParameter("validate");
        if (StringUtils.isEmpty(validate)) {
            ACResultDto asresult;
            try {
                asresult = goodsItemCouponService.checkAnticheat(request, itemKeyDto, app, consumer);
            } catch (Exception e) {
                log.warn("防作弊检查异常,appId= {} consumerId= {}, itemId= {}, ip= {}",
                    app.getId(),consumer.getId(), ItemKeyUtil.getItemId(itemKeyDto),ip, e);
                remoteTokenService.delConsumerPCOrderToken(consumer.getId(), ItemKeyUtil.getAppItemId(itemKeyDto),
                        ItemKeyUtil.getItemId(itemKeyDto));
                // 防作弊异常默认需要验证码校验
                return JsonRender.successResult(nECaptchaService.getCaptchaJson(app.getId()));
            }
            if (asresult != null && !asresult.getPass()) {
                remoteTokenService.delConsumerPCOrderToken(consumer.getId(), ItemKeyUtil.getAppItemId(itemKeyDto),
                        ItemKeyUtil.getItemId(itemKeyDto));
                return getJsonRender(app, asresult);
            }
            // 秒杀新增验证码
            if (captchaManager.isSecKillSkipItem(itemKeyDto)) {
                remoteTokenService.delConsumerPCOrderToken(consumer.getId(), ItemKeyUtil.getAppItemId(itemKeyDto),
                        ItemKeyUtil.getItemId(itemKeyDto));
                return JsonRender.successResult(nECaptchaService.getCaptchaJson(app.getId()));
            }
        } else {
            NECaptchaResultVO result = nECaptchaService.isCaptchaPass(validate, app.getId());
            if(!result.isResult()){
                return JsonRender.failResult("验证码校验失败");
            }
        }
        return null;
    }

    private JsonRender getJsonRender(AppSimpleDto app, ACResultDto asresult) {
        if (asresult.getSubResult() != null
            && (asresult.getSubResult() == ACResultDto.SUB_RESULT_NEED_IDENTIFYING_CODE)) {
            return JsonRender.successResult(nECaptchaService.getCaptchaJson(app.getId()));
        } else {
            return JsonRender.failResult("防作弊策略判断拒绝");
        }
    }
}
