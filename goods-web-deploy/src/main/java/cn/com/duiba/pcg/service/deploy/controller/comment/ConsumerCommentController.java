package cn.com.duiba.pcg.service.deploy.controller.comment;

import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.utils.ReplaceCdnUtil;
import cn.com.duiba.pcg.service.biz.service.comment.ConsumerCommentService;
import cn.com.duiba.pcg.service.biz.vo.comment.ConsumerCommentListVO;
import cn.com.duiba.pcg.service.biz.vo.comment.ConsumerCommentVO;
import cn.com.duibabiz.component.domain.DomainService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhangyongjie on 2021/9/30 3:35 下午
 * 【智几汽车】商品评论
 */
@RestController
@RequestMapping("/gaw/comment")
public class ConsumerCommentController {

    private static final Logger logger = LoggerFactory.getLogger(ConsumerCommentController.class);

    @Autowired
    private ConsumerCommentService consumerCommentService;

    @Autowired
    private DomainService domainService;



    /**
     * 获取用户评论记录
     * @param orderId
     * @param appItemId
     * @param skuId
     * @return
     */
    @GetMapping("/commentRecord")
    public Result<?> queryConsumerCommentRecord(@RequestParam Long orderId,
                                                @RequestParam Long appItemId,
                                                @RequestParam Long skuId){
        try{
            consumerCommentService.checkAuth();
            return ResultBuilder.success(consumerCommentService.hasComment(orderId, appItemId, skuId));
        }catch (BizException e){
            logger.warn("【智几汽车】查询用户评价记录失败",e);
            return ResultBuilder.fail(e.getMessage());
        }catch (Exception e){
            logger.warn("【智几汽车】查询用户评价记录失败",e);
            return ResultBuilder.fail("网络异常");
        }
    }

    /**
     * 用户提交评价
     * @param consumerCommentVO
     * @return
     */
    @PostMapping("/submit")
    public Result<?> submitComment(@RequestBody ConsumerCommentVO consumerCommentVO){
        try{
            logger.info("【智几汽车】用户提交评价，param :{}", JSON.toJSONString(consumerCommentVO));
            consumerCommentService.checkAuth();
            consumerCommentService.submitComment(consumerCommentVO);
            return ResultBuilder.success();
        } catch (BizException e) {
            logger.warn("【智几汽车】用户提交评价失败",e);
            return ResultBuilder.fail(e.getMessage());
        }catch (Exception e){
            logger.warn("【智几汽车】用户提交评价失败",e);
            return ResultBuilder.fail("网络异常");
        }
    }

    /**
     * 查询某个商品下的评价
     * @return
     */
    @GetMapping("/queryItemComment")
    public Result<?> queryItemComment(@RequestParam(defaultValue = "1") Integer pageNo,
                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                      @RequestParam Long appItemId){
        try{
            consumerCommentService.checkAuth();
            Page<ConsumerCommentListVO> data = consumerCommentService.queryPerAppItemCommentList(pageNo, pageSize, appItemId);
            List<ConsumerCommentListVO> list = data.getList();
            //替换cdn
            replaceCdn(list);


            return ResultBuilder.success(data);
        }catch (Exception e){
            logger.warn("【智几汽车】查询商品评价列表失败",e);
            return ResultBuilder.fail("网络异常");
        }
    }

    /**
     * 替换cdn
     * @param list
     */
    private void replaceCdn(List<ConsumerCommentListVO> list) {
        DomainConfigDto systemDomain = domainService.getSystemDomain(RequestLocal.getAppId());
        if (Objects.nonNull(systemDomain)){
            for (ConsumerCommentListVO consumerCommentListVO : list) {
                ConsumerExtraDto consumerExtraDto = consumerCommentListVO.getConsumerExtraDto();
                if (Objects.nonNull(consumerExtraDto)){
                    consumerExtraDto.setAvatar(ReplaceCdnUtil.replaceCdn(systemDomain,consumerExtraDto.getAvatar()));
                }
                List<String> images = consumerCommentListVO.getImages();
                if (CollectionUtils.isNotEmpty(images)){
                    for (int i = 0; i < images.size(); i++) {
                        images.set(i,ReplaceCdnUtil.replaceCdn(systemDomain,images.get(i)));
                    }
                }

            }
        }
    }
}