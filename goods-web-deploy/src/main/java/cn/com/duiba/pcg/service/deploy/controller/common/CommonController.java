package cn.com.duiba.pcg.service.deploy.controller.common;

import cn.com.duiba.api.tools.RandomCodeUtil;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.reqresult.Result;
import cn.com.duiba.biz.tool.duiba.reqresult.ResultBuilder;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duibabiz.component.oss.OssClient;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Date 2021/4/23 10:12 上午
 * @Created by cmm
 */
@RestController
@RequestMapping("gaw/common/")
public class CommonController {
    public static final Long MAX_SIZE = 1024 * 1024 * 5L;
    public static final Long ONE_M = 1024 * 1024 * 1L;
    private static final Logger logger = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private OssClient ossClient;
    @Autowired
    private RemoteConsumerOrderSimpleService orderSimpleService;

    /**
     * 售后上传
     */
    @PostMapping("/postsaleUpload")
    public Result uploadFile(MultipartFile file, Long orderId) throws Exception {
        if (file == null) {
            return ResultBuilder.fail("文件不能不能为空");
        }
        String name = file.getOriginalFilename();
        if (StringUtils.isBlank(name)) {
            return ResultBuilder.fail("图片名称不能为空");
        }
        logger.info("售后单的上传图片开始------orderId：{},fileName:{},fileSize:{}", orderId, name, file.getSize() / 1024);
        String suffix = name.substring(name.lastIndexOf('.') + 1);
        if (!"jpeg".equalsIgnoreCase(suffix) && !"png".equalsIgnoreCase(suffix)&& !"jpg".equalsIgnoreCase(suffix)) {
            logger.warn("售后单的上传图片只支持jpeg、png、jpg格式------suffix：{}",suffix);
            return ResultBuilder.fail("只支持jpeg、png、jpg格式");
        }
        if (file.getSize() >= MAX_SIZE) {
            return ResultBuilder.fail("上传图片大小不能超过5M");
        }
        if (orderId == null) {
            return ResultBuilder.fail("订单号不能为空");
        }
        OrdersDto ordersDto = orderSimpleService.findById(orderId, RequestLocal.getCid()).getResult();
        if (ordersDto == null) {
            return ResultBuilder.fail("订单不存在");
        }
        //校验订单当前状态是否是已发货
        //不评论也要用，评论的订单状态是success
        if (!"AfterSend-started".equals(ordersDto.getFlowworkStage())
            && !"success".equals(ordersDto.getStatus())) {
            logger.warn("当前订单状态不允许上传图片 orderId:{}",orderId);
            return ResultBuilder.fail("当前订单状态不正确");
        }
        String path = "gaw/" + RequestLocal.getCid() + "/" + Long.toHexString(System.currentTimeMillis());
        String[] split = name.split("\\.");
        if (split.length != 2) {
            //如果图片名为xxx.xxx.jpg 直接改成随机数加上.后缀
            name = RandomCodeUtil.getCode(6) + "." + suffix;
        }
        try(InputStream inputStream = file.getInputStream()) {
            String url;
                url = ossClient.upload(null, path, inputStream, name);
//                //压缩图片
//                ByteArrayInputStream bis = compressImage(inputStream);
//                url = ossClient.upload(null, path, bis, name);
            logger.info("售后单的上传图片成功url :{}",url);
            return ResultBuilder.success(url);
        } catch (Exception e) {
            logger.error("upload file error", e);
            return ResultBuilder.fail("系统繁忙，请稍候再试");
        }
    }

    @NotNull
    private ByteArrayInputStream compressImage(InputStream inputStream) throws IOException {
        // 把图片读入到内存中
        BufferedImage bufImg = ImageIO.read(inputStream);
        float ratio = 0.20f;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        //按压缩率进行压缩
        bufImg = Thumbnails.of(bufImg)
                .scale(ratio)
                .rotate(90)
                .asBufferedImage();
        ImageIO.write(bufImg, "png",bos);
        ByteArrayInputStream result = new ByteArrayInputStream(bos.toByteArray());
        return result;
    }
}
