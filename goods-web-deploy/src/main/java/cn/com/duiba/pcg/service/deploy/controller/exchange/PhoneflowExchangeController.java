/**
 * Project Name:platform-coupon-deploy File Name:PhoneflowExchangeController.java Package
 * Name:cn.com.duiba.pcg.service.deploy.controller.exchange Date:2017年3月9日下午3:16:36 Copyright (c) 2017, duiba.com.cn All
 * Rights Reserved.
 */

package cn.com.duiba.pcg.service.deploy.controller.exchange;

import cn.com.duiba.anticheat.center.api.result.goods.ACResultDto;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.manager.ExtrangeProcessorManager;
import cn.com.duiba.pcg.service.biz.processor.extrange.ExtrangeBaseProcessor;
import cn.com.duiba.pcg.service.biz.service.AppExtrangeLimitService;
import cn.com.duiba.pcg.service.biz.service.FormTokenService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemCouponService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.NECaptchaService;
import cn.com.duiba.pcg.service.biz.vo.NECaptchaResultVO;
import cn.com.duiba.pcg.tool.JsonRender;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * ClassName:PhoneflowExchangeController <br/>
 * Date: 2017年3月9日 下午3:16:36 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.6
 * @see
 */
@Controller
@RequestMapping("/phoneflowExchange")
@Api(tags = "流量充值")
public class PhoneflowExchangeController {

    private static final Logger  LOG = LoggerFactory.getLogger(PhoneflowExchangeController.class);
    @Autowired
    private FormTokenService     formTokenService;
    @Autowired
    private GoodsItemDataService goodsItemDataService;
    @Autowired
    private ExtrangeProcessorManager         extrangeProcessorManager;
    @Autowired
    private AppExtrangeLimitService          appExtrangeLimitService;
    @Autowired
    private NECaptchaService nECaptchaService;
    @Autowired
    private GoodsItemCouponService goodsItemCouponService;



    /**
     * @param request
     * @param response
     * @return JsonRender
     */
    @RequestMapping("/exchange")
    @ApiOperation(value = "流量充值", notes = "流量充值", httpMethod = "POST")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "phone", value = "充值的手机号码", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "phoneProvince", value = "号码归属地（如：浙江）", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "phoneCatName", value = "运营商名称（如：中国联通）", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "degreeId", value = "充值档位ID", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "token", value = "token", dataType = "String", required = true, paramType = "query"),
            @ApiImplicitParam(name = "appItemId", value = "appItemId", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "itemId", value = "itemId", dataType = "String", paramType = "query") })
    @ResponseBody
    public JsonRender exchange(HttpServletRequest request, HttpServletResponse response) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        AppSimpleDto app = RequestLocal.getConsumerAppDO();
        String ip = RequestTool.getIpAddr(request);
        if (!formTokenService.checkAndInvalidConsumerToken(consumer.getId(), request.getParameter("token"))) {
            LOG.info("流量充值token校验失败, cid={}, appId={}, ip={}", consumer.getId(), app.getId(), ip);
            return JsonRender.failResult("流量充值token校验失败");
        }
        String appItemIdstr = request.getParameter("appItemId");
        String itemIdstr = request.getParameter("itemId");
        // 获取itemKey
        ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemIdstr, itemIdstr, app);

        // 未登录用户无法下单。
        if (consumer.isNotLoginUser()) {
            return JsonRender.failResult("请先登录");
        }

        String ignoreCaptchaCheck = System.getProperty("ignoreCaptchaCheck");
        //vpc跳过滑块校验
        if(!"true".equals(ignoreCaptchaCheck)){
            JsonRender x = validate(request, consumer, app, ip, itemKeyDto);
            if (x != null) {
                return x;
            }
        }

        // 兑吧商品黑名单过滤
        appExtrangeLimitService.blackListValidate(itemKeyDto, app, consumer);
        // 下单
        ExtrangeBaseProcessor processor = extrangeProcessorManager.getProcessorByType(itemKeyDto.getItemDtoType());
        if (processor == null) {
            return JsonRender.failResult("无法识别的商品类型");
        }
        try {
            return processor.extrange(request, itemKeyDto, app, consumer);
        } catch (GoodsWebException e) {
            LOG.warn("流量兑换失败", e);
            return JsonRender.failResult(e.getMessage());
        }
    }




    private JsonRender validate(HttpServletRequest request, ConsumerDto consumer, AppSimpleDto app, String ip, ItemKeyDto itemKeyDto) {
        String validate = request.getParameter("validate");
        if (StringUtils.isEmpty(validate)) {
            ACResultDto asresult;
            try {
                asresult = goodsItemCouponService.checkAnticheat(request, itemKeyDto, app, consumer);
            } catch (Exception e) {
                LOG.warn("防作弊检查异常,appId=" + app.getId() + ",consumerId=" + consumer.getId() + ",ip="
                                 + ip + ",itemId=" + itemKeyDto.getItem().getId(), e);
                return JsonRender.successResult(nECaptchaService.getCaptchaJson(app.getId()));
            }
            if (asresult != null && !asresult.getPass()) {
                if (asresult.getSubResult() != null
                    && (asresult.getSubResult() == ACResultDto.SUB_RESULT_NEED_IDENTIFYING_CODE)) {
                    return JsonRender.successResult(nECaptchaService.getCaptchaJson(app.getId()));
                } else {
                    return JsonRender.failResult("防作弊策略判断拒绝");
                }
            }
        } else {
            NECaptchaResultVO result = nECaptchaService.isCaptchaPass(validate, app.getId());
            if(!result.isResult()){
                return JsonRender.failResult("验证码校验失败");
            }
        }
        return null;
    }
}
