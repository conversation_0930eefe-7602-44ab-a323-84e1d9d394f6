package cn.com.duiba.custom.boc;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteItemSkuService;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.enums.JiangSuBocErrorCode;
import cn.com.duiba.pcg.service.biz.cache.GoodsCacheService;
import cn.com.duiba.pcg.service.biz.config.JiangSuBocConfig;
import cn.com.duiba.pcg.service.biz.request.boc.JiangSuBocRequest;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.boc.JiangSuBocOrderInfoData;
import cn.com.duiba.pcg.service.biz.response.boc.JiangSuBocPruchaseData;
import cn.com.duiba.pcg.service.biz.response.boc.JiangSuBocSupplyResponse;
import cn.com.duiba.pcg.service.biz.response.supply.SupplyPurchaseResponse;
import cn.com.duiba.pcg.service.biz.service.front.SupplyService;
import cn.com.duiba.pcg.service.deploy.controller.amb.AmbPostsaleController;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.SM3;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/gaw/boc/js")
public class JiangSuBocContoller {

    private static final Logger log = LoggerFactory.getLogger(AmbPostsaleController.class);

    @Autowired
    private SupplyService supplyService;
    @Autowired
    private RemoteAppService remoteAppService;
    @Autowired
    protected RemoteItemSkuService remoteItemSkuService;
    @Autowired
    private JiangSuBocConfig jiangSuBocConfig;
    @Autowired
    private GoodsCacheService goodsCacheService;

    private static final String JS_BOC_APPITEM_JSON = "JSZHSPGXYS_yangyukang";

    private static final SimpleDateFormat SECOND = new SimpleDateFormat("yyyyMMddHHmmss");

    @Resource
    private RemoteDuiBaSupplyOrderService remoteDuiBaSupplyOrderService;

    @PostMapping(value = "/supply/purchase")
    @ResponseBody
    public JiangSuBocSupplyResponse purchase(HttpServletRequest request, @RequestBody JiangSuBocRequest jiangSuBocRequest) {
        log.info("江苏中行采购下单,jiangSuBocRequest={}", JSON.toJSONString(jiangSuBocRequest));
        JiangSuBocPruchaseData result = new JiangSuBocPruchaseData();
        result.setOrderState(getStatus(DuiBaSupplyOrdersStatusEnum.CREATE.getCode()));
        //通用校验
        JiangSuBocSupplyResponse response = checkParams(request, jiangSuBocRequest, result);
        if (response != null) {
            return response;
        }
        String jsonConfig = WhiteAccessUtil.selectWhiteListJsonConfig(JS_BOC_APPITEM_JSON);
        if(StringUtils.isBlank(jsonConfig)) {
            return buildResponse(JiangSuBocErrorCode.E1003.getCode(), JiangSuBocErrorCode.E1003.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        JSONObject jsonObject = JSONObject.parseObject(jsonConfig);
        Long appItemId = jsonObject.getLong(jiangSuBocRequest.getGoodsCode());
        AppSimpleDto appSimpleDto = remoteAppService.getSimpleApp(jiangSuBocConfig.getAppId()).getResult();
        // 新建SupplyPurchaseRequest类，创建拼接方法，拼接参数
        SupplyPurchaseRequest supplyPurchaseRequest;
        try {
            supplyPurchaseRequest = converSupplyRequest(jiangSuBocRequest, appItemId, appSimpleDto);
        } catch (BizException e) {
            return buildResponse(e.getCode(), e.getMessage(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        // 调用supplyService.purchase()方法，获取返回结果
        SupplyPurchaseResponse supplyPurchaseResponse;
        try {
            supplyPurchaseResponse = supplyService.purchase(supplyPurchaseRequest, appSimpleDto);
        } catch (BizException e) {
            log.info("江苏中行采购下单失败,jiangSuBocRequest={}", JSON.toJSONString(jiangSuBocRequest), e);
            if(Objects.equals(e.getCode(), ErrorCode.E1100009.getCode())) {
                return buildResponse(JiangSuBocErrorCode.E1003.getCode(), JiangSuBocErrorCode.E1003.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
            } else if(Objects.equals(e.getCode(), ErrorCode.E1100025.getCode())){
                return buildResponse(JiangSuBocErrorCode.E1007.getCode(), JiangSuBocErrorCode.E1007.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
            } else if(Objects.equals(e.getCode(), ErrorCode.E1100010.getCode())){
                return buildResponse(JiangSuBocErrorCode.E1006.getCode(), JiangSuBocErrorCode.E1006.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
            }
            return buildResponse(JiangSuBocErrorCode.E5000.getCode(), JiangSuBocErrorCode.E5000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        } catch (Exception e) {
            log.warn("江苏中行采购下单异常,jiangSuBocRequest={}", JSON.toJSONString(jiangSuBocRequest), e);
            return buildResponse(JiangSuBocErrorCode.E5000.getCode(), JiangSuBocErrorCode.E5000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        // 判断返回结果是否为空，不为空则返回
        if(supplyPurchaseResponse != null) {
            result.setSupOrderNo(supplyPurchaseResponse.getOrderNum());
            return buildResponse(JiangSuBocErrorCode.E0000.getCode(), JiangSuBocErrorCode.E0000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), supplyPurchaseResponse.getOrderNum()), result);
        }
        return buildResponse(JiangSuBocErrorCode.E5000.getCode(), JiangSuBocErrorCode.E5000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
    }


    @PostMapping(value = "/supply/queryOrder")
    @ResponseBody
    public JiangSuBocSupplyResponse queryOrder(HttpServletRequest request, @RequestBody JiangSuBocRequest jiangSuBocRequest) {
        log.info("江苏中行查询订单入参：{}", JSON.toJSONString(jiangSuBocRequest));
        JiangSuBocOrderInfoData result = new JiangSuBocOrderInfoData();
        //通用校验
        JiangSuBocSupplyResponse response = checkParams(request, jiangSuBocRequest, result);
        if (response != null) {
            return response;
        }
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(jiangSuBocRequest.getOrderNo(), jiangSuBocConfig.getAppId());
        //判断 duiBaSupplyOrdersDto 不为空 直接返回
        if(duiBaSupplyOrdersDto != null) {
            result.setOrderNo(duiBaSupplyOrdersDto.getThirdOrderNum());
            result.setOrderState(getStatus(duiBaSupplyOrdersDto.getOrderStatus()));
            result.setFinishTime(duiBaSupplyOrdersDto.getOrderStatus().equals(DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode()) ? DateUtils.getSecondStr(duiBaSupplyOrdersDto.getGmtModified()) : "");
            result.setAccount(duiBaSupplyOrdersDto.getRechargeAccount());
            JSONObject extra= JSONObject.parseObject(duiBaSupplyOrdersDto.getExtraInfo());
            result.setGoodsCode(extra.getString("goodsCode"));
            log.info("江苏中行查询订单结果：" + JSON.toJSONString(result));
            return buildResponse(JiangSuBocErrorCode.E0000.getCode(), JiangSuBocErrorCode.E0000.getDesc(), queryOrderSign(result), result);
        }
        return buildResponse(JiangSuBocErrorCode.E1004.getCode(), JiangSuBocErrorCode.E1004.getDesc(), queryOrderSign(result), result);
    }

    @Nullable
    private JiangSuBocSupplyResponse checkParams(HttpServletRequest request, JiangSuBocRequest jiangSuBocRequest, Object result) {
        String timestamp = request.getHeader("timestamp");
        if(StringUtils.isBlank(timestamp)) {
            return buildResponse(JiangSuBocErrorCode.E1000.getCode(), JiangSuBocErrorCode.E1000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        //时间格式化处理
        Date date;
        try {
            date = SECOND.parse(timestamp);
        } catch (ParseException e) {
            return buildResponse(JiangSuBocErrorCode.E1000.getCode(), JiangSuBocErrorCode.E1000.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        //判断timestamp 是否 比现在小5分钟
        if(System.currentTimeMillis() - date.getTime() > 5 * 60 * 1000){
            return buildResponse(JiangSuBocErrorCode.E1001.getCode(), JiangSuBocErrorCode.E1001.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        // 测试环境，开始mock后跳过签名校验
        if (SpringEnvironmentUtils.isTestEnv() && StringUtils.equals(jiangSuBocConfig.getDoTest(),"1")){
            return null;
        }
        String sign = request.getHeader("sign");
        if(StringUtils.isBlank(sign)) {
            return buildResponse(JiangSuBocErrorCode.E1002.getCode(), JiangSuBocErrorCode.E1002.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        //验签
        if(!checkSign(jiangSuBocRequest, timestamp, sign)) {
            return buildResponse(JiangSuBocErrorCode.E1002.getCode(), JiangSuBocErrorCode.E1002.getDesc(), purchaseSign(jiangSuBocRequest.getOrderNo(), null), result);
        }
        return null;
    }

    private SupplyPurchaseRequest converSupplyRequest(JiangSuBocRequest jiangSuBocRequest, Long appItemId, AppSimpleDto appSimpleDto) throws BizException {
        List<AppItemSkuDto> appItemSkuDtos = goodsCacheService.findAppItemSkuByAppItemId(appItemId);
        if(CollectionUtils.isEmpty(appItemSkuDtos)) {
            throw new BizException(JiangSuBocErrorCode.E1003.getDesc()).withCode(JiangSuBocErrorCode.E1003.getCode());
        }
        SupplyPurchaseRequest supplyPurchaseRequest = new SupplyPurchaseRequest();
        supplyPurchaseRequest.setAccount(jiangSuBocRequest.getAccount());
        supplyPurchaseRequest.setAppItemId(appItemId);
        supplyPurchaseRequest.setAppKey(appSimpleDto.getAppKey());
        supplyPurchaseRequest.setSkuId(appItemSkuDtos.get(0).getId());
        JSONObject extra = new JSONObject();
        extra.put("activity", jiangSuBocRequest.getActivity());
        extra.put("goodsCode", jiangSuBocRequest.getGoodsCode());
        supplyPurchaseRequest.setExtraInfo(extra.toJSONString());
        supplyPurchaseRequest.setThirdOrderNum(jiangSuBocRequest.getOrderNo());
        return supplyPurchaseRequest;
    }

    /**
     * 验签
     * @param jiangSuBocRequest
     * @param timestamp
     * @param actSign
     * @return
     */
    private boolean checkSign(JiangSuBocRequest jiangSuBocRequest, String timestamp, String actSign) {
        String data = timestamp +
                (StringUtils.isBlank(jiangSuBocRequest.getActivity()) ? "" : jiangSuBocRequest.getActivity()) +
                (StringUtils.isBlank(jiangSuBocRequest.getGoodsCode()) ? "" : jiangSuBocRequest.getGoodsCode()) +
                (StringUtils.isBlank(jiangSuBocRequest.getOrderNo()) ? "" : jiangSuBocRequest.getOrderNo()) +
                (StringUtils.isBlank(jiangSuBocRequest.getAccount()) ? "" : jiangSuBocRequest.getAccount()) + jiangSuBocConfig.getKey();
        String sign = new SM3(jiangSuBocConfig.getKey().getBytes()).digestHex(data);
        log.info("江苏中行验签 sign data:{}, sign= {}, actSign={}", data, sign, actSign);
        if (!ObjectUtil.equals(actSign, sign)) {
            //验签失败
            return false;
        }
        return true;
    }

    /**
     * 下单加签
     * @param orderNo
     * @param supOrderNo
     * @return
     */
    private String purchaseSign(String orderNo, String supOrderNo) {
        String data = orderNo + supOrderNo + jiangSuBocConfig.getKey();
        return new SM3(jiangSuBocConfig.getKey().getBytes()).digestHex(data);
    }

    /**
     * 查询订单返回加签
     * @return
     */
    private String queryOrderSign(JiangSuBocOrderInfoData jiangSuBocOrderInfoData) {
        String data = jiangSuBocOrderInfoData.getOrderNo() + jiangSuBocOrderInfoData.getGoodsCode() + jiangSuBocOrderInfoData.getAccount() +
                jiangSuBocOrderInfoData.getOrderState() + jiangSuBocOrderInfoData.getFinishTime() + jiangSuBocConfig.getKey();
        return new SM3(jiangSuBocConfig.getKey().getBytes()).digestHex(data);
    }



    private JiangSuBocSupplyResponse buildResponse(String code, String message, String sign, Object result) {
        JiangSuBocSupplyResponse response = new JiangSuBocSupplyResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setResult(result);
        response.setSign(sign);
        return response;
    }

    private String getStatus(Integer orderState) {
        if (Objects.equals(orderState, DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode())) {
            return "success";
        } else if(Objects.equals(orderState, DuiBaSupplyOrdersStatusEnum.PROCESSING.getCode()) || Objects.equals(orderState, DuiBaSupplyOrdersStatusEnum.CREATE.getCode()) || Objects.equals(orderState, DuiBaSupplyOrdersStatusEnum.WAIT_SEND.getCode())) {
            return "processing";
        } else {
            return "failed";
        }
    }

}
