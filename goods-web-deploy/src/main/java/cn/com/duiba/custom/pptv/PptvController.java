package cn.com.duiba.custom.pptv;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppBudgetDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.domain.dto.RemainingMoneyDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.dto.GoodsBatchDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.order.center.api.dto.TakeOrderQueueRecordDto;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.service.AppRequestService;
import cn.com.duiba.pcg.service.biz.service.AppSimpleQueryService;
import cn.com.duiba.pcg.service.biz.service.ConsumerItemRenderService;
import cn.com.duiba.pcg.service.biz.service.CouponService;
import cn.com.duibabiz.component.domain.DomainService;
import cn.com.duiba.pcg.service.biz.service.FormTokenService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemActualPriceCalculateService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStatusService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemTimeLimitService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemVipLimitService;
import cn.com.duiba.pcg.service.biz.service.ItemViewService;
import cn.com.duiba.pcg.service.biz.service.QuantityLimitService;
import cn.com.duiba.pcg.service.biz.service.TakeOrderQueueService;
import cn.com.duiba.pcg.service.biz.service.impl.PlatFormCouponServiceImpl;
import cn.com.duiba.pcg.service.biz.util.CreditsCalculateUtil;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.vo.CItemVO;
import cn.com.duiba.pcg.service.biz.vo.ExchangeButtonControlInfoVO;
import cn.com.duiba.pcg.service.biz.vo.ItemKeyVO;
import cn.com.duiba.pcg.service.biz.vo.VipLimitViewInfoVO;
import cn.com.duiba.pcg.service.deploy.controller.MobileController;
import cn.com.duiba.pcg.tool.JsonRender;
import cn.com.duiba.pcg.exception.StatusException;
import cn.com.duiba.wolf.dubbo.DubboResult;

import com.google.common.collect.Maps;

/**
 * Created by xiaoxuda on 2017/7/18.
 */
@Controller
@RequestMapping("/pptv")
public class PptvController {
    private static Logger logger = LoggerFactory.getLogger(PptvController.class);
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Autowired
    private ConsumerItemRenderService consumerItemRenderService;
    @Autowired
    private QuantityLimitService quantityLimitService;
    @Autowired
    private CouponService couponService;
    @Autowired
    private GoodsItemVipLimitService goodsItemVipLimitService;
    @Autowired
    private ItemViewService itemViewService;
    @Autowired
    private GoodsItemTimeLimitService goodsItemTimeLimitService;
    @Autowired
    private FormTokenService            formTokenService;
    @Autowired
    private GoodsItemDataService goodsItemDataService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private AppRequestService   appRequestService;
    @Autowired
    private GoodsItemActualPriceCalculateService    goodsItemActualPriceCalculateService;
    @Autowired
    private AppSimpleQueryService appSimpleQueryService;
    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    private TakeOrderQueueService   takeOrderQueueService;
    @Autowired
    private GoodsItemStatusService goodsItemStatusService;

    @RequestMapping("/appItemDetail")
    public ModelAndView appItemDetail(HttpServletRequest request, HttpServletResponse response) throws Exception{
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
        DomainCrossUtils.crossDomain(request, response, domainConfigDto.getHomeDomain());
        ModelAndView model = new ModelAndView("pptv/appitem_detail");
        model.addObject("domainConfigDto", domainConfigDto);
        try {
            Long appItemId = Long.valueOf(request.getParameter("appItemId"));
            ConsumerDto consumer = RequestLocal.getConsumerDO();
            final AppSimpleDto app = RequestLocal.getConsumerAppDO();

            ItemKeyDto itemKey = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemId.toString(), null, app);
            if(itemKey.getAppItem()==null){
                throw new GoodsWebException("该兑换项已经被删除");
            }
            if (itemKey.getAppItem() != null && !itemKey.getAppItem().getAppId().equals(consumer.getAppId())) {
                throw new GoodsWebException("无权访问");
            }

            model.addObject("type",itemKey.getItemDtoType());
            CItemVO citem = consumerItemRenderService.getCItemVO(itemKey, app);
            model.addObject("citem", citem);
            model.addObject("faicePirce", ItemKeyUtil.getFacePrice(itemKey, app));

            //剩余库存
            Integer remaining = getRemaining(itemKey);
            model.addObject("remainStr", getRemainText(itemKey, remaining));
            //今日剩余库存
            boolean isDayLimit = quantityLimitService.getQuantityLimitItemRemaining(itemKey) != null;
            model.addObject("isDayLimit", isDayLimit);
            if(isDayLimit) {
                model.addObject("todayRemaining", goodsItemStatusService.getStockByCache(itemKey));
            }

            model.addObject("itemKey", itemKey);
            model.addObject("itemKeyVO",new ItemKeyVO(itemKey));

            if(ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())){
                GoodsBatchDto batch = couponService.getGoodsUsingBatch(itemKey);
                model.addObject("batch", batch);
            }

            if(ItemDto.TypeVirtual.equals(itemKey.getItemDtoType())){
                PriceDegreeDto pd = new PriceDegreeDto(itemKey.getAppItem().getCustomPrice());
                boolean singleDegree = pd.isSingleDegree();
                String degreeType = singleDegree ? "singleDegree" : "multiDegree";
                model.addObject("degreeType", degreeType);
                final Map<String,Map<String,String>> des = virtualDegree(app, pd.getCustomDegreeMap());
                model.addObject("priceDegree",des);
                Set<String> keySet = des.keySet();
                List<String> priceList = new ArrayList<>(keySet);
                model.addObject("singleGear", priceList.get(0));
            }

            //会员限制
            Boolean isVipLimit = app.isAppSwitch(AppSimpleDto.SwitchOpenVipLimit);
            model.addObject("isVipLimit", isVipLimit);
            VipLimitViewInfoVO vipLimitViewInfoVO = goodsItemVipLimitService.buildViPlimitViewInfo(itemKey, consumer, app, domainConfigDto);
            model.addObject("limitRemark", vipLimitViewInfoVO.getLimitRemark());
            model.addObject("isCanConsumerExchange", vipLimitViewInfoVO.getIsCanConsumerExchange());

            //兑换按钮文案
            ExchangeButtonControlInfoVO buttonControlInfoVO = itemViewService.buildButtonControlInfo(
                    itemKey, consumer, app, new ExchangeButtonControlInfoVO(), null,citem.getCredits(),null,null);
            // 日预算，月预算，余额验证
            if (itemKey.getItem() != null && itemKey.getItem().getActualPrice() > 0) {
                AppBudgetDto appBudgetDto = remoteAppServiceNew.getAppBudget(app.getId()).getResult();
                checkBudget(itemKey, appBudgetDto, buttonControlInfoVO);
            }
            model.addObject("exchangeStatus", buttonControlInfoVO.getExchangeStatus());
            model.addObject("exchangeEnable", buttonControlInfoVO.getExchangeEnable());
            model.addObject("exchangeText", buttonControlInfoVO.getExchangeText());
            //获取得到cfgMap所需要的参数
            Map<String,Object> cfg = detailCfg(itemKey, citem, app, consumer, buttonControlInfoVO.getExchangeStatus());
            cfg.put("isSeckillItem", ItemKeyUtil.isSecondKill(itemKey)
                    && (ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())
                    || ItemDto.TypeObject.equals(itemKey.getItemDtoType())));
            model.addObject("cfg", cfg);
            model.addObject("token", formTokenService.getConsumerToken(consumer.getId()));
            app.setAppSecret("");
            model.addObject("app", app);
            model.addObject("consumer", consumer);
            model.addObject("isMoney", app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint));

        } catch(GoodsWebException e){
            logger.info("商品信息查询失败, errMsg={}", e.getMessage());
            logger.debug("商品信息查询失败", e);
        }catch (Exception e){
            logger.error("商品信息查询失败", e);
        }
        return model;
    }

    /**
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/queueQuery")
    @ResponseBody
    @ApiOperation(value = "秒杀队列", notes = "秒杀队列", httpMethod = "GET")
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "queueId", value = "queueId", dataType = "Long", required = true, paramType = "query"),
    })
    public JsonRender queueQuery(HttpServletRequest request, HttpServletResponse response){
        DomainConfigDto domainConfigDto = domainService.getSystemDomain(RequestLocal.getAppId());
        DomainCrossUtils.crossDomain(request, response, domainConfigDto.getHomeDomain());
        Long queueId=Long.valueOf(request.getParameter("queueId"));
        TakeOrderQueueRecordDto record=takeOrderQueueService.findRecordByQueueId(queueId);
        if(record!=null){
            //js不能接收太长的整形，需要转换为字符串处理
            return JsonRender.successResult().addResult("status", "success").addResult("orderId", record.getOrderId().toString());
        }else{
            int ret=takeOrderQueueService.checkTakeOrderSuccess(queueId);
            if(ret==TakeOrderQueueService.SECONDKILLFAIL){
                return JsonRender.successResult().addResult("status", "fail");
            }else{
                //排队中
                return JsonRender.successResult().addResult("status", "process");
            }
        }
    }

    /**
     * 兑换项详细页JSON信息封装
     *
     * @throws StatusException
     */
    private Map<String,Object> detailCfg(ItemKeyDto itemKey, CItemVO cItemVO,  AppSimpleDto app, ConsumerDto consumer,Integer exchangeStatus) {
        Map<String, Object> cfgMap = Maps.newHashMap();
        Integer status;
        if (consumer.isNotLoginUser()) {
            status = GoodsItemStatusService.IS_NOTLOGIN;
        } else {
            status = exchangeStatus;
        }
        cfgMap.put("status", status);
        String limitDate = getLimitData(itemKey);
        String startDay = "";
        if (limitDate != null && !limitDate.equals("no")) {
            startDay = limitDate.split(",")[0];
        }
        cfgMap.put("limitDay", goodsItemTimeLimitService.limitDayIsStart(itemKey));
        cfgMap.put("limitdayend", goodsItemTimeLimitService.limitDayIsOver(itemKey));
        cfgMap.put("startday", startDay);
        Boolean ifTimeLimit = (itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit))
                || (itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit));
        cfgMap.put("timedown", ifTimeLimit);
        String startTime = "";
        String endTime = "";
        if (itemKey.getItem() != null && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit)) {
            startTime = itemKey.getItem().getLimitTimeBetween().split("-")[0];
            endTime = itemKey.getItem().getLimitTimeBetween().split("-")[1];
        } else if (itemKey.getAppItem() != null && itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit)) {
            startTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[0];
            endTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[1];
        }
        cfgMap.put("startTime", startTime);
        cfgMap.put("endTime", endTime);
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        cfgMap.put("serverTime", sdf.format(new Date()));
        cfgMap.put("mycredits", consumer.getCredits());
        cfgMap.put("needcredits", PlatFormCouponServiceImpl.convertCreditsUnit4Consumer(cItemVO.getCredits(), app));
        if (itemKey.getAppItem() != null) {
            cfgMap.put("needAccount", itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeNeedUserName));
        } else {
            cfgMap.put("needAccount", false);
        }
        cfgMap.put("itemType", itemKey.getItemDtoType());
        return cfgMap;
    }

    private String getLimitData(ItemKeyDto itemKey) {
        String limitDate = null;
        if (itemKey.isItemMode()) {
            limitDate = itemKey.getItem().getLimitDate();
        }
        if (itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().getLimitDate() != null && !itemKey.getItem().getLimitDate().equals("no")) {
                limitDate = itemKey.getItem().getLimitDate();
            } else if (itemKey.getAppItem().getLimitDate() != null) {
                limitDate = itemKey.getAppItem().getLimitDate();
            }
        } else if (itemKey.isSelfAppItemMode()) {
            limitDate = itemKey.getAppItem().getLimitDate();
        }
        return limitDate;
    }

    /**
     * taglib转为类私有方法
     *
     * <AUTHOR>
     * @throws
     * @modified
     * @datetime 2015年8月25日 下午4:30:57
     */
    private String getRemainText(ItemKeyDto itemKey, Integer remaining) {
        if (goodsItemTimeLimitService.timeLimitSwitch(itemKey)) {
            try {
                if (goodsItemTimeLimitService.canTakeOrder(itemKey)) {
                    return "已兑完";
                }
            } catch (Exception e) {
                logger.error("", e);
            }
        }

        if (itemKey.getItemDtoType().equals(ItemDto.TypeTurntable)
                || itemKey.getItemDtoType().equals(ItemDto.TypeSingleLottery)
                || itemKey.getItemDtoType().equals(ItemDto.TypeManualLottery)
                || itemKey.getItemDtoType().equals(ItemDto.TypeHdtollLottery)) {
            return "无限";
        }

        return MobileController.getFormatRemaining(remaining);
    }

    private Integer getRemaining(ItemKeyDto itemKey){
        Integer remaining = 0;
        if (itemKey.getAppItem() != null && itemKey.isSelfAppItemMode()) {
            remaining = itemKey.getAppItem().getRemaining();
        }
        if (itemKey.getItem() != null
                && (ItemDto.TypeCoupon.equals(itemKey.getItemDtoType()) || ItemDto.TypeObject.equals(itemKey.getItemDtoType()))) {
            remaining = itemKey.getItem().getRemaining();
            // 如果是定向商品或预分配商品,调用查库存
            remaining = getSpeOrPreRemaining(itemKey, remaining);
        }
        return remaining;
    }

    private Integer getSpeOrPreRemaining(ItemKeyDto itemKey, Integer remaining) {
        if (remaining > 0
                && (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeSpecify) || itemKey.getItem().isOpTypeItem(ItemDto.OpTypePreStockSwith))) {
            DubboResult<Long> ret = remoteItemKeyService.findStock(itemKey);
            if (!ret.isSuccess()) {
                throw new GoodsWebException(ret.getMsg());
            }
            remaining = ret.getResult().intValue();
        }
        return remaining;
    }

    /**
     * 虚拟商品档位
     */
    private Map<String, Map<String, String>> virtualDegree(AppSimpleDto app, Map<String, Map<String, String>> map) {
        for (Map.Entry<String, Map<String, String>> entry : map.entrySet()) {
            Map<String, String> hashMap = entry.getValue();
            hashMap.put("creditsShow",CreditsCalculateUtil.convertCreditsUnit4Consumer(Long.valueOf(hashMap.get("credits")), app));
        }
        return map;
    }

    private void checkBudget(ItemKeyDto itemKeyDto, AppBudgetDto appBudgetDto, ExchangeButtonControlInfoVO exchangeButtonControlInfoVO) {
        if (!appRequestService.isDayBudgetEnough(itemKeyDto, appBudgetDto, null)) {
            exchangeButtonControlInfoVO.setExchangeText("今日已兑完");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.StatusTypeEnum.IS_REMAINING_0.getValue());
            exchangeButtonControlInfoVO.setLock(true);
        } else if (!appRequestService.isMonthBudgetEnough(itemKeyDto, appBudgetDto)) {
            exchangeButtonControlInfoVO.setExchangeText("已兑完");
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.StatusTypeEnum.IS_REMAINING_0.getValue());
            exchangeButtonControlInfoVO.setLock(true);
        } else {
            Long price = goodsItemActualPriceCalculateService.calculateMinActualPrice(itemKeyDto).longValue();
            // 2016年1月19加入加钱购商品余额逻辑
            if (itemKeyDto.getItem() != null && itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeIsAmb)) {
                price = price.longValue() - itemKeyDto.getItem().getSalePrice();
            }
            Long money = getConsumerRemainingMoneyDO().getMoney().longValue();
            if (price > money) {
                exchangeButtonControlInfoVO.setExchangeText("已兑完");
                exchangeButtonControlInfoVO.setExchangeEnable(false);
                exchangeButtonControlInfoVO.setExchangeStatus(GoodsItemStatusService.StatusTypeEnum.IS_REMAINING_0.getValue());
                exchangeButtonControlInfoVO.setLock(true);
            }
        }

    }

    private RemainingMoneyDto getConsumerRemainingMoneyDO(){
        return appSimpleQueryService.findRemainingMoneyByDeveloperIdWithCache(RequestLocal.getConsumerAppDO().getDeveloperId());
    }
}
