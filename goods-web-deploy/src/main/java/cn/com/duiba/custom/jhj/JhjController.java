package cn.com.duiba.custom.jhj;

import cn.com.duiba.activity.custom.center.api.dto.jhj.JhjThirdOrderDto;
import cn.com.duiba.activity.custom.center.api.remoteservice.jhj.RemoteJhjThirdOrderService;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.reqresult.Result;
import cn.com.duiba.biz.tool.duiba.reqresult.ResultBuilder;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteAppItemClassifyService;
import cn.com.duiba.goods.center.api.remoteservice.dto.AppItemClassifyDto;
import cn.com.duiba.order.center.api.dto.OrderItemDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrderItemService;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.jhj.JhjConfig;
import cn.com.duiba.pcg.service.biz.dto.jhj.JhjAddressDto;
import cn.com.duiba.pcg.service.biz.dto.lshm.LshmGoodsGroup;
import cn.com.duiba.pcg.service.biz.param.jhj.JhjExpressParam;
import cn.com.duiba.pcg.service.biz.service.ItemFrontDataService;
import cn.com.duiba.pcg.service.biz.vo.AppItemPageDataVO;
import cn.com.duiba.pcg.service.biz.vo.AppItemVO;
import cn.com.duiba.pcg.tool.JsonRender;
import cn.com.duiba.thirdpartyvnew.api.jhj.RemoteJhjService;
import cn.com.duiba.thirdpartyvnew.dto.jhj.request.JhjBaseRequest;
import cn.com.duiba.thirdpartyvnew.dto.jhj.request.JhjExpressRequest;
import cn.com.duiba.thirdpartyvnew.dto.jhj.response.JhjAddressResponseData;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 金徽酒
 * @date 2025/5/14 19:50
 */
@Slf4j
@RequestMapping("/gaw/jhj")
@RestController
public class JhjController {
    //接口appKey，应用的唯一标识
    protected static final String APPKEY_KEY = "appKey";
    //查询个数
    protected static final String PAGESIZE_KEY = "pageSize";
    //当前页
    protected static final String PAGENO_KEY = "pageNo";
    //商品类型：object（实物），coupon(优惠券)，virtual（虚拟商品）
    protected static final String TYPE_KEY = "type";
    //全部和自定义商品类型
    protected static final String CLASSIFY_TYPE = "type";
    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    private ItemFrontDataService itemFrontDataService;
    @Resource
    private RemoteJhjService remoteJhjService;
    @Resource
    private RemoteConsumerExtraService remoteConsumerExtraService;

    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;
    @Autowired
    private JhjConfig jhjConfig;
    @Resource
    private RemoteJhjThirdOrderService remoteJhjThirdOrderService;
    @Resource
    private RemoteAppItemClassifyService remoteAppItemClassifyService;

    /**
     * 获取收货地址
     *
     * @return {@link Result}<{@link List}<{@link JhjAddressDto}>>
     */
    @RequestMapping("/listAddress")
    public Result<List<JhjAddressDto>> listAddress(@RequestParam(required = false) String defaultFlag, @RequestParam(required = false) Long index) {
        ConsumerDto consumerDO = RequestLocal.getConsumerDO();
        JhjBaseRequest jhjBaseRequest = getBaseRequest(consumerDO.getId() + "");
        if (StringUtils.isAnyBlank(jhjBaseRequest.getToken(), jhjBaseRequest.getSerialId(), jhjBaseRequest.getUserId())) {
            return ResultBuilder.fail(ErrorCode.E3100000.getDesc());
        }
        List<JhjAddressResponseData> jhjAddressResponseData = remoteJhjService.listAddress(jhjBaseRequest);
        List<JhjAddressResponseData> resultList;
        if (CollectionUtils.isNotEmpty(jhjAddressResponseData) && Objects.equals("1", defaultFlag)) {
            resultList = jhjAddressResponseData.stream().filter(JhjAddressResponseData::getDefaultFlag).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resultList)) {
                resultList = Collections.singletonList(jhjAddressResponseData.get(0));
            }
        } else if (CollectionUtils.isNotEmpty(jhjAddressResponseData) && index != null) {
            resultList = jhjAddressResponseData.stream().filter(u -> Objects.equals(u.getIndex(), index)).collect(Collectors.toList());
        } else {
            resultList = jhjAddressResponseData;
        }
        return ResultBuilder.success(BeanUtil.copyToList(resultList, JhjAddressDto.class));
    }

    /**
     * 新增收货地址
     *
     * @param param
     * @return {@link Result}<{@link Boolean}>
     */
    @RequestMapping("/insertAddress")
    public Result<Boolean> insertAddress(@RequestBody @Valid JhjExpressParam param) {
        ConsumerDto consumerDO = RequestLocal.getConsumerDO();
        JhjBaseRequest jhjBaseRequest = getBaseRequest(consumerDO.getId() + "");
        if (StringUtils.isAnyBlank(jhjBaseRequest.getToken(), jhjBaseRequest.getSerialId(), jhjBaseRequest.getUserId())) {
            return ResultBuilder.fail(ErrorCode.E3100000.getDesc());
        }
        JhjExpressRequest jhjExpressRequest = BeanUtil.copyProperties(param, JhjExpressRequest.class);
        jhjExpressRequest.setToken(jhjBaseRequest.getToken());
        jhjExpressRequest.setSerialId(jhjBaseRequest.getSerialId());
        jhjExpressRequest.setUserId(jhjBaseRequest.getUserId());
        return ResultBuilder.success(remoteJhjService.insertAddress(jhjExpressRequest));
    }

    /**
     * 修改收货地址
     *
     * @param param
     * @return {@link Result}<{@link Boolean}>
     */
    @RequestMapping("/updateAddress")
    public Result<Boolean> updateAddress(@RequestBody @Valid JhjExpressParam param) {
        ConsumerDto consumerDO = RequestLocal.getConsumerDO();
        JhjBaseRequest jhjBaseRequest = getBaseRequest(consumerDO.getId() + "");
        if (StringUtils.isAnyBlank(jhjBaseRequest.getToken(), jhjBaseRequest.getSerialId(), jhjBaseRequest.getUserId())) {
            return ResultBuilder.fail(ErrorCode.E3100000.getDesc());
        }
        JhjExpressRequest jhjExpressRequest = BeanUtil.copyProperties(param, JhjExpressRequest.class);
        jhjExpressRequest.setToken(jhjBaseRequest.getToken());
        jhjExpressRequest.setSerialId(jhjBaseRequest.getSerialId());
        jhjExpressRequest.setUserId(jhjBaseRequest.getUserId());
        return ResultBuilder.success(remoteJhjService.updateAddress(jhjExpressRequest));
    }

    /**
     * 删除收货地址
     *
     * @param index
     * @return {@link Result}<{@link Boolean}>
     */
    @RequestMapping("/deleteAddress")
    public Result<Boolean> deleteAddress(@RequestParam Long index) {
        ConsumerDto consumerDO = RequestLocal.getConsumerDO();
        JhjBaseRequest jhjBaseRequest = getBaseRequest(consumerDO.getId() + "");
        if (StringUtils.isAnyBlank(jhjBaseRequest.getToken(), jhjBaseRequest.getSerialId(), jhjBaseRequest.getUserId())) {
            return ResultBuilder.fail(ErrorCode.E3100000.getDesc());
        }
        JhjExpressRequest jhjExpressRequest = BeanUtil.copyProperties(jhjBaseRequest, JhjExpressRequest.class);
        jhjExpressRequest.setIndex(index);
        return ResultBuilder.success(remoteJhjService.deleteAddress(jhjExpressRequest));
    }

    /**
     * 前置分页查询当前app 商品列表
     * 支持 类别查询
     *
     * @param request
     * @return JsonRender
     */
    @RequestMapping("/jhjQuerypage")
    @ResponseBody
    public JsonRender jhjQuerypage(HttpServletRequest request) {
        try {
            //参数验证
            //validQueryPageParam(request);
            //应用验证
            AppSimpleDto app = remoteAppServiceNew.getAppByAppKey(request.getParameter(APPKEY_KEY)).getResult();

            if (app == null) {
                throw new GoodsWebException("查询不到应用信息");
            }

            if (!jhjConfig.getAppIds().contains(app.getId())) {
                throw new GoodsWebException("非法appid");
            }
            Integer pageNo = validPageNo(request);
            Integer pageSize = validPageSize(request);
            // 获取商品信息
            AppItemPageDataVO appItemPageDataVO = itemFrontDataService.getItemsByClassifyName(request, app, pageNo, pageSize);
            // 替换图片
            for (AppItemVO appItemVO : appItemPageDataVO.getList()) {
                appItemVO.setSmallImage(appItemVO.getMultiImage());
            }
            // 查询数据
            Page<AppItemClassifyDto> pageList = remoteAppItemClassifyService.findByLimitNewByAppAndName(app.getId(), null, 0, 100);
            List<AppItemClassifyDto> list = pageList.getList();
            Map<String, Long> nameMap = list.stream().collect(Collectors.toMap(AppItemClassifyDto::getName, AppItemClassifyDto::getId));
            List<String> sortedKeys = nameMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByValue())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            // Set<String> classfyNames = pageList.getList().stream().map(AppItemClassifyDto::getName).collect(Collectors.toSet());
            return JsonRender.successResult().addResult("data", JSON.parseObject(JSON.toJSONString(appItemPageDataVO)).fluentPut("groupNameList", sortedKeys));
        } catch (GoodsWebException ge) {
            log.info("前置分页查询当前app 商品列表,param-{}", JSONObject.toJSONString(request.getParameterMap()), ge);
            return JsonRender.failResult(ge.getMessage());
        } catch (Exception e) {
            log.warn("前置分页查询当前app 商品列表,param {}", JSONObject.toJSONString(request.getParameterMap()), e);
            return JsonRender.failResult("系统异常，查询失败");
        }
    }


    @RequestMapping("/getTypeByThirdOrderNo")
    public Result<JSONObject> getTypeByThirdOrderNo(HttpServletRequest request) {
        //pageNo校验
        String thirdOrderNo = request.getParameter("thirdOrderNo");
        JhjThirdOrderDto jhjThirdOrderDto = remoteJhjThirdOrderService.selectByThirdOrderNo(thirdOrderNo);
        JSONObject jsonObject = new JSONObject().fluentPut("type", "jhj_object").fluentPut("orderNum", "");
        if (jhjThirdOrderDto == null) {
            return ResultBuilder.success(jsonObject);
        }
        // 金徽酒实物jhj_object  兑吧实物  db_object 兑吧优惠券 db_coupon 兑吧自充 db_virtual
        String duibaOrderNo = jhjThirdOrderDto.getDuibaOrderNo();
        DubboResult<OrdersDto> ordersDtoDubboResult = remoteConsumerOrderSimpleService.findById(Long.valueOf(duibaOrderNo));
        OrdersDto result = ordersDtoDubboResult.getResult();
        // 自有
        Long itemId = result.getItemId();
        Long appItemId = result.getAppItemId();
        if (itemId != null || jhjConfig.getAppItemIds().contains(String.valueOf(appItemId))){
            String type = result.getType();
            jsonObject.put("type", "db_" + type);
            jsonObject.put("orderNum", result.getOrderNum());
        }
        return ResultBuilder.success(jsonObject);
    }


    protected Integer validPageNo(HttpServletRequest request) throws Exception {
        //pageNo校验
        String pageNoParam = request.getParameter(PAGENO_KEY);
        Integer pageNo = org.apache.commons.lang.StringUtils.isEmpty(pageNoParam) ? 1 : Integer.valueOf(pageNoParam.trim());
        if (pageNo <= 0) {
            throw new GoodsWebException("参数错误，" + PAGENO_KEY + "类型不符合");
        }
        return pageNo;
    }

    protected Integer validPageSize(HttpServletRequest request) throws Exception {
        //pageSize校验
        String pageSizeParam = request.getParameter(PAGESIZE_KEY);
        Integer pageSize = org.apache.commons.lang.StringUtils.isEmpty(pageSizeParam) ? 20 : Integer.valueOf(pageSizeParam.trim());
        if (pageSize <= 0) {
            throw new GoodsWebException("参数错误，" + PAGESIZE_KEY + "类型不符合");
        }
        if (pageSize > 50) {
            throw new GoodsWebException(PAGESIZE_KEY + "请限制为50及50以内");
        }
        return pageSize;
    }


    /**
     * 验证请求参数，验证失败直接抛出异常
     *
     * @param request
     * @return
     */
    protected void validQueryPageParam(HttpServletRequest request) {
        if (org.apache.commons.lang.StringUtils.isBlank(request.getParameter(CLASSIFY_TYPE))) {
            throw new GoodsWebException("参数错误," + CLASSIFY_TYPE + " 必填");
        }
    }

    private JhjBaseRequest getBaseRequest(String userId) {
        DubboResult<ConsumerExtraDto> consumerExtraDtoDubboResult = remoteConsumerExtraService.findByConsumerId(Long.valueOf(userId));
        String json = consumerExtraDtoDubboResult.getResult().getJson();
        if (StringUtils.isBlank(json)) {
            return new JhjBaseRequest();
        }
        JhjBaseRequest jhjBaseRequest = JSON.parseObject(json, new TypeReference<JhjBaseRequest>() {
        });
        jhjBaseRequest.setUserId(userId);
        return jhjBaseRequest;
    }


}
