<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta content="telephone=no" name="format-detection"/>
    <title>${title}</title>
    #parse("/templates/consumer/tempsolver/viewport.vm")
	<link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/coupon/detail/detail_201707051653.css">
    #if(${app.id} == 7585)
    <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/coupon/goods/weiboWallet_detail_201801301455.css" />
    #end
    <link rel="shortcut icon" type="image/x-icon" href="${domainConfig.cdnDomain}/logo.png">
    ${loginProgram}
	<style>
		.powered-logo{width:27px;}
		.theme-color{color: ${color};}
		.theme-bgcolor{background-color: ${color};}
		.theme-bordercolor{border-color: ${color};}
		/* radio */
		.radio-group.cur{border-color:${color}; }
		.radio-group .cur-arrow{border-color: ${color} transparent transparent ${color};}
		.description a {color: ${color};}
        .recommend-location .credits-txt {color: ${color};}

        #if(${app.id} == 7585)
        .item-wrapper .item-info .item-price span.plus{color: ${color};}
        footer .submit[disabled]{background-color: ${color}!important; opacity:0.5;}
        .modal-true{color: ${color};}
        #end
    </style>
    ${loginProgram}
</head>
<body>
    #parse("/templates/consumer/tempsolver/new_nav_header.vm")
    <div id="db-content">
        <div class="swipe" id="swipe">
          <div class="swipe-wrap">
            #foreach($item in $image.split(','))
              <div class="swiper-slide">
                <img alt="" src="${item}" />
              </div>
            #end
          </div>
        </div>
        <header>
            <div class="item-info">
                <div class="item-title">
                    <h3>${title}</h3>
                </div>
                <div class="price">
                    <span class="num theme-color">
                        ${salePrice}
                    </span>
                    <span class="unit theme-color">
                        ${unitName}
                    </span>
				</div>
            </div>
            #if(${dateShow})
            <div class="validDate">
                <i class="arrow"></i>
                <strong>有效期 #if(${app.id} != 7585)：#end</strong>
                <span>
                    ${validStartDate}&nbsp;至&nbsp;${validEndDate}
                </span>
            </div>
            #end
        </header>
        <!-- 详情说明 -->
        <section>
            <p class="title">
                <i class="arrow"></i>
                <span>图文详情：</span>
            </p>
            <div class="description">
                <div class="con">
                    #foreach ($line in $description.split('\n'))
                        #if ($line.startsWith('###'))
                            <h4>${line.substring(3)}</h4>
                        #else
                            ${line}
                        #end
    				#end
                </div>
            </div>
        </section>

		#if (!$itemKey.appItem.isOwner)
		<div class="statement">
			<p>
				<i class="arrow"></i><span>重要声明：</span>
			</p>
			<ul>
				<li>除商品本身不能正常兑换外，商品一经兑换，一律不退还${creditsName}，请用户兑换前仔细参照使用规则、使用说明、有效期等重要信息；</li>
				<li>对于每位用户限兑一次的商品，若发现多个用户账号使用相同手机号或收货地址兑换同一商品，则会被取消订单，被扣除${creditsName}返还；</li>
				<li>通过非法途径获得${creditsName}后进行的正常兑换行为，或不按商品使用规则进行兑换，商家有权不提供服务；</li>
				<li>凡以不正当手段（包括但不限于作弊、扰乱系统、实施网络攻击等）进行兑换，平台有权终止该次兑换</li>
			</ul>
		</div>
		#end
        #if ($poweredBy)
            #if($os=='ios')
                <p class="apple">
                    *兑换项和活动均与设备生产商Apple Inc.无关<br/>
                    Powered by <img class="powered-logo" src="${domainConfig.cdnDomain}/webapp/img/webappLogo.png"/>
                </p>
            #else
                <p class="apple">
                    Powered by <img class="powered-logo" src="${domainConfig.cdnDomain}/webapp/img/webappLogo.png"/>
                </p>
            #end
        #else
            #if($os=='ios')
                <p class="apple">
                    *兑换项和活动均与设备生产商Apple Inc.无关
                </p>
            #end
        #end
        <footer>

            #if(${isLogin})
                #if(${exchangeText} == 1)
                    <button type="button" class="submit theme-bgcolor" disabled>已兑换</button>
                #elseif(${exchangeText} == 2)
                    #if(${earnCreditsUrl}!="")
                        <span class="less-point">您的${creditsName}不足</span>
                        <button type="button" class="submit theme-bgcolor" onclick="window.location.href='${earnCreditsUrl}'">
                        #if(${earnCreditsUrl} != "")
                            ${earnCreditsUrl}
                        #else
                            去赚取更多${creditsName}
                        #end

                        #if(${app.id} == 7585) <i></i> #else >> #end</button>
                    #else
                        <button type="button" class="submit theme-bgcolor" disabled>${creditsName}不足</button>
                    #end

                #elseif(${exchangeText} == 3)
                    <button type="button" class="submit theme-bgcolor" disabled>已兑完</button>
                #elseif(${exchangeText} == 4)
                    <button type="button" class="submit theme-bgcolor">马上兑换</button>

                #end

            #else

                <button type="button" class="submit theme-bgcolor" >请先登录</button>
            #end


        </footer>
    </div>
<script>

var CFG = {
  isLogin: ${isLogin},
  isUseLoginCode: ${isUseLoginCode},
  exchangeText: ${exchangeText},
  unitName: '${unitName}',
  pcgId: '${pcgId}',
  salePrice: '${salePrice}'
};

$javaScriptUtil.obfuscateScript("
  function getDuibaToken(data, callback){
    $.ajax({
      url: '/gaw/ctoken/getToken',
      type: 'post',
      dataType: 'json',
      cache: false,
      success: function (res) {
        if (res.success) {
            eval(res.token);
            var key = '$consumerTokenUtil.getScrectTokenKey()';
            data.token = window[key];
          callback && callback(data);
        } else {
          callback(false);
        }
      },
      error: function() {
        callback(false);
      }
    });
  }");


function loader(cb) {
  var _files = [
    '//cstaticdun.126.net/load.min.js'
  ];
  Loader.async(_files, function () {
    cb && cb();
  });
}
</script>
<script type="text/javascript" src="${domainConfig.cdnDomain}/h5/base2_201703142002.js"></script>
<script src="${domainConfig.cdnDomain}/h5/coupon/detail/detail_201710191717.js"></script>
</body>
</html>
