<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>流量</title>
    <meta name="description" content="收货信息"/>
    <link href="${domainConfig.cdnDomain}/pcduiba/css/common.css" rel="stylesheet">
    <link href="${domainConfig.cdnDomain}/pcduiba/css/phoneFlow.css" rel="stylesheet">
    <link href="${domainConfig.cdnDomain}/pcduiba/css/swiper.css" rel="stylesheet">
</head>
<body>
<div class="main-content">
    <div class="address">
        <div class="block" id="rechargepay-title">流量充值</div>
        <!-- <div class="change-tip" id="change-tip"></div> -->
        <form id="address-valid">
            <div class="form-group phone">
                <label for="" class="control-label">手机号码：</label><input placeholder="请输入需要充值的手机号" class="form-control"
                                                                        type="text" id="userPhone" name="userPhone"/>
            </div>
            <div class="form-group items cf" id="gears">
                <label for="" class="control-label">充值面额：</label>
                <div class="items-warp">
                </div>
            </div>
        </form>
        <div class="form-group btn-grounp cf">
            <button  class="btn btn-save submit" id="btn-save">
                立即兑换
            </button>
        </div>
    </div>
    <div class="recommend">
        <b class="block">好礼推荐</b>
        <div class="swiper-container">
            <div class="swiper-wrapper" id="swiper-wrapper">
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="${domainConfig.cdnDomain}/pcduiba/js/jQuery.min.js"></script>
<script src="${domainConfig.cdnDomain}/pcduiba/js/valid.js"></script>
<script type="text/javascript" src="${domainConfig.cdnDomain}/pcduiba/js/swiper.js"></script>
<script type="text/javascript">
    var CFG =${itemDetail};
    var consumerBingdingInfo = ${consumerBingdingInfo};
    CFG = $.extend({},CFG,consumerBingdingInfo);
    CFG.orderDetailUrl = '${orderDetailUrl}';
</script>
<script type="text/javascript" src="${domainConfig.cdnDomain}/pcduiba/js/base/phoneflowpay.js"></script>
</body>
</html>
