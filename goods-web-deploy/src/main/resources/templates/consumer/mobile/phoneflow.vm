<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="format-detection" content="telephone=no"/>
  <meta charset="utf-8" />
  <meta name="description" content="积分兑换商城" />
  <meta name="keywords" content="积分兑换,积分,兑换" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="format-detection" content="email=no" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  #parse("/templates/consumer/tempsolver/viewport.vm")
  <title>流量充值</title>
  <link rel="shortcut icon" type="image/x-icon" href="${domainConfig.cdnDomain}/logo.png">
  <link rel="dns-prefetch" href="${domainConfig.cdnDomain}">
  <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/directcharge/phoneflow/detail_201707062002.css" />
</head>
<body>
#parse("/templates/consumer/tempsolver/new_nav_header.vm")
<div id="db-content">
</div>


<script type="text/template" id="tpl_detail">
  <style>
    {{if !showCredits}}
    .needprice>span{display:none}.toolbar{display:none}
    {{/if}}
    a.a-click, .navbar li .ui-btn-active,.ui-input-btn div { background-color: {{app.color}}; }
    #list-line-img .link-bt,.coupon .detail a,.jifen-bar .total strong,#list-box .info strong,.toolbar a:active,.toolbar div strong,.line.needprice strong,.line.main-info a,.overbox h4,.ui-radio label.ui-radio-on,.navbar li a,.overbox div p a,.line.message a{ color: {{app.color}}; }
    #list-line-img .link-bt,.navbar li a,.ui-select .ui-focus, .main .ui-focus { border-color: {{app.color}}; }
    .overbox h4 {border-bottom: 1px solid {{app.color}};}
    .overbox span a.confirm {border: 1px solid {{app.color}};background: {{app.color}};}
    .theme-color{color: {{app.color}};}
    .theme-bgcolor{background-color: {{app.color}};}
    .theme-bordercolor{border-color: {{app.color}};}
    .description a {color: {{app.color}};}
  </style>
  <div class="main">
    {{if itemKey.item.showAlert}}
      <div class="alertmsg">{{itemKey.item.alertMessage}}</div>
    {{/if}}
    #if(${app.id} == 37590)
      {{if isVipLimit && !isCanConsumerExchange }}
        <style>.main{padding-top:0;}</style>
        {{if ruleUrl.indexOf('http://') == -1 && ruleUrl.indexOf('https://') == -1}}
          <a onclick="{{ruleUrl}}" class="deposit-fix flex">
        {{else}}
          {{if ruleUrl.indexOf('?') !== -1}}
              <a href="{{ruleUrl}}&uid={{consumer.partnerUserId}}&dbnewopen=" class="deposit-fix flex">
          {{else}}
            <a href="{{ruleUrl}}?uid={{consumer.partnerUserId}}&dbnewopen=" class="deposit-fix flex">
          {{/if}}
        {{/if}}
        <div>
          <span onclick="return" class="deposit-btn">充值押金</span>
        </div>
        <div class="side-auto">
          ，成为哈罗生活馆会员
        </div>
      </a>
      {{/if}}
    #end
    <input class="rate" type="hidden" value="{{app.creditsRate}}" />
    <div class="line ui-input-text" style="margin-top: 0;">
      <span>手机号码</span>
      <input class="tel-input" name="tel" type="tel" maxlength="11" placeholder="请输入需要充值的手机号">
      <input class="province" type="hidden" value="" /><input class="catName" type="hidden" value="" />
    </div>
    <div class="line ui-input-select">
      <span>套餐</span>
      <label for="select-native-2" class="sele"></label>
      <select name="select-native-2" id="areas" style="display:none;"></select>
    </div>
    {{if !isLogin}}
      <a href="javascript:void(0)" class="noAccessBtn theme-bgcolor" onclick="window.requirelogin();">请先登录</a>
    {{else}}
      <div class="line needprice hidden">
        <span>需要<strong></strong>{{unitA}}</span><em></em>
      </div>
      <p class="tip error telError" style="display: none;">同面值产品每月限充1次，不同面值产品可叠加3次.即时生效，当月有效</p>
      <p class="tip error telError2" style="display: none;">即时生效，当月有效</p>
      <div class="line ui-input-btn {{if lock}}duiba-lock {{/if}}  {{if !exchangeEnable}}disabled{{/if}}">
        <div>
          <span>{{exchangeText}}</span>
          <input class="submit" type="submit" name="button" id="button1" >
        </div>
      </div>
    {{/if}}
    {{if poweredBy}}
      {{if isios}}
        <p class="apple">*兑换项和活动均与设备生产商Apple Inc.无关<br/>Powered by <img class="powered-logo" src="${domainConfig.cdnDomain}/webapp/img/webappLogo.png"/></p>
      {{else}}
        <p class="apple">Powered by <img class="powered-logo" src="${domainConfig.cdnDomain}/webapp/img/webappLogo.png"></p>
      {{/if}}
    {{else}}
      {{if isios}}
        <p class="apple">*兑换项和活动均与设备生产商Apple Inc.无关<br/>
      {{/if}}
    {{/if}}
  </div>
  <div data-role="footer" class="toolbar">
    <div>我的{{unit}}：<strong id="usercredits" style="display:none;">{{consumer.credits}}</strong>
      <strong id="usercredits1">{{consumerCreditsShow}}</strong>
    </div>
  </div>
  <div class="overlayer" style="display:none">
    <div class="overbox"><h4>提示</h4><div><h5></h5><p></p></div><span><a href="javascript:void(0)" class="confirm">确定</a><a href="javascript:void(0)" class="cancel">取消</a></span></div>
  </div>
</script>
<script>

var DATA = ${data};

$javaScriptUtil.obfuscateScript("
  function getDuibaToken(data, callback){
    $.ajax({
      url: '/gaw/ctoken/getToken',
      type: 'post',
      dataType: 'json',
      cache: false,
      success: function (res) {
        if (res.success) {
            eval(res.token);
            var key = '$consumerTokenUtil.getScrectTokenKey()';
            data.token = window[key];
          callback && callback(data);
        } else {
          callback(false);
        }
      },
      error: function() {
        callback(false);
      }
    });
  }");

function loader(cb) {
  var _files = [
    '//cstaticdun.126.net/load.min.js'
  ];
  Loader.async(_files, function () {
    cb && cb();
  });
}
</script>
<script type="text/javascript" src="${domainConfig.cdnDomain}/h5/base_201702061551.js"></script>
<script src="${domainConfig.cdnDomain}/h5/directcharge/phoneflow/detail_202107161037.js"></script>
  #parse("consumer/tempsolver/_requirelogin.vm")
</body>
</html>
