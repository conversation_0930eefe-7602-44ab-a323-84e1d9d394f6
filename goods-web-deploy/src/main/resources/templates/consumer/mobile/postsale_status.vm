<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="format-detection" content="telephone=no"/>
  <meta charset="utf-8" />
  <meta name="description" content="积分兑换商城" />
  <meta name="keywords" content="积分兑换,积分,兑换" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="format-detection" content="email=no" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  #parse("/templates/consumer/tempsolver/viewport.vm")
  <title>售后状态</title>
  <link rel="shortcut icon" type="image/x-icon" href="${domainConfig.cdnDomain}/logo.png">
  <link rel="dns-prefetch" href="${domainConfig.cdnDomain}">
  <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/object/goods/postsaleStatus_202105101552.css" />
  #if(${isMobike})
  <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/object/goods/mobike_postsaleStatus_201712181509.css" />
  #end
  <style type="text/css">
    .theme-color{color: ${data.app.color} !important;}
    .theme-bgcolor{background-color: ${data.app.color};}
    .theme-bordercolor{border-color: ${data.app.color};}
    .dui-btn--primary{color: #FFF !important;background-color: ${data.app.color} !important;}
    /* radio */
    .radio-group.cur{border-color:${data.app.color}; }
    .radio-group .cur-arrow{border-color: ${data.app.color} transparent transparent ${data.app.color};}
    .description a {color: ${data.app.color};}
    footer a{color: ${data.app.color};border-color: ${data.app.color};}
    footer a:active{color: #fff;background-color: ${data.app.color};}
  </style>
</head>
<body>
  #parse("/templates/consumer/tempsolver/new_nav_header.vm")
<div id="db-content">
  #if(${data.postsaleOrder.postsaleStatus} == 'wait')
  <header class="success">
    <h3>售后处理中···</h3>
    #if(${data.minites} >= 0)
    <p>您的售后申请将在最长<span>${data.days}</span>天<span>${data.hours}</span>时<span>${data.minites}</span>分内处理</p>
    #end
  </header>
  #elseif(${data.postsaleOrder.postsaleStatus} == 'refunding')
  <header class="success">
    <h3>售后已完成</h3>
    <p>退款将沿支付方式原路返回，请注意查收。</p>
  </header>
  #elseif(${data.postsaleOrder.postsaleStatus} == 'reject')
  <header class="failed">
    <h3>售后已拒绝</h3>
  </header>
  #elseif(${data.postsaleOrder.postsaleStatus} == 'canceled')
  <header class="failed">
    <h3>售后已取消</h3>
  </header>
  #elseif(${data.postsaleOrder.postsaleStatus} == 'agree')
  <header class="success">
    <h3>售后已同意</h3>
  </header>
  #elseif(${data.postsaleOrder.postsaleStatus} == 'success')
  <header class="success">
    <h3>售后已完成</h3>
    <p>退款将沿支付方式原路返回，请注意查收。</p>
  </header>
  #end
  <section>
    <div class="item">
      #if(!${showItems})
      <p class="row"><label>商品名称</label><strong>${data.itemTitle}</strong></p>
      #end
      <p class="row"><label>支付价格</label>
        <strong class="theme-color">
        #if(${isMobike})
          <span id="J_mobikeCredits"></span>
          + ${data.showConsumerPayPrice}元
        #else
          #if(${data.showCredits} != '0')
            ${data.showCredits}${data.unitName}+${data.showConsumerPayPrice}元
          #else
            ${data.showConsumerPayPrice}元
          #end
        #end
        </strong>
        <span class="express">
        （含运费￥${data.expressPrice}
          #if(${data.deductAmount} && ${data.deductAmount} != '0')
            ,已抵扣${data.deductAmount}元
          #end
         )
        </span></p>
    </div>
    #if(${data.postsaleOrder.operateRemark})
    <div class="info">
      <p class="row"><label>商家备注</label><strong>${data.postsaleOrder.operateRemark}</strong></p>
      </div>
    #end
    <div class="info">
      #if(${data.postsaleOrder.finishTime})
      <p class="row">
        <label>完成时间</label>
        <strong class="finishtime">${data.postsaleOrder.finishTime}</strong> 
      </p>
      #end
        
      <p class="row">
        <label>申请时间</label>
        <strong class="gmtcreate">${data.postsaleOrder.gmtCreate}</strong> 
      </p>
      <p class="row">
        <label>联系人</label>
        <strong>${data.postsaleOrder.contactPerson}</strong>
      </p>
      <p class="row">
        <label>联系电话</label>
        <strong>${data.postsaleOrder.contactPhone}</strong>
      </p>
      <p class="row">
        <label>退款金额</label>
        <strong class="theme-color price"><span>￥</span>${data.postsaleMoney}</strong>
      </p>
      #if(${data.postsaleOrder.consumerRemark})
      <p class="row">
        <label>备注</label>
        <strong>${data.postsaleOrder.consumerRemark}</strong>
      </p>
      #end
      #if(${data.pictureUrlList})
      <p class="row posts-order-wrap">
        <label>图片凭证</label>
        #foreach($img in ${data.pictureUrlList})
        <img class="posts-order-img" src="$!{img}" />
        #end
        <div class="posts-order-preview"></div>
      </p>
      #end
    </div>
  </section>
  <footer>
    #if(${data.orderStatus} == 'after_send')
      #if(${data.postsaleOrder.postsaleStatus} == 'wait')
        <a href="/ambPostsale/cancel?orderId=${data.postsaleOrder.ordersId}" class="">取消售后申请</a>
      #elseif(${data.postsaleOrder.postsaleStatus} == 'refunding')
        <a href="${data.appDomain}?dbbackroot" class="">回到首页</a>
      #elseif(${data.postsaleOrder.postsaleStatus} == 'reject')
        <a href="javascript:;" class="applySale">重新发起售后</a>
      #elseif(${data.postsaleOrder.postsaleStatus} == 'canceled')
        <a href="javascript:;" class="applySale">重新发起售后</a>
      #elseif(${data.postsaleOrder.postsaleStatus} == 'agree')
        <a href="/ambPostsale/cancel?orderId=${data.postsaleOrder.ordersId}" class="">取消售后申请</a>
      #elseif(${data.postsaleOrder.postsaleStatus} == 'success')
        <a href="${data.appDomain}?dbbackroot" class="">回到首页</a>
      #end
    #end
  </footer>
</div>

<script>
var DATA = ${data};
var showItems = ${showItems};

</script>
<script src="${domainConfig.cdnDomain}/h5/object/goods/postsaleStatus_202105101552.js"></script>
</body>
</html>
