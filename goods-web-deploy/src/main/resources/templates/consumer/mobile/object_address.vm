<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="format-detection" content="telephone=no"/>
  <title></title>
  <link rel="dns-prefetch" href="${domainConfig.cdnDomain}">
  <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/object/goods/address.debug.css" />
</head>
<body>
  #parse("/templates/consumer/tempsolver/new_nav_header.vm")
<div id="db-content">
  <script type="text/template" id="detail-item">
    {@if params.intent == "1"}
      <div class="item-info">
        <div class="info-group">
            <label>商品名称</label>
            <p>${citem.title}</p>
        </div>
        {@if !isSwitchCloseCredits}
          <div class="info-group">
            <label>支付价格</label>
            <p>
              <span class="num">${citem.credits}${app.unitName}</span>
              {@if (citem.salePrice > 0 || citem.expressPrice > 0)}
                <span class="plus">+</span>
              {@/if}
              <span class="num" id="allPrice">
                {@if (citem.allPrice > 0)}
                  ${citem.allPrice} 元
                {@/if}
              </span>
              <span class="express" id="expressPrice">
                {@if citem.expressPrice > 0}
                  （含运费￥${citem.expressPrice}）
                {@else}  
                  （包邮）
                {@/if}
              </span>
            </p>
          </div>
        {@/if}
      </div>
    {@/if}
    <div class="form-family">
      <div class="form-group">
        <label class="form-label" for="receiver">收货人</label>
        <div class="form-combination">
          <input class="form-control" id="receiver" name="receiver" maxlength="10" value="${consumer.addrName}" placeholder="请输入收货人名字">
        </div>
      </div>
      <div class="form-group tel">
        <label class="form-label" for="tel">手机号码</label>
        <div class="form-combination">
          <input class="form-control" id="tel" name="tel" type="tel" maxlength="11" value="${consumer.addrPhone}" placeholder="收货人的电话，方便联系">
        </div>
      </div>
      <div class="form-group">
        <label class="form-label" for="province">省份</label>
        <div class="form-combination">
            <span class="province-val form-control"></span>
        </div>
        <select class="mock-select" id="province"></select>
      </div>
      <div class="form-group">
        <label class="form-label" for="city">城市</label>
        <div class="form-combination">
            <span class="city-val form-control"></span>
        </div>
        <select class="mock-select" id="city"></select>
      </div>
      <div class="form-group">
        <label class="form-label" for="area">地区</label>
        <div class="form-combination">
            <span class="area-val form-control"></span>
        </div>
        <select class="mock-select" id="area"></select>
      </div>
      <div class="form-group detail">
        <label class="form-label" for="detail">详细地址</label>
        <div class ="form-combination">
          <textarea class="form-control" id="detail" name="detail" type="text" placeholder="请输入街道、门牌等详细地址信息">${consumer.addrDetail}</textarea>
        </div>
      </div>
    </div>
    <div class="btn-group">
      {@if params.intent == "1"}
        <button id="submit" type="button">马上下单</button>
      {@else}
          <input type="hidden" id="referer" value="${referer}">
          <button id="submit" type="button">保存地址</button>
      {@/if}
    </div>
  </script>
</div>

<script src ="${domainConfig.cdnDomain}/db-m/app/address/areaText_c166faa.js"></script>
<script>
var DATA = {};
</script>
<script src="${domainConfig.cdnDomain}/h5/object/goods/address.debug.js"></script>
</body>
</html>
