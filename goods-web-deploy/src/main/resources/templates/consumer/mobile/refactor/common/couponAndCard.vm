<!-- 积分价格展示 -->
<script type="text/template" id="tpl_common_credits">
	{{set marketingPrice = _currentSku.marketingPrice}}
	{{set marketingPrice_1 = _currentSku.marketingPrice_1}}
	{{set marketingPrice_2 = _currentSku.marketingPrice_2}}
	{{set marketingCredits = _currentSku.marketingCredits}}
	{{if _vipShowFlag && _currentSku.vipInfo}}
		{{set marketingPrice = _currentSku.vipInfo.price}}
		{{set marketingPrice_1 = _currentSku.vipPrice_1}}
		{{set marketingPrice_2 = _currentSku.vipPrice_2}}
		{{set marketingCredits = _currentSku.vipInfo.credits}}
	{{/if}}
	{{if marketingPrice > 0}}
		<span class="sale-price">
				<span class="sale-price__yen">&yen;</span><span class="sale-price__price">{{marketingPrice_1}}</span>.{{marketingPrice_2}}
		</span>
		{{if marketingCredits > 0}}
		<span class="sale-price__plus">＋</span>
		{{/if}}
	{{/if}}
	{{if marketingCredits > 0}}
		<span class="goods-info__credits">{{marketingCredits}}</span>
		<span class="goods-info__unit">{{app.unitName}}</span>
	{{/if}}
	{{if (marketingPrice || 0) <= 0 && (marketingCredits || 0) <= 0}}
	<span class="goods-info__credits">免费</span>
	{{/if}}
	{{if vipFlag}}
		{{if _vipShowFlag}}
			<i class="vip-prize-icon {{(marketingPrice || 0) <= 0 && (marketingCredits || 0) <= 0 ? 'v-top' : ''}}"></i>
		{{/if}}
			<div class="one-line">
			{{if _vipShowFlag && _currentSku.priceDiffText}}
				<p class="vip-price-diff">省{{_currentSku.priceDiffText}}</p>
			{{/if}}
		{{if _type === 'coupon'}}
		  {{include 'tpl_show_faceprice'}}
		{{/if}}
		</div>
		{{include 'tpl_vip_upgrade'}}
	{{else}}
	  {{if _type === 'coupon'}}
			{{include 'tpl_show_faceprice'}}
		{{/if}}
	{{/if}}
</script>

<!-- 市场参考价展示 -->
<script type="text/template" id="tpl_show_faceprice">
	{{if typeof citem.hideFacePrice !== 'boolean' || (typeof citem.hideFacePrice === 'boolean' && citem.hideFacePrice === false)}}
		<del class="card-price">市场参考价 <span class="card-price__yen">￥</span>{{_currentSku.facePrice || 0}}</del>
	{{/if}}
</script>

<!-- 会员提示升级 -->
<script type="text/template" id="tpl_vip_upgrade">
	{{if _currentSku.vipInfo && _currentSku.vipInfo.upgradeText}}
  <div class="vip-upgrade-tips dui-flex dui-flex--aligncenter">
    <div class="vip-custom-icon">
      <img src="{{vipGoodsIcon}}" />
    </div>
		<div class="vip-upgrade-tips__title dui-flex__item">
			{{@_currentSku.vipInfo.upgradeText}}
			{{if vipRuleUrl}}
				<a href="{{vipRuleUrl}}" class="vip-upgrade-tips__rule"></a>
			{{/if}}
		</div>
		<div>
			<a class="vip-upgrade-tips__btn" href="{{upgradeUrl}}">
				{{entryText}}
			</a>
		</div>
	</div>
	{{/if}}
</script>

<!-- 会员商品原价购买 -->
<script type="text/template" id="tpl_original_pricebuy_forvip">
	{{if originalPriceBuy && _currentSku.salePrice}}
	<div id="{{ _currentSku.originalPriceBuyBtn ? 'buyBtn' : 'buyBtn-disabled'}}" class="vip-original-price dui-flex dui-flex--aligncenter">
		<div class="dui-flex__item">
				<span>原价购买：</span>&yen;{{_currentSku.salePrice_1}}.{{_currentSku.salePrice_2}}
		</div>
		{{if _currentSku.originalPriceBuyBtn}}
		<i class="dui-icon-handle dui-icon__more"></i>
		{{/if}}
	</div>
	{{/if}}
</script>

<script type="text/template" id="tpl_equity_footer_btn">
	{{if !isLogin}}
		<a class="exchange-btn login-btn" onclick="window.requirelogin();">请先登录</a>
	{{else}}
		<button class="exchange-btn J_Exchange page-btn equity-exc-mdbtn">{{equityExchangeBtnText}}</button>
	{{/if}}
</script>

<!-- 兑换按钮 -->
<script type="text/template" id="tpl_footer_btn">
	{{if !_isSeckill && qqMarketDownloadConfig.download && (app.id == 61921 || app.id == 62781)}}
		{{include 'tpl_yybDownloadFooter'}}
	{{else}}
		{{if _isSeckill && _seckill && _seckill.begin && !_seckill.hasBegin}}
		<button class="exchange-btn seckill-begin-btn" disabled>秒杀未开始</button>
		{{else if _isSeckill && _seckill && _seckill.end && _seckill.hasEnd}}
		<button class="exchange-btn seckill-end-btn" disabled>秒杀已结束</button>
		{{else if !isLogin}}
			<a class="exchange-btn login-btn" onclick="window.requirelogin();">请先登录</a>
		{{else if !exchangeEnable}}
			<button class="exchange-btn disabled-btn" disabled>{{exchangeText}}</button>
		{{else if isVipLimit && !isCanConsumerExchange }}
			{{if ifHidden}}
			<button class="exchange-btn level-btn" disabled>兑换等级不符</button>
			{{else}}
			{{set _symbol = (ruleUrl || '').indexOf('?') !== -1 ? '&' : '?'}}
			<a href="{{ruleUrl}}{{_symbol}}uid={{consumer.partnerUserId}}&dbnewopen=" class="exchange-btn level-btn">如何获得兑换资格<i class="dui-icon-handle dui-icon__more"></i></a>
			{{/if}}
		{{else if !_currentSku.originalPriceBuy && consumer.credits < _currentSku.marketingCredits && app.earnCreditsUrl}}
			{{set _dbnewopen = app.earnCreditsUrl.indexOf('noopen=true') !== -1 ? '' : '&dbnewopen='}}
			{{set _symbol = app.earnCreditsUrl.indexOf('?') !== -1? '&' : '?'}}
			<a href="{{app.earnCreditsUrl}}{{_symbol}}uid={{consumer.partnerUserId}}{{_dbnewopen}}" class="exchange-btn credits-btn">
				{{if app.earnCreditsLetter}}
					{{app.earnCreditsLetter}}
				{{else}}
					{{creditsName}}不足，去赚{{creditsName}}
				{{/if}}<i class="dui-icon-handle dui-icon__more"></i>
			</a>
		{{else if _currentSku.disableExchange}}
			<button class="exchange-btn disabled-btn" disabled>{{_currentSku.disableExchangeText}}</button>
		{{else}}
				<button class="exchange-btn J_Exchange page-btn">{{exchangeText}}</button>
		{{/if}}
	{{/if}}
	<!-- 梦洁定制 -->
	{{if _isMengjieJinLi}}
	<a href="/chome/index">
		<button class="exchange-btn page-btn go-mengjie-home">去积分商城看看</button>
	</a>
	{{/if}}
</script>

<!-- 等级限制 -->
<script type="text/template" id="tpl_vipLimit">
	{{if (isVipLimit && !isCanConsumerExchange) || vipFlag}}
		<div id="usageLimit">
		<div class="dui-article goods-description__article goods-statement {{citem.v1Goods ? 'is-new' : 'is-old'}}">
		<h3 class="title">兑换限制</h3>
		<div class="dui-article__content">
			<div class="dui-flex consumer-limit-remark">
				<div>{{vipFlag ? '会员限购：' : ''}}</div>
				<div class="dui-flex__item">仅限{{limitRemark}}</div>
			</div>
			{{if vipFlag && consumerLimitRemark}}
			<div class="dui-flex consumer-limit-remark">
				<div>每人限购：</div>
				<div class="dui-flex__item">{{@consumerLimitRemark}}</div>
			</div>
			{{/if}}
		</div>
		</div>
	</div>
	{{/if}}
</script>

<script type="text/template" id="tpl_vipLimit_rule">
	{{if (isVipLimit && !isCanConsumerExchange) || citem.usageRule || vipFlag}}
	<section class="dui-section">
		{{include 'tpl_rule'}}
		{{include 'tpl_vipLimit'}}
	</section>
	{{/if}}
</script>

<!-- 商品详情  -->
<script type="text/template" id="tpl_rule">
	{{if citem.usageRule}}
	<div id="usageRule">
		<div class="dui-article goods-description__article goods-statement {{citem.v1Goods ? 'is-new' : 'is-old'}}">
		<h3 class="title">使用规则</h3>
		<div class="dui-article__content">
			{{@citem.usageRule}}
		</div>
		</div>
	</div>
	{{/if}}
</script>

<!-- 商品详情  -->
<script type="text/template" id="tpl_description">
	{{if citem.description}}
	<section class="dui-section" id="usageDescipt">
		<div class="dui-article goods-description__article goods-statement {{citem.v1Goods ? 'is-new' : 'is-old'}}">
		<h3 class="title">商品详情</h3>
		<div class="dui-article__content">
			{{@citem.description}}
		</div>
		</div>
	</section>
{{/if}}
</script>

<!-- 重要声明 -->
<script type="text/template" id="tpl_statement">
	{{if (_isSeckill && !owner) || (!_isSeckill && !(itemKey.appItem && itemKey.appItem.owner))}}
	<section class="dui-panel">
		<div class="goods-statement">
			<h3 class="title">重要声明</h3>
			<ol class="statement-list">
				<li>1.除商品本身不能正常兑换外，商品一经兑换，一律不退还{{creditsName}}，请用户兑换前仔细参照使用规则、使用说明、有效期等重要信息；</li>
				<li>2.对于每位用户限兑一次的商品，若发现多个用户账号使用相同手机号或收货地址兑换同一商品，则会被取消订单，被扣除{{creditsName}}返还；</li>
				<li>3.通过非法途径获得{{creditsName}}后进行的正常兑换行为，或不按商品使用规则进行兑换，商家有权不提供服务；</li>
				<li>4.凡以不正当手段（包括但不限于作弊、扰乱系统、实施网络攻击等）进行兑换，平台有权终止该次兑换。</li>
			</ol>
		</div>
	</section>
	{{/if}}
</script>

<!-- 价格声明 -->
<script type="text/template" id="tpl_price_statement">
	<section class="dui-panel">
		<div class="goods-statement">
			<h3 class="title">价格说明</h3>
			<ol class="statement-list">
				<li><span>划线价格：</span>指商品的专柜价、吊牌价、正品零售价、厂商指导价或该商品的曾经展示过的销售价等，并非原价，仅供参考。</li>
				<li><span>未划线价格：</span>指商品的实时标价，不因表述的差异改变性质。具体成交价格根据商品参加活动，或会员使用优惠券、积分等发生变化，最终以订单结算价格为准。</li>
				<li>此说明仅当出现价格比较时有效。若这件商品针对划线价格进行了特殊说明，以特殊说明为准。</li>
			</ol>
		</div>
	</section>
</script>

<!-- 底部提示文案 -->
<script type="text/template" id="tpl_bottomMsg">
	<section class="bottom-msg">
		{{if isIos}}
		<p>＊商品和活动皆与设备制作商Apple Inc.无关</p>
		{{/if}}
		{{if poweredBy}}
		<p>兑吧提供技术支持</p>
		{{/if}}
	</section>
</script>

<!-- 应用宝下载按钮 -->
<script type="text/template" id="tpl_yybDownloadFooter">
	<section class="dui-footer">
		<div class="dui-footer__bd">
			<button type="button" id="download" class="dui-btn dui-btn--primary dui-btn--lg" >马上下载</button>
		</div>
	</section>
</script>

<!-- 秒杀 -->
<script type="text/template" id="tpl_seckill_credits">
	限时秒杀<!--
	-->{{if _currentSeckillSku.secPrice > 0}}<!--
	-->￥{{_currentSeckillSku.secPrice_1}}.{{_currentSeckillSku.secPrice_2}}<!--
	-->{{if _currentSeckillSku.secCredits > 0}}＋{{/if}}<!--
	-->{{/if}}<!--
	-->{{if _currentSeckillSku.secCredits > 0}}<!--
	-->{{_currentSeckillSku.secCredits}}{{app.unitName}}{{/if}}<!--
	-->{{if (_currentSeckillSku.secPrice || 0) <= 0 && (_currentSeckillSku.secCredits || 0) <= 0}}免费{{/if}}
</script>

<!-- 秒杀倒计时 -->
<script type="text/template" id="tpl_countdown_time">
	{{if _seckill}}
		<i>距开始</i>{{if _seckill.day}}{{_seckill.day}}天{{/if}}<span><span>{{_seckill.hour}}</span></span>:<span><span>{{_seckill.minite}}</span></span>:<span><span>{{_seckill.second}}</span></span>
	{{/if}}
</script>

<script type="text/template" id="tpl_countdown">
	{{if secKillActConf}}
		<div class="seckill-wrap" style="display: none;">
			<div class="seckill-inner dui-flex">
				<i class="dui-icon-handle dui-icon-miaosha"></i>
				<div class="seckill-price dui-flex__item" id="renderSeckillPrice">
					{{include 'tpl_seckill_credits'}}
				</div>
				<div class="seckill-countdown" id="renderCountdown">
					{{include 'tpl_countdown_time'}}
				</div>
			</div>
		</div>
	{{/if}}
</script>
