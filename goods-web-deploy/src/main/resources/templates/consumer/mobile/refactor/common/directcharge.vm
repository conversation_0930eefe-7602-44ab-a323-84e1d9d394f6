
<!-- 兑换提示 -->
<script type="text/template" id="tpl_tip">
  {{if _currentSku.tipText}}
    {{if _currentSku.status}}
    <p class="tip">
    {{else}}
    <p class="tip error">
    {{/if}}
      <i class="dui-icon-handle dui-icon-zhuangtai-shibai"></i>{{_currentSku.tipText}}
    </p>
  {{/if}}
  {{if showCredits}}
  <h2 class="chongzhi-title">需支付
    <span class="credits">您有<span class="num">{{consumerCreditsShow}}</span>{{unit}}可用</span>
  </h2>
  <div class="need">
    <p>
      <span class="need-credit">{{_currentSku.credits}}</span>{{unitA}}
    </p>
  </div>
  {{/if}}
</script>

<!-- 兑换按钮 -->
<script type="text/template" id="tpl_btn">
  {{if !isLogin}}
    <a class="vip-btn login-btn" href="javascript:void(0)" onclick="window.requirelogin();">请先登录</a>
  {{else if !exchangeEnable}}
    <button type="button" class="submit" disabled>
    {{exchangeText}}
    </button>
  {{else if isVipLimit && !isCanConsumerExchange}}
    {{if ifHidden}}
      <a class="vip-btn level-btn" href="javascript:void(0)">兑换等级不符</a>
    {{else}}
      {{if ruleUrl.indexOf("?") != -1}}
        <a class="vip-btn level-btn" href="{{ruleUrl}}&uid={{consumer.partnerUserId}}&dbnewopen=">如何获得兑换资格？</a>
      {{else}}
        <a class="vip-btn level-btn" href="{{ruleUrl}}?uid={{consumer.partnerUserId}}&dbnewopen=">如何获得兑换资格？</a>
      {{/if}}
    {{/if}}
  {{else if _currentSku.needMoreCredits && app.earnCreditsUrl}}
    {{if app.earnCreditsUrl.indexOf("noopen=true") != -1}}
      {{if app.earnCreditsUrl.indexOf("?") != -1}}
        <a href="{{app.earnCreditsUrl}}&uid={{consumer.partnerUserId}}" class="get-credits credits-btn">
        {{if app.earnCreditsLetter}}
          {{app.earnCreditsLetter}}
        {{else}}
          去赚取更多{{unit}}
        {{/if}}
        </a>
      {{else}}
        <a href="{{app.earnCreditsUrl}}?uid={{consumer.partnerUserId}}" class="get-credits credits-btn">
        {{if app.earnCreditsLetter}}
          {{app.earnCreditsLetter}}
        {{else}}
          去赚取更多{{unit}}
        {{/if}}
        </a>
      {{/if}}
    {{else}}
      {{if app.earnCreditsUrl.indexOf("?") != -1}}
        <a href="{{app.earnCreditsUrl}}&uid={{consumer.partnerUserId}}&dbnewopen=" class="get-credits credits-btn">
        {{if app.earnCreditsLetter}}
          {{app.earnCreditsLetter}}
        {{else}}
          去赚取更多{{unit}}
        {{/if}}
        </a>
      {{else}}
        <a href="{{app.earnCreditsUrl}}?uid={{consumer.partnerUserId}}&dbnewopen=" class="get-credits credits-btn">
        {{if app.earnCreditsLetter}}
          {{app.earnCreditsLetter}}
        {{else}}
          去赚取更多{{unit}}
        {{/if}}
        </a>
      {{/if}}
    {{/if}}
  {{else}}
    {{if !_currentSku.status || _currentSku.needMoreCredits}}
    <button type="button" class="submit disabled-btn" disabled>
    {{else}}
    <button type="button" class="submit page-btn">
    {{/if}}
    {{_currentSku.btnText}}
    </button>
  {{/if}}
  </script>

  <!-- 兑吧和ios声明 -->
  <script type="text/template" id="tpl_bottommsg">
    {{if poweredBy || isios}}
    <div class="bottom-msg">
        {{if isios}}
        <p>*兑换项和活动均与设备生产商Apple Inc.无关</p>
        {{/if}}
        {{if poweredBy}}
        <p>兑吧提供技术支持</p>
        {{/if}}
    </div>
    {{/if}}
  </script>