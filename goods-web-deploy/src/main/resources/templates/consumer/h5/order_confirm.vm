
<!DOCTYPE html>
<html>
  <head>
      <title>确认订单</title>
      <link rel="stylesheet" href="${domainConfig.cdnDomain}/h5/goods_custom/pages/order/orderConfirm/index.20191210111657.css"/>
			##<link rel="stylesheet" href="//yun.dui88.com/h5/goods_custom/pages/order/orderConfirm/index_develop.css" />

			#if(${appId} == 57220)
					<!-- 大参林定制 -->
				<link href="${domainConfig.cdnDomain}/h5/goods_custom/pages/order/orderConfirm/custom/dashenlin.**************.css" rel="stylesheet">
			#end

      #parse("/templates/consumer/tempsolver/refactor_head.vm")
  </head>
  <body>
  <div id="db-content"></div>

<!-- main -->
<script type="text/template" id="tpl_main">
	<div class="confirm-order__wraper">
		<section id="top-header">
			{{if itemType == 'object'}}
				{{include 'tpl_address'}}
			{{else itemType == 'virtual'}}
				{{include 'tpl_charge_account'}}
			{{/if}}
		</section>
		{{if shoppingCartFlag}}
			{{include 'tpl_shoppingcart_product'}}
		{{else}}
			{{include 'tpl_product'}}
		{{/if}}
		{{include 'tpl_price'}}
		<section id="J_footer">
			{{include 'tpl_footer'}}
		</section>
		{{include 'tpl_bottomMsg'}}
  </div>
</script>

<script type="text/template" id="tpl_address">
	<section class="dui-zujian__address dui-zujian__wraper" id="J_adreess_btn">
    <div class="dui-panel">
      <div>
      {{if hasAddress}}
      	<div class="panel__specific dui-flex dui-flex--aligncenter">
					<div class="dui-flex__item">
						<div>
							<span class="address-name">{{addrName}}</span>
							<span class="address-phone">{{addrPhone}}</span>
						</div>
						<div class="dui-flex address-detail">
							<i class="dui-icon dui-icon-address2"></i>
							<p>{{addrText}}</p>
						</div>
						{{if !lastLevel}}
							<div class="error-tips">收货信息不全，请重新编辑收货信息</div>
						{{/if}}
            </div>
            <div>
            	<i class="dui-icon dui-icon-arrow"></i>
            </div>
          </div>
        {{else}}
          <div class="panel__blank dui-flex dui-flex--aligncenter">
            <div class="dui-flex__item">
              <span class="dui-font-bold">请添加收货地址</span>
            </div>
            <div>
              <i class="dui-icon dui-icon-arrow"></i>
            </div>
          </div>
        {{/if}}
      </div>
    </div>
  </section>
</script>

<!-- db-zujian - 通用商品信息 -->
<script type="text/template" id="tpl_product">
	<div class="dui-flex dui-flex-column db-zujian-product__wraper">
    <div class="dui-panel db-zujian-product--top">
		{{each items item}}
			<div class="dui-flex dui-flex-row dui-panel__bd prouct-item">
					<div class="product-img">
						<img src="{{item.sku.imgUrl || item.multiImage}}" alt="">
							{{@ item.sku.status | IvalidShadow}}
					</div>
					<div class="product-detail dui-flex dui-flex-column dui-flex__item">
						<div class="product-detail__title  {{item.sku.status|productInvalidColor}}">
							{{if item.vipFlag}}<div  class="vip-custom-icon"><img src="{{vipInfo.vipGoodsIcon}}"/></div>{{/if}}{{item.title}}
						</div>
          <div>
          {{if item.sku && item.sku.attr && item.sku.attr[0] && (item.sku.attr[0].name || item.sku.attr[0].value)}}

						{{if itemType == 'virtual'}}
							<div class="product-detail__sku">
							{{each item.sku.attr attrItem}}
								<span class="sku__item">
										<span>{{attrItem.value}}</span>
								</span>
							{{/each}}
							</div>
            {{else}}
						<div class="product-detail__sku">
								{{each item.sku.attr attrItem}}
										<span class="sku__item">
												<span>{{attrItem.name ? (attrItem.name + ':') : ''}}</span>
												<span>{{attrItem.value}}</span>
										</span>
								{{/each}}
						</div>
								{{/if}}
						{{/if}}
					</div>
					{{if _isSeckill}}
					<div class="seckill-icon"><span>秒杀</span></div>
					{{/if}}
					{{if item.sku.status == '1'}}
					<div class="product-detail__price dui-flex dui-flex--aligncenter dui-flex-space-between">
							<div class="dui-font--red">
									<span class="price-detail">
											{{if  item.sku && item.sku.salePrice}}
													<span class="rmb">{{@+item.sku.salePrice | formatPrice}}</span>
											{{/if}}
											{{if  item.sku.salePrice &&  item.sku.credits}}
													<span class="small-font">+</span>
											{{/if}}
											{{if item.sku.credits}}
													<span class="credits">
															{{item.sku.credits}}<span class="small-font">{{orderCreditsUnit}}</span>
													</span>
											{{/if}}
											{{if item.sku.credits <= 0 && item.sku.salePrice <= 0}}
												<span class="credits">免费</span>
											{{/if}}
												</span>
										</div>
                    <span class="item__qulity {{item.sku.status|productInvalidColor}}">x{{item.sku.quantity}}</span>
                  </div>
                  {{else}}
                  <div>
                    <div class="product-detail__price dui-flex dui-flex--aligncenter dui-flex-space-between">
											<div>
												{{if item.sku.status == '2'}}
														<div>
																<span class="ivalid-tip">商品已下架</span>
														</div>
												{{else if item.sku.status == '3'}}
													<div>
														<span class="ivalid-tip">商品已售罄</span>
													</div>
												{{else if item.sku.status == '4'}}
													<div>
															<span class="ivalid-tip">超出配送范围</span>
													</div>
												{{/if}}
                        </div>
                        <span class="{{item.sku.status | productInvalidColor}}">x{{item.sku.quantity}}</span>
                    </div>
                  </div>
                {{/if}}
              </div>
          </div>
        {{/each}}
        {{include 'tpl_expressType'}}
				##{{include 'tpl_leaveword'}}
      </div>
  </div>
</script>

<!-- db-zujian - 价格信息 -->
<script type="text/template" id="tpl_price">
	<div class="db-zujian-price__wraper" id="J_price">
		<div class="dui-panel">
				<div class="dui-panel__bd dui-flex dui-flex-column">
						{{if +orderCredits}}
							<div class="product-credits">
									<span>账户可用{{useCreditsUnit}}</span>
									<span>{{usableCredits}}</span>
							</div>
						{{/if}}
						<div class="product-price dui-flex dui-flex-space-between">
								<span>商品金额</span>
								{{set showPrice = consumerPayPrice - expressPrice}}
								{{if showPrice <= 0 && orderCredits <= 0}}
										<span class="dui-font-bold">免费</span>
								{{else}}
										<span class="dui-font-bold">
												{{showPrice > 0 ? '￥' + (showPrice / 100).toFixed(2) : ''}}
												{{if showPrice > 0 && +orderCredits}}
														+
												{{/if}}
												{{if +orderCredits}}
														{{orderCredits}}<span class="credits__unit">{{orderCreditsUnit}}</span>
												{{/if}}
										</span>
								{{/if}}
						</div>
						{{if itemType == 'object'}}
							<div class="product-Freight dui-flex dui-flex-space-between">
									<span>运费</span>
									<span class="product-Freight-num">+ ¥{{(+expressPrice / 100 || 0).toFixed(2)}}</span>
							</div>
						{{/if}}
				</div>
		</div>
  </div>
</script>

<script type="text/template" id="tpl_footer">
	<section class="dui-zujian__footer">
		{{if itemType == 'object'}}
			{{if hasAddress}}
				{{if !lastLevel}}
					<div class="footer_address footer_address--error">收货信息不全，请重新编辑收货信息</div>
				{{else}}
					<div class="footer_address">{{addrText}}</div>
				{{/if}}
			{{else}}
				<div class="footer_address">
						<span class="dui-font--red">需要添加收货地址哦</span>
				</div>
			{{/if}}
    {{/if}}
    <div class="dui-flex dui-flex--aligncenters dui-zujian__price">
      <div class="dui-flex__item">
				<div class="dui-font-bold price-wrap {{_vipPriceTextDiff ? 'price-wrap--diff' : ''}}">
						{{@ consumerPayPrice | formatPrice}}
						{{if +consumerPayPrice && +orderCredits}}
								<span class="small-font">+</span>
						{{/if}}
						{{if +orderCredits}}
							{{orderCredits}}<span class="smaller-font">{{orderCreditsUnit}} </span>
						{{/if}}
				</div>
				{{if _vipPriceTextDiff}}
					<div class="vip-price-diff">{{_vipPriceTextDiff}}</div>
				{{/if}}
      </div>
			<div class="btn-wrap defalut" id="J_doExchange">
				提交订单
			</div>
    </div>
  </section>
</script>

<script type="text/template" id="tpl_charge_account">
	{{if virtualInputShow}}
		<div class="dui-zujian-charge dui-zujian__wraper">
			<div class="dui-panel">
				<div class="account-container">
					<div class="product-leaveword dui-panel__bd dui-flex dui-flex--aligncenter">
						<label>{{virtualInputTitle}}：</label>
						<div class="dui-flex__item dui-font-medium word__wrap">
							<input id="J_account" type="text" maxlength="50" value="{{account}}"  placeholder="{{virtualInputPlaceholderText}}" onfocus="this.select()">
						</div>
						<i class="dui-icon dui-icon--small dui-icon-input-clear J_clear_btn"></i>
					</div>
				</div>
			</div>
    </div>
  {{/if}}
</script>


<!-- 购物车商品列表 -->
<script type="text/template" id="tpl_shoppingcart_product">
	<div class="dui-flex dui-flex-column db-zujian-product__wraper">
    <div class="dui-panel db-zujian-product--top">
		{{each list item}}
			<div class="dui-flex dui-flex-row dui-panel__bd prouct-item J_cartItem_skuId{{item.skuId}}"
				data-skuInfo="{{item.skuInfo}}" data-image="{{item.smallImage}}" data-title="{{item.title}}"
				data-quantity="{{item.quantity}}">
				<div class="product-img">
					<img src="{{item.smallImage}}" alt="">
				</div>
				<div class="product-detail dui-flex dui-flex-column dui-flex__item">
					<div class="product-detail__title">
						{{if item.vipFlag}}<div class="vip-custom-icon"><img src="{{vipGoodsIcon}}"/></div>{{/if}}{{item.title}}
					</div>
					{{if item.skuInfo && item.skuInfo[0] && (item.skuInfo[0].attrName || item.skuInfo[0].attrValue)}}
					<div class="product-detail__sku">
						{{each item.skuInfo attrItem}}
							<span class="sku__item">
								<span>{{attrItem.attrName ? (attrItem.attrName + ':') : ''}}</span>
								<span>{{attrItem.attrValue}}</span>
							</span>
						{{/each}}
					</div>
					{{/if}}
					<div class="J_unavailableReason"></div>
					<div class="product-detail__price dui-flex dui-flex--aligncenter dui-flex-space-between">
						<div class="dui-font--red">
							<span class="price-detail">
								{{if item.money}}
									<span class="rmb">{{@+item.money | formatPrice}}</span>
								{{/if}}
								{{if item.money && +item.credits}}
										<span class="small-font">+</span>
								{{/if}}
								{{if +item.credits}}
									<span class="credits">
										{{item.credits}}<span class="small-font">{{orderCreditsUnit}}</span>
									</span>
								{{/if}}
							</span>
						</div>
						<span class="item__qulity">x{{item.quantity}}</span>
					</div>
				</div>
      </div>
		{{/each}}
		{{include 'tpl_expressType'}}
		<!-- {{include 'tpl_leaveword'}} -->
		</div>
  </div>
</script>

<!-- 配送方式 -->
<script type="text/template" id="tpl_expressType">
{{if expressTypeDesc}}
	<div class="product-express-type  dui-panel__bd dui-flex">
			<span class="dui-flex__item">配送方式</span>
			<span class="dui-font-bold">{{expressTypeDesc}}</span>
	</div>
{{/if}}
</script>

<!-- 买家留言 -->
<script type="text/template" id="tpl_leaveword">
	<div class="product-leaveword  dui-panel__bd dui-flex dui-flex--aligncenter">
		<label>买家留言</label>
		<div class="dui-flex__item word__wrap">
				<input maxlength="100" type="text" id="J_consumer_remark" placeholder="填写内容已和卖家协商确认" onfocus="this.select()">
		</div>
		<i class="dui-icon dui-icon--small dui-icon-input-clear J_clear_btn"></i>
	</div>
</script>


<!-- 地域限制商品弹窗 -->
<script type="text/template" id="tpl_areaLimt">
	<div id="J_areaLimitModal" class="arealimit-modal">
		<div class="arealimit-modal__shade"></div>
		<div class="arealimit-modal__container">
			<div class="arealimit-modal__hd">
				<div class="arealimit-modal__desc">{{errorObj.tips}}</div>
				<div class="arealimit-modal__close" id="J_areaLimitModalClose">
					<i class="dui-icon-handle dui-icon-danchuang-guanbi"></i>
				</div>
			</div>
			<div class="arealimit-modal__bd">
				{{each errorObj._errorInfoList item}}
				<div class="arealimit-modal-item dui-flex dui-flex--aligncenter">
					<div class="arealimit-modal-item__img" style="background-image:url({{item.image}})"></div>
					<div class="arealimit-modal-item__title dui-flex__item dui-flex dui-flex-column">{{item.title}}
					<div class="product-detail__sku dui-flex dui-flex-space-between">
						<div>
						{{if item.skuInfo && item.skuInfo[0] && (item.skuInfo[0].attrName || item.skuInfo[0].attrValue)}}
							{{each item.skuInfo attrItem}}
							<span class="sku__item">
								<span>{{attrItem.attrName ? (attrItem.attrName + ':') : ''}}</span>
								<span>{{attrItem.attrValue}}</span>
							</span>
							{{/each}}
						{{/if}}
						</div>
						<span class="item__qulity qulity__error__modal">x{{item.quantity}}</span>
					</div>
					</div>
				</div>
				{{/each}}
			</div>
			<div class="arealimit-modal__ft">
				<div class="arealimit-modal__btn" id="J_areaLimitBtn">我知道了</div>
			</div>
		</div>
	</div>
</script>

<!-- 底部提示文案 -->
<script type="text/template" id="tpl_bottomMsg">
	<section class="bottom-msg">
		{{if isIos}}
		<p>＊商品和活动皆与设备制作商Apple Inc.无关</p>
		{{/if}}
		{{if poweredBy}}
		兑吧提供技术支持
		{{/if}}
	</section>
</script>

    <script>
        var DATA = {
          appId: '$!{appId}',
          appItemId: '$!{appItemId}',
          itemId: '$!{itemId}',
          appSkuId: '$!{appSkuId}',
          itemType: '$!{itemType}',
          payType: '$!{payType}',
          degreeId: '$!{degreeId}',
          tradeDomain: '$!{domainConfig.tradeDomain}',
          goodsDomain: '$!{domainConfig.goodsDomain}',
		      vipFlag: ${vipFlag},
          shoppingCartFlag: ${shoppingCartFlag},
          tokenKey: '$!{tokenKey}',
			    pid: '$!{pid}',
          themeColor:'$!{themeColor}',
					poweredBy: ${poweredBy},
			appPayChannelTypes :'${appPayChannelTypes}',
			yinlianChannelType :${yinlianChannelType},
			bindPhone:'$!{bindPhone}'
		}

        $javaScriptUtil.obfuscateScript("
        function getDuibaToken(data, callback){
            $.ajax({
                url: '/gaw/ctoken/getToken',
                type: 'post',
                dataType: 'json',
                cache: false,
                success: function (res) {
                    if (res.success) {
                        eval(res.token);
                        var key = '$consumerTokenUtil.getScrectTokenKey()';
                        data.token = window[key];
                        callback && callback(data);
                    } else {
                        callback(false);
                    }
                },
                error: function() {
                    callback(false);
                }
            });
        }");

    </script>
  <script type="text/javascript" src="${domainConfig.cdnDomain}/h5/base2_201912181447.js"></script>
  <script src="${domainConfig.cdnDomain}/h5/goods_custom/pages/order/orderConfirm/index.1235af55.js"></script>
  ##<script src="//yun.dui88.com/h5/goods_custom/pages/order/orderConfirm/index.dev.js"></script>
  <script src="//cstaticdun.126.net/load.min.js"></script>
  </body>
</html>
