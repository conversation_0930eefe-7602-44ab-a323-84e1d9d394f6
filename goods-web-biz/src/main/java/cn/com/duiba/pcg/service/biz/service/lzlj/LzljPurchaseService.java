package cn.com.duiba.pcg.service.biz.service.lzlj;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.order.center.api.constant.StatusIntEnum;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.service.biz.cache.GoodsCacheService;
import cn.com.duiba.pcg.service.biz.config.LzljObjectConfig;
import cn.com.duiba.pcg.service.biz.dto.lzlj.InventoryResult;
import cn.com.duiba.pcg.service.biz.dto.lzlj.LzljBaseRequest;
import cn.com.duiba.pcg.service.biz.dto.lzlj.LzljBaseResponse;
import cn.com.duiba.pcg.service.biz.dto.lzlj.LzljInventoryRequest;
import cn.com.duiba.pcg.service.biz.dto.lzlj.LzljOrderSyncRequest;
import cn.com.duiba.pcg.service.biz.dto.lzlj.OrderSyncResult;
import cn.com.duiba.pcg.service.biz.dto.purchase.MallPurchaseAddressParam;
import cn.com.duiba.pcg.service.biz.dto.purchase.MallPurchaseItemParam;
import cn.com.duiba.pcg.service.biz.dto.purchase.MallPurchaseParam;
import cn.com.duiba.pcg.service.biz.dto.purchase.MallPurchaseResult;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.supply.SupplyPurchaseResponse;
import cn.com.duiba.pcg.service.biz.service.front.SupplyService;
import cn.com.duiba.pcg.service.biz.service.purchase.MallPurchaseService;
import cn.com.duiba.pcg.service.biz.util.LzljSignUtils;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 泸州老窖采购服务
 */
@Slf4j
@Service
public class LzljPurchaseService {

    @Resource
    private SupplyService supplyService;

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private GoodsCacheService goodsCacheService;
    @Resource
    private RemoteAppService remoteAppService;

    @Resource
    private LzljObjectConfig lzljObjectConfig;

    @Resource
    private RemoteDuiBaSupplyOrderService remoteDuiBaSupplyOrderService;

    @Resource
    private RemoteAppItemSkuService remoteAppItemSkuService;

    @Resource
    private MallPurchaseService mallPurchaseService;


    /**
     * 采购下单
     * @param req 采购请求
     * @return 采购响应
     */
    public LzljBaseResponse<?> mallPurchase(LzljBaseRequest<LzljOrderSyncRequest> req) {
        LzljOrderSyncRequest request = req.getData();
        try {
            // 签名校验
            checkSign(req);
            // 获取并校验商品信息
            String thirdMainId = req.getData().getId();
            List<LzljOrderSyncRequest.SaleOrderItem> saleOrderItemList = request.getSaleOrderItemList();
            Long appId = null;
            for (LzljOrderSyncRequest.SaleOrderItem saleOrderItem : saleOrderItemList) {
                Triple<Long, Long, Long> longLongLongTriple = checkAndGetAppItemInfo(saleOrderItem.getProdCode());
                appId = longLongLongTriple.getLeft();
            }
            // 幂等
            LzljBaseResponse<?> lzljBaseResponse = successCheck(appId, thirdMainId);
            if(lzljBaseResponse != null){
                return lzljBaseResponse;
            }
            // 构建采购请求参数
            MallPurchaseParam mallPurchaseParam = builMallPurchaseParam(request,appId);
            // 发起采购下单
            MallPurchaseResult mallPurchaseResult = mallPurchaseService.mallPurchase(mallPurchaseParam);
            return buildPurchaseResponse(mallPurchaseResult, thirdMainId);
        }catch (BizException bizException) {
            log.warn("泸州老窖 采购下单 业务异常 request={}", JSONObject.toJSONString(request), bizException);
            return buildExceptionResponse(bizException);
        } catch (Exception e) {
            log.error("泸州老窖 采购下单 异常 request={}", JSONObject.toJSONString(request), e);
            return buildExceptionResponse(e);
        }
    }

    /**
     * 构建采购请求参数
     * @param request 泸州老窖下单请求
     * @param appId 应用id
     * @return 请求参数
     */
    private MallPurchaseParam builMallPurchaseParam(LzljOrderSyncRequest request,Long appId) {
        MallPurchaseParam mallPurchaseParam = new MallPurchaseParam();
        mallPurchaseParam.setAppId(appId);
        mallPurchaseParam.setThirdOrderNum(request.getId());
        // 构架商品信息
        ArrayList<MallPurchaseItemParam> mallPurchaseItemParams = new ArrayList<>();
        for (LzljOrderSyncRequest.SaleOrderItem saleOrderItem : request.getSaleOrderItemList()) {
            MallPurchaseItemParam mallPurchaseItemParam = new MallPurchaseItemParam();
            String[] split = saleOrderItem.getProdCode().split("_");
            mallPurchaseItemParam.setAppItemId(Long.parseLong(split[0]));
            mallPurchaseItemParam.setAppItemSkuId(Long.parseLong(split[1]));
            mallPurchaseItemParam.setQuantity(saleOrderItem.getQty());
            mallPurchaseItemParam.setThirdSubOrderId(saleOrderItem.getId());
            mallPurchaseItemParams.add(mallPurchaseItemParam);
        }
        mallPurchaseParam.setItemParams(mallPurchaseItemParams);
        // 构建地址信息
        MallPurchaseAddressParam mallPurchaseAddressParam = new MallPurchaseAddressParam();
        mallPurchaseAddressParam.setAddrName(request.getCustomConsignee());
        mallPurchaseAddressParam.setAddrPhone(request.getCustomMobilePhone());
        mallPurchaseAddressParam.setAddrProvince(request.getProvince());
        mallPurchaseAddressParam.setAddrCity(request.getCity());
        mallPurchaseAddressParam.setAddrArea(request.getDistrict());
        mallPurchaseAddressParam.setAddrStreet(request.getStreet());
        mallPurchaseAddressParam.setAddrDetail(request.getCustomAddr());
        mallPurchaseParam.setAddressParam(mallPurchaseAddressParam);
        return mallPurchaseParam;
    }


    /**
     * 采购下单
     * @param req 采购请求
     * @return 采购响应
     */
    public LzljBaseResponse<?> purchase(LzljBaseRequest<LzljOrderSyncRequest> req) {
        LzljOrderSyncRequest request = req.getData();
        try {
            // 签名校验
            checkSign(req);
            // 请求参数校验
            checkParam(request);
            // 获取并校验商品信息
            String thirdMainId = req.getData().getId();
            LzljOrderSyncRequest.SaleOrderItem saleOrderItem = request.getSaleOrderItemList().get(0);
            Triple<Long, Long, Long> appItemInfo = checkAndGetAppItemInfo(saleOrderItem.getProdCode());
            // 填充标准采购参数
            Long appId = appItemInfo.getLeft();
            AppSimpleDto app = remoteAppService.getSimpleApp(appId).getResult();
            SupplyPurchaseRequest purchaseRequest = new SupplyPurchaseRequest();
            String thirdOrderNum = saleOrderItem.getId();
            purchaseRequest.setThirdOrderNum(thirdOrderNum);
            purchaseRequest.setAppItemId(appItemInfo.getMiddle());
            purchaseRequest.setSkuId(appItemInfo.getRight());
            // 填充收货信息
            purchaseRequest.setName(request.getCustomConsignee());
            purchaseRequest.setPhone(request.getCustomMobilePhone());
            purchaseRequest.setProvince(request.getProvince());
            purchaseRequest.setCity(request.getCity());
            purchaseRequest.setDistrict(request.getDistrict());
            purchaseRequest.setStreet(request.getStreet());
            purchaseRequest.setAddress(request.getCustomAddr());
            purchaseRequest.setTimestamp(String.valueOf(System.currentTimeMillis()));
            purchaseRequest.setAppKey(app.getAppKey());
            purchaseRequest.setSkipSign(true);
            purchaseRequest.setLzljMainId(request.getId());
            // 幂等
            LzljBaseResponse<?> lzljBaseResponse = successCheck(appId, thirdOrderNum, thirdMainId);
            if(lzljBaseResponse != null){
                return lzljBaseResponse;
            }
            // 发起实际采购
            Triple<ErrorCode, SupplyPurchaseResponse, Long> tripleRes = supplyService.purchaseHandle(purchaseRequest, httpServletRequest);
            // 构建相应结果
            return buildPurchaseResponse(tripleRes, thirdMainId);
        } catch (BizException bizException) {
            log.warn("泸州老窖 采购下单 业务异常 request={}", JSONObject.toJSONString(request), bizException);
            return buildExceptionResponse(bizException);
        } catch (Exception e) {
            log.error("泸州老窖 采购下单 异常 request={}", JSONObject.toJSONString(request), e);
            return buildExceptionResponse(e);
        }
    }

    /**
     * 幂等
     * @param appId 应用id
     * @param thirdOrderNum 子订单号
     * @param thirdMainId 主订单号
     * @return
     */
    private LzljBaseResponse<?> successCheck(Long appId, String thirdOrderNum, String thirdMainId) throws BizException {
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(thirdOrderNum, appId);
        if (duiBaSupplyOrdersDto == null) {
            return null;
        }
        if (DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode().equals(duiBaSupplyOrdersDto.getOrderStatus())){
            throw new BizException("订单已成功发货");
        }
        if (DuiBaSupplyOrdersStatusEnum.FAIL.getCode().equals(duiBaSupplyOrdersDto.getOrderStatus())){
            throw new BizException("订单已取消");
        }
        if (!DuiBaSupplyOrdersStatusEnum.WAIT_SEND.getCode().equals(duiBaSupplyOrdersDto.getOrderStatus())){
           return null;
        }
        LzljBaseResponse<OrderSyncResult> purchaseResponse = new LzljBaseResponse<>();
        purchaseResponse.setSuccess(true);
        OrderSyncResult orderSyncResult = new OrderSyncResult();
        orderSyncResult.setId(thirdMainId);
        orderSyncResult.setIntegrationId(duiBaSupplyOrdersDto.getSupplyOrderNum().toString());
        purchaseResponse.setResult(orderSyncResult);
        return purchaseResponse;
    }

    /**
     * 幂等
     * @param appId 应用id
     * @param thirdOrderNum 主订单号
     * @return
     */
    private LzljBaseResponse<?> successCheck(Long appId, String thirdOrderNum) throws BizException {
        OrdersDto ordersDto = mallPurchaseService.findOrderByThirdOrderNum(appId, thirdOrderNum);
        if (ordersDto == null) {
            return null;
        }
        List<Integer> successStatus = Lists.newArrayList(StatusIntEnum.WAIT_RECEIVE.getCode(),StatusIntEnum.RECEIVE.getCode(),StatusIntEnum.SUCCESS.getCode());
        if (successStatus.contains(ordersDto.getStatusInt())){
            throw new BizException("订单已成功发货");
        }
        List<Integer> failStatus = Lists.newArrayList(StatusIntEnum.CANCEL.getCode(),StatusIntEnum.ERROR.getCode(),StatusIntEnum.FAIL.getCode());
        if (failStatus.contains(ordersDto.getStatusInt())){
            throw new BizException("订单已取消");
        }
        if (!StatusIntEnum.WAIT_SEND.getCode().equals(ordersDto.getStatusInt())){
            return null;
        }
        LzljBaseResponse<OrderSyncResult> purchaseResponse = new LzljBaseResponse<>();
        purchaseResponse.setSuccess(true);
        OrderSyncResult orderSyncResult = new OrderSyncResult();
        orderSyncResult.setId(thirdOrderNum);
        orderSyncResult.setIntegrationId(ordersDto.getOrderNum());
        purchaseResponse.setResult(orderSyncResult);
        return purchaseResponse;

    }

    /**
     * 签名校验
     * @param req 请求
     */
    private void checkSign(LzljBaseRequest<?> req) throws Exception {
        String jsonString = JSONObject.toJSONString(req);
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        log.info("[泸州老窖] 采购下单 签名校验 header={}", JSONObject.toJSONString(httpServletRequest.getHeaderNames()));
        String authCode = httpServletRequest.getHeader("authCode");
        log.info("[泸州老窖] 采购下单 签名校验 authCode={}", authCode);
        if (StringUtils.isBlank(authCode)) {
            throw new BizException("authCode 为空");
        }
        if (SpringEnvironmentUtils.isTestEnv() && "huakaiTest".equals(authCode)){
            return;
        }
        LzljSignUtils.validSign(jsonObject,authCode,lzljObjectConfig.getDuibaPrivateKey());
    }

    /**
     * 构建响应结果
     * @param tripleRes 兑吧采购相应结果
     */
    private LzljBaseResponse<?> buildPurchaseResponse(Triple<ErrorCode, SupplyPurchaseResponse, Long> tripleRes, String thirdMainId) throws BizException {
        if (Objects.nonNull(tripleRes.getLeft())) {
            throw new BizException(tripleRes.getLeft().getDesc());
        }
        SupplyPurchaseResponse response = tripleRes.getMiddle();
        if (Objects.isNull(response)) {
            throw new BizException("系统异常");
        }
        LzljBaseResponse<OrderSyncResult> purchaseResponse = new LzljBaseResponse<>();
        purchaseResponse.setSuccess(true);
        OrderSyncResult orderSyncResult = new OrderSyncResult();
        orderSyncResult.setId(thirdMainId);
        orderSyncResult.setIntegrationId(response.getOrderNum());
        purchaseResponse.setResult(orderSyncResult);
        return purchaseResponse;
    }

    /**
     * 构建响应结果
     * @param mallPurchaseResult 兑吧采购相应结果
     */
    private LzljBaseResponse<?> buildPurchaseResponse(MallPurchaseResult mallPurchaseResult, String thirdMainId) throws BizException {
        if (mallPurchaseResult == null ||  StringUtils.isBlank(mallPurchaseResult.getOrderNum())){
            throw new BizException("系统异常");
        }
        LzljBaseResponse<OrderSyncResult> purchaseResponse = new LzljBaseResponse<>();
        purchaseResponse.setSuccess(true);
        OrderSyncResult orderSyncResult = new OrderSyncResult();
        orderSyncResult.setId(thirdMainId);
        orderSyncResult.setIntegrationId(mallPurchaseResult.getOrderNum());
        purchaseResponse.setResult(orderSyncResult);
        return purchaseResponse;
    }


    /**
     * 获取并校验appItemId和skuId
     */
    private Triple<Long, Long, Long> checkAndGetAppItemInfo(String prodCode) throws BizException {
        // 商品编码不能为空
        if (StringUtils.isBlank(prodCode)) {
            throw new BizException("商品编码不能为空");
        }
        // 商品编码格式不正确
        String[] split = prodCode.split("_");
        if (split.length != 2) {
            throw new BizException("商品编码格式不正确");
        }
        // 校验商品存在
        Long appItemId = Long.parseLong(split[0]);
        Long skuId = Long.parseLong(split[1]);
        AppItemSkuDto appItemSkuDto = goodsCacheService.findAppItemSkuById(skuId);
        if (appItemSkuDto == null) {
            throw new BizException("商品不存在");
        }
        if (!Objects.equals(appItemId, appItemSkuDto.getAppItemId())) {
            throw new BizException("商品不存在");
        }
        AppItemDto appItemDto = goodsCacheService.findAppItemById(appItemId);
        if (appItemDto == null) {
            throw new BizException("商品不存在");
        }
        Long appId = appItemDto.getAppId();
        return Triple.of(appId, appItemId, skuId);

    }

    private void checkParam(LzljOrderSyncRequest request) throws BizException {
        List<LzljOrderSyncRequest.SaleOrderItem> saleOrderItemList = request.getSaleOrderItemList();

        if (CollectionUtils.isEmpty(saleOrderItemList)) {
            throw new BizException("商品信息为空");
        }
        if (saleOrderItemList.size() > 1) {
            throw new BizException("不支持下单多种商品");
        }
        if (saleOrderItemList.get(0).getQty() > 1) {
            throw new BizException("不支持下单多件");
        }
    }

    /**
     * 构建异常响应
     */
    public LzljBaseResponse<?> buildExceptionResponse(Exception exception) {
        LzljBaseResponse<String> response = new LzljBaseResponse<>();
        response.setSuccess(false);
        if (exception instanceof BizException) {
            response.setResult(exception.getMessage());
            return response;
        }
        response.setResult("系统异常");
        return response;
    }

    /**
     * 查询商品库存
     * @param > 查询请求
     * @return 商品库存信息
     */
    public LzljBaseResponse<?> queryGoodStock(LzljBaseRequest<LzljInventoryRequest> req) {
        LzljInventoryRequest request = req.getData();
        try {
            // 签名校验
            checkSign(req);
            // 请求参数校验
            Triple<Long, Long, Long> appItemInfo = checkAndGetAppItemInfo(request.getProdCode());
            Long skuId = appItemInfo.getRight();
            Map<Long, AppItemSkuDto> appItemSkuDtoMap = remoteAppItemSkuService.findAllSkuListWithStock(Lists.newArrayList(skuId)).stream()
                    .collect(Collectors.toMap(AppItemSkuDto::getId, Function.identity(), (o, n) -> n));
            AppItemSkuDto appItemSkuDto = appItemSkuDtoMap.get(skuId);
            if (appItemSkuDto == null) {
                throw new BizException("商品库存不存在");
            }
            return buildQueryGoodStockResponse(appItemSkuDto);
        } catch (BizException bizException) {
            log.warn("泸州老窖 采购下单 业务异常 request={}", JSONObject.toJSONString(request), bizException);
            return buildExceptionResponse(bizException);
        } catch (Exception e) {
            log.error("泸州老窖 采购下单 异常 request={}", JSONObject.toJSONString(request), e);
            return buildExceptionResponse(e);
        }
    }

    /**
     * 构建查询商品库存响应
     * @param appItemSkuDto appSku信息
     * @return 商品库存响应
     */
    private LzljBaseResponse<?> buildQueryGoodStockResponse(AppItemSkuDto appItemSkuDto) {
        LzljBaseResponse<InventoryResult> inventoryResponse = new LzljBaseResponse<>();
        inventoryResponse.setSuccess(true);
        InventoryResult inventoryResult = new InventoryResult();
        inventoryResult.setInventory(appItemSkuDto.getRemaining());
        inventoryResult.setIsSupported(lzljObjectConfig.getSupport());
        inventoryResponse.setResult(inventoryResult);
        return inventoryResponse;
    }
}
