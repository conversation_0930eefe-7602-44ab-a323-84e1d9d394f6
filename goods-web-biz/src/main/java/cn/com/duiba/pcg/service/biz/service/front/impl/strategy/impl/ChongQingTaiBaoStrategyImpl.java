package cn.com.duiba.pcg.service.biz.service.front.impl.strategy.impl;


import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.pcg.service.biz.config.chongqingtaibao.ChongqingTaiBaoConfig;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.supply.QuerySupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.response.supply.cqtb.ChongQingTaiBaoSupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.response.supply.cqtb.ChongqingTaiBaoSubOrderResponse;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategy;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategyRegistry;
import cn.com.duiba.supplier.center.api.dto.DuiBaSubSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSubSupplyOrderService;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import cn.com.duiba.supplier.center.api.request.order.DuiBaSupplyOrderPurchaseRequest;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

@Service
public class ChongQingTaiBaoStrategyImpl extends ExtraStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChongQingTaiBaoStrategyImpl.class);

    @Resource
    private ChongqingTaiBaoConfig chongQingTaiBaoConfig;
    @Resource
    private RemoteDuiBaSubSupplyOrderService remoteDuiBaSubSupplyOrderService;
    @Resource
    private RemoteDuiBaSupplyOrderService remoteDuiBaSupplyOrderService;
    @Autowired
    private ExecutorService executorService;

    @PostConstruct
    private void init() {
        ExtraStrategyRegistry.register(Lists.newArrayList(chongQingTaiBaoConfig.getAppIds()), this);
    }

    @Override
    public Object extraFild(QuerySupplyOrderResponse response, DuiBaSupplyOrdersDto order) {
        ChongQingTaiBaoSupplyOrderResponse chongQingTaiBaoSupplyOrderResponse = BeanUtils.copy(response, ChongQingTaiBaoSupplyOrderResponse.class);
        JSONObject jsonObject = JSON.parseObject(order.getExpressInfo());
        if(Objects.nonNull(jsonObject)) {
            chongQingTaiBaoSupplyOrderResponse.setAmount(jsonObject.getLong(ChongqingTaiBaoConfig.AMOUNT));
        }
        List<DuiBaSubSupplyOrdersDto> duiBaSubSupplyOrdersDtos = remoteDuiBaSubSupplyOrderService.selectBySupplyOrderNum(Long.valueOf(chongQingTaiBaoSupplyOrderResponse.getOrderNum()));
        if(!CollectionUtils.isEmpty(duiBaSubSupplyOrdersDtos)) {
            List<ChongqingTaiBaoSubOrderResponse> chongqingTaiBaoSubOrderResponses = BeanUtils.copyList(duiBaSubSupplyOrdersDtos, ChongqingTaiBaoSubOrderResponse.class);
            chongqingTaiBaoSubOrderResponses.forEach(this::converStatus);
            chongQingTaiBaoSupplyOrderResponse.setChongqingTaiBaoSubOrderResponseList(chongqingTaiBaoSubOrderResponses);
        }
        return chongQingTaiBaoSupplyOrderResponse;
    }

    @Override
    public void customPurchaseHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request) {
        //重庆太保发微信红包 一单最高只支持499元
        JSONObject jsonObject = JSON.parseObject(request.getExtraInfo());
        if(Objects.isNull(jsonObject) || jsonObject.isEmpty()) {
            jsonObject = new JSONObject();
        }
        jsonObject.put(ChongqingTaiBaoConfig.REAL_AMOUNT, request.getAmount());
        request.setExtraInfo(jsonObject.toJSONString());
        if(request.getAmount() > chongQingTaiBaoConfig.getSplitAmount()) {
            r.setAmount(chongQingTaiBaoConfig.getSplitAmount());
        }
        r.setExtraInfo(request.getExtraInfo());
    }

    @Override
    public void customPurchaseAfterHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request, Long supplyOrderNum) {
        if(request.getAmount() > chongQingTaiBaoConfig.getSplitAmount()) {
            try{
                //放入子订单关联表定制处理
                Long price = request.getAmount() - chongQingTaiBaoConfig.getSplitAmount();
                List<Long> priceList = priceSplit(price);
                List<DuiBaSubSupplyOrdersDto> duiBaSubSupplyOrdersDtos = Lists.newArrayList();
                priceList.forEach(prices -> {
                    DuiBaSubSupplyOrdersDto duiBaSubSupplyOrdersDto = new DuiBaSubSupplyOrdersDto();
                    duiBaSubSupplyOrdersDto.setAppId(r.getAppId());
                    duiBaSubSupplyOrdersDto.setAmount(prices);
                    duiBaSubSupplyOrdersDto.setSupplyOrderNum(supplyOrderNum);
                    duiBaSubSupplyOrdersDto.setAppId(r.getAppId());
                    duiBaSubSupplyOrdersDtos.add(duiBaSubSupplyOrdersDto);
                });
                //批量插入
                int i = remoteDuiBaSubSupplyOrderService.batchInsert(duiBaSubSupplyOrdersDtos);
                LOGGER.info("重庆太保批量插入,priceList={}, duiBaSubSupplyOrdersDtos={}, i={}", JSON.toJSONString(priceList), JSON.toJSONString(duiBaSubSupplyOrdersDtos), i);
                executorService.submit(()-> purchaseSubOrder(r, request, supplyOrderNum));
            } catch (Exception e) {
                LOGGER.warn("插入子订单表失败,request={}, supplyOrderNum={}", JSONObject.toJSONString(request), supplyOrderNum, e);
            }
        }
    }

    private void purchaseSubOrder(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request, Long supplyOrderNum) {
        //分表之后的逻辑，只取第一条数据。执行，因为目前只支持浦上的发红包，每天额度1000的上线，一单最大499，所以我们一天只发2单
        //新逻辑，直接全发
        List<DuiBaSubSupplyOrdersDto> duiBaSubSupplyOrdersDtoList = remoteDuiBaSubSupplyOrderService.selectBySupplyOrderNum(supplyOrderNum);
        List<DuiBaSupplyOrderPurchaseRequest> list = Lists.newArrayList();
        duiBaSubSupplyOrdersDtoList.forEach(duiBaSubSupplyOrdersDto ->  {
            if(!DuiBaSupplyOrdersStatusEnum.CREATE.getCode().equals(duiBaSubSupplyOrdersDto.getStatus())) {
                LOGGER.warn("重庆采购定制拆单逻辑,订单不是创建状态,supplyOrderNum={},duiBaSubSupplyOrdersDto={}", supplyOrderNum, JSON.toJSONString(duiBaSubSupplyOrdersDto));
                return;
            }
            DuiBaSupplyOrderPurchaseRequest duiBaSupplyOrderPurchaseRequest = new DuiBaSupplyOrderPurchaseRequest();
            duiBaSupplyOrderPurchaseRequest.setAccount(request.getAccount());
            duiBaSupplyOrderPurchaseRequest.setAmount(duiBaSubSupplyOrdersDto.getAmount());
            duiBaSupplyOrderPurchaseRequest.setAppId(r.getAppId());
            AppItemDto appItemDto = new AppItemDto();
            appItemDto.setId(r.getAppItem().getId());
            duiBaSupplyOrderPurchaseRequest.setAppItem(appItemDto);
            AppItemSkuDto appItemSkuDto = new AppItemSkuDto();
            appItemSkuDto.setId(r.getAppItemSku().getId());
            duiBaSupplyOrderPurchaseRequest.setAppItemSku(appItemSkuDto);
            duiBaSupplyOrderPurchaseRequest.setThirdOrderNum(r.getThirdOrderNum());
            duiBaSupplyOrderPurchaseRequest.setDeveloperId(r.getDeveloperId());
            duiBaSupplyOrderPurchaseRequest.setRechargeOrderNum(duiBaSubSupplyOrdersDto.getSupplySubOrderNum());
            list.add(duiBaSupplyOrderPurchaseRequest);
        });
        try {
            remoteDuiBaSupplyOrderService.purchaseSubOrderBycChongQingTaiBaoByList(list);
        } catch (Exception e) {
            LOGGER.warn("重庆太保子订单下单错误,list={},r={}", JSONObject.toJSONString(list), JSON.toJSONString(r), e);
        }

    }

    private List<Long> priceSplit(Long totalPrice) {
        List<Long> priceList = Lists.newArrayList();
        if(chongQingTaiBaoConfig.getSplitAmount() > totalPrice) {
            priceList.add(totalPrice);
            return priceList;
        }
        int num;
        if ((totalPrice % chongQingTaiBaoConfig.getSplitAmount()) == 0) {
            num = Math.toIntExact(totalPrice / chongQingTaiBaoConfig.getSplitAmount());
        } else {
            num = Math.toIntExact(totalPrice / chongQingTaiBaoConfig.getSplitAmount()) + 1;
        }
        for (int i = 0; i < num; i++) {
            if(totalPrice > chongQingTaiBaoConfig.getSplitAmount()) {
                totalPrice = totalPrice - chongQingTaiBaoConfig.getSplitAmount();
                priceList.add(chongQingTaiBaoConfig.getSplitAmount());
            } else {
                priceList.add(totalPrice);
            }
        }
        return priceList;
    }

    private void converStatus(ChongqingTaiBaoSubOrderResponse chongqingTaiBaoSubOrderResponse) {
        Integer status = chongqingTaiBaoSubOrderResponse.getStatus();
        chongqingTaiBaoSubOrderResponse.setStatus(QuerySupplyOrderResponse.statusMap.get(status));
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.FAIL.getCode())) {
            chongqingTaiBaoSubOrderResponse.setFailReason(chongqingTaiBaoSubOrderResponse.getFailReason());
        }
    }
}
