/**
 * Project Name:platform-coupon-biz File Name:NECaptchaServiceImpl.java Package
 * Name:cn.com.duiba.pcg.service.biz.service.impl Date:2017年4月17日上午10:21:31 Copyright (c) 2017, duiba.com.cn All Rights
 * Reserved.
 */

package cn.com.duiba.pcg.service.biz.service.impl;


import cn.com.duiba.boot.flowreplay.FlowReplayCustomizeSpan;
import cn.com.duiba.developer.center.api.domain.dto.AppNewExtraDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppNewExtraService;
import cn.com.duiba.pcg.service.biz.service.NECaptchaService;
import cn.com.duiba.pcg.service.biz.util.AppLogUtil;
import cn.com.duiba.pcg.service.biz.vo.NECaptchaResultVO;
import cn.com.duiba.pcg.tool.HttpClientUtil;
import cn.com.duiba.wolf.dubbo.DubboResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ClassName:NECaptchaServiceImpl <br/>
 * 易盾验证码服务实现类 <br/>
 * Date: 2017年4月17日 上午10:21:31 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.6
 * @see
 */
@Service
public class NECaptchaServiceImpl implements NECaptchaService {

    private static final Logger LOG     = LoggerFactory.getLogger(NECaptchaServiceImpl.class);
    private static final String VERSION = "v2";
    @Value("${pcgweb.ne.verify.secretId}")
    private String              neSecretId;
    @Value("${pcgweb.ne.verify.secretKey}")
    private String              neSecretKey;
    @Value("${pcgweb.ne.verify.api.2.0}")
    private String              verifyApi_2_0;// 易盾2.0二次验证接口
    @Value("${pcgweb.ne.verify.exchangeValidationSlide}")
    private String exchangeValidationSlide; //兑吧普兑验证码2.0-滑动
    @Value("${pcgweb.ne.verify.exchangeValidationClick}")
    private String exchangeValidationClick; //兑吧普兑验证码2.0-点选

    @Autowired
    private RemoteAppNewExtraService remoteAppNewExtraService;

    @Override
    @FlowReplayCustomizeSpan
    public NECaptchaResultVO isCaptchaPass(String validate, Long appId) {
        if (StringUtils.isEmpty(validate)) {
            LOG.warn("verifyRet warn: validate is null");
            return new NECaptchaResultVO(false, null, "validate is null");
        }
        Map<String, String> params = new HashMap<>();
        params.put("captchaId", getExchangeCaptcha(appId));
        params.put("validate", validate);// 提交二次校验的验证数据，即NECaptchaValidate值
        params.put("user", "");
        // 公共参数
        params.put("secretId", neSecretId);
        params.put("version", VERSION);
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("nonce", String.valueOf(ThreadLocalRandom.current().nextInt()));
        // 计算请求参数签名信息
        String signature = sign(neSecretKey, params);
        params.put("signature", signature);
        String resp = HttpClientUtil.sendPost(verifyApi_2_0, params);
        NECaptchaResultVO result = verifyRet(resp);
        if(!result.isResult()){
            LOG.info("verifyRet warn: error="+result.getError()+";msg="+result.getMsg()+";params="+params.toString());
        }
        return result;
    }

    /**
     * 生成签名信息
     *
     * @param secretKey 验证码私钥
     * @param params 接口请求参数名和参数值map，不包括signature参数名
     * @return 签名后的字符串
     */
    public static String sign(String secretKey, Map<String, String> params) {
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key).append(params.get(key));
        }
        sb.append(secretKey);
        try {
            return DigestUtils.md5Hex(sb.toString().getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            AppLogUtil.warn(LOG, "易盾签名失败，param={}", params, e);
        }
        return null;
    }

    /**
     * 封装包含验证码id的返回参数
     * @param appId
     * @return
     */
    public JSONObject getCaptchaJson(Long appId) {
        JSONObject json = new JSONObject();
        json.put("success", true);
        json.put("neCaptcha", true);
        json.put("neCaptchaId", getExchangeCaptcha(appId));
        return json;
    }

    /**
     * 获取普兑专用易盾2.0验证码id
     * 后台开关控制验证方式，默认关闭：滑动类型；开启：点击类型
     * @param appId
     * @return
     */
    private String getExchangeCaptcha(Long appId) {
        DubboResult<AppNewExtraDto> appNewExtraDto = remoteAppNewExtraService.findByAppId(appId);
        if (null != appNewExtraDto && appNewExtraDto.isSuccess() && null != appNewExtraDto.getResult()) {
            String exchangeValidation = appNewExtraDto.getResult().getExchangeValidation();
            if (Objects.equal(exchangeValidation, AppNewExtraDto.EXCHANGE_VALIDATION_CLICK)) {
                return  exchangeValidationClick;
            }
        }
        return exchangeValidationSlide;
    }

    /**
     * 验证返回结果
     *
     * @param resp
     * @return
     */
    private NECaptchaResultVO verifyRet(String resp) {
        if (StringUtils.isEmpty(resp)) {
            LOG.warn("verifyRet warn: 返回结果为空");
            return new NECaptchaResultVO(true, null, "返回结果为空");
        }
        try {
            NECaptchaResultVO result = JSON.parseObject(resp, NECaptchaResultVO.class);
            return result;
        } catch (Exception e) {
            LOG.warn("verifyRet warn: resp=" + resp, e);
            return new NECaptchaResultVO(true, null, "返回结果解析异常，response=" + resp);
        }
    }
}
