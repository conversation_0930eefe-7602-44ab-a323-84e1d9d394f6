package cn.com.duiba.pcg.service.biz.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "icbc.credits")
public class IcbcCreditsConfig {

    private String notifyUrl = "https://activity.m.duiba.com.cn/taw/icbc/charge/notify";

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}
