// package cn.com.duiba.pcg.service.biz.util;
//
// import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
// import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
// import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
// import cn.com.duiba.pcg.exception.GoodsWebException;
// import com.google.gson.Gson;
//
// import java.util.Objects;
//
// /**
//  * Created by zhangshun on 2018/12/27,09:18:34.
//  */
// public class RequestLocalUtils {
//     private static Gson gson = new Gson();
//
//     private RequestLocalUtils() {
//         throw new IllegalStateException("Utility class");
//     }
//
//     public static Long getAppId() {
//        return RequestLocal.getAppId();
//     }
//
//     public static Long getCid() {
//        return RequestLocal.getCid();
//     }
//
//     public static AppSimpleDto getConsumerAppDO() {
//        AppSimpleDto appSimpleDto = RequestLocal.getConsumerAppDO();
//         if (Objects.isNull(appSimpleDto)) {
//             throw new GoodsWebException("获取APP错误");
//         }
//         return appSimpleDto;
//     }
//
//     public static ConsumerDto getConsumerDO() {
//        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
//         if (Objects.isNull(consumerDto)) {
//             throw new GoodsWebException("获取买家信息错误");
//         }
//         return consumerDto;
//     }
//
// }
