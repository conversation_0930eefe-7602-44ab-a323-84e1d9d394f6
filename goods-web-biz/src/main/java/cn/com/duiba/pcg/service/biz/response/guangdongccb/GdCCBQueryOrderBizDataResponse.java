package cn.com.duiba.pcg.service.biz.response.guangdongccb;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/8/17 10:43
 */
public class GdCCBQueryOrderBizDataResponse {
    /**
     * 提货地址（url提货模式必输）
     */
    private String cardUrl;

    /**
     * 订单号（充值中心订单号，取请求入参中的orderNo）
     */
    private String orderNo;

    /**
     * 提货方式
     * A01:直冲
     * A02:卡密
     * A03:url提货
     */
    private String deliverType;

    /**
     * 有效开始时间
     */
    private String validBeginTime;

    /**
     * 有效结束时间
     */
    private String validEndTime;

    public String getValidBeginTime() {
        return validBeginTime;
    }

    public void setValidBeginTime(String validBeginTime) {
        this.validBeginTime = validBeginTime;
    }

    public String getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(String validEndTime) {
        this.validEndTime = validEndTime;
    }

    public String getDeliverType() {
        return deliverType;
    }

    public void setDeliverType(String deliverType) {
        this.deliverType = deliverType;
    }

    public String getCardUrl() {
        return cardUrl;
    }

    public void setCardUrl(String cardUrl) {
        this.cardUrl = cardUrl;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
