package cn.com.duiba.pcg.service.biz.handler.ordersource.impl;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.mall.center.api.domain.MallCenterException;
import cn.com.duiba.mall.center.api.domain.dto.groupbuy.GroupBuyOrderDto;
import cn.com.duiba.mall.center.api.domain.paramquary.groupbuy.GroupBuyCreateParam;
import cn.com.duiba.mall.center.api.domain.paramquary.groupbuy.GroupBuyOrderUpdateParam;
import cn.com.duiba.mall.center.api.remoteservice.groupbuy.RemoteGroupBuyDetailService;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.tool.OrderUtils;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.enums.OrderSourceTypeEnum;
import cn.com.duiba.pcg.service.biz.handler.ordersource.AbstraceOrderSourceHandler;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.BaseParam;
import cn.com.duiba.pcg.service.biz.util.GroupBuyTool;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>发起拼团策略</p>
 **/
@Component
public class CreateGroupHandler extends AbstraceOrderSourceHandler {

    @Autowired
    private RemoteGroupBuyDetailService remoteGroupBuyDetailService;
    @Autowired
    private GroupBuyTool groupBuyTool;

    @Override
    public Integer getSourceType() {
        return OrderSourceTypeEnum.CREATE_GROUP.getCode();
    }

    @Override
    public void validate(Long sourceId, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) throws BizException {

    }

    @Override
    public void beforeExchange(Long sourceId, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer, OrdersDto order) {
        GroupBuyCreateParam param = groupBuyTool.getGroupBuyParam(consumer, order);
        try {
            GroupBuyOrderDto groupOrder = remoteGroupBuyDetailService.createGroup(param);

            groupBuyTool.fillGroupBuyMainOrder(order, groupOrder);
        } catch (BizException e) {
            throw new GoodsWebException(e.getMessage());
        }

    }




    @Override
    public void afterExchange(Long sourceId, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer, OrdersDto order, BaseParam baseParam) {
        //主订单id更新到拼团订单里
        remoteGroupBuyDetailService.updateOrderId(order.getRelationId(),order.getId());
    }
}
