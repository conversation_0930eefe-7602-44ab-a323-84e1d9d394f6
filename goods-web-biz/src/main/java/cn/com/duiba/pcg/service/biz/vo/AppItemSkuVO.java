package cn.com.duiba.pcg.service.biz.vo;

import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemCreditsSkuDto;

import java.util.List;

/**
 * Created by sunyan on 2019/12/30.
 */
public class AppItemSkuVO {
    private String title;
    private String url;
    private String type;
    private Boolean owner;
    private String smallImage;
    private String multiImage;
    private Long expressPrice;
    private String expressTemplateName;
    private List<AppItemCreditsSkuDto.AppItemCreditsSkuDetail> skuList;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getOwner() {
        return owner;
    }

    public void setOwner(Boolean owner) {
        this.owner = owner;
    }

    public String getSmallImage() {
        return smallImage;
    }

    public void setSmallImage(String smallImage) {
        this.smallImage = smallImage;
    }

    public String getMultiImage() {
        return multiImage;
    }

    public void setMultiImage(String multiImage) {
        this.multiImage = multiImage;
    }

    public Long getExpressPrice() {
        return expressPrice;
    }

    public void setExpressPrice(Long expressPrice) {
        this.expressPrice = expressPrice;
    }

    public String getExpressTemplateName() {
        return expressTemplateName;
    }

    public void setExpressTemplateName(String expressTemplateName) {
        this.expressTemplateName = expressTemplateName;
    }

    public List<AppItemCreditsSkuDto.AppItemCreditsSkuDetail> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<AppItemCreditsSkuDto.AppItemCreditsSkuDetail> skuList) {
        this.skuList = skuList;
    }
}
