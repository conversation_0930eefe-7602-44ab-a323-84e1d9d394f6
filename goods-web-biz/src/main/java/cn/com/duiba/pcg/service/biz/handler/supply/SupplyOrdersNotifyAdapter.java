package cn.com.duiba.pcg.service.biz.handler.supply;

import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;

/**
 * 订单回调适配器
 * <AUTHOR>
 * @date 2023/7/31 10:25
 */
public interface SupplyOrdersNotifyAdapter {

    /**
     * 订单结果异步通知
     * @param dto 订单信息
     */
    void supplyOrdersNotify(DuiBaSupplyOrdersDto dto);

    /**
     * 异步通知重试
     * @param appId 分销商id
     * @param thirdOrderNum 分销商订单号
     */
    void retryNotify(Long appId, String thirdOrderNum);
}
