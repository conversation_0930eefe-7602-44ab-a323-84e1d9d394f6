package cn.com.duiba.pcg.service.biz.vo.equity;

import org.apache.commons.lang.builder.ToStringBuilder;

import java.io.Serializable;

/**
 * 创建权益订单成功返回对象
 */
public class EquityOrderCreatedVO implements Serializable {

    private String orderId;

    private String forwardUrl;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getForwardUrl() {
        return forwardUrl;
    }

    public void setForwardUrl(String forwardUrl) {
        this.forwardUrl = forwardUrl;
    }
}
