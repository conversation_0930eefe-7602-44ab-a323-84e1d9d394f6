package cn.com.duiba.pcg.service.biz.handler.exchangecheck.handler;

import cn.com.duiba.activity.custom.center.api.dto.fjzh.UpgradeModuleDto;
import cn.com.duiba.activity.custom.center.api.enums.fjzh.FujianWhiteKeyEnums;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteOrdersTextChangeService;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.BocConfig;
import cn.com.duiba.pcg.service.biz.handler.exchangecheck.ExchangeCheckHandler;
import cn.com.duiba.pcg.service.biz.param.CustomExchangeParam;
import cn.com.duiba.pcg.service.biz.processor.extrange.CouponExtrangeProcessor;
import cn.com.duiba.thirdparty.api.boc.RemoteBocService;
import cn.com.duiba.thirdparty.dto.boc.GetWxCardDto;
import cn.com.duiba.wechat.server.api.dto.CreditsDto;
import cn.com.duiba.wechat.server.api.remoteservice.RemoteWeChatCreditsService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: fss
 * @Date: 2021/12/17 15
 * @Description:
 */
@Component
public class FujianBocExchangeCheckHandler implements ExchangeCheckHandler {

    protected Logger logger = LoggerFactory.getLogger(FujianBocExchangeCheckHandler.class);

    @Autowired
    private RemoteWeChatCreditsService remoteCreditsService;
    @Autowired
    private BocConfig bocConfig;
    @Autowired
    private RemoteConsumerService remoteConsumerService;
    @Autowired
    private RemoteOrdersTextChangeService remoteOrdersTextChangeService;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    private static final String UNION_NO = "unionNo";
    private static final String ACTIVITY_ID = "duiba_0001";
    private static final String ACTIVITY_NAME = "升级奖品兑换";
    private static final String CARD_NO = "cardNo";

    @Override
    public boolean support(Long appId) {
        return bocConfig.getFujianAppIdSet().contains(appId);
    }

    private Long getCurrentMonthAddCredits(ConsumerDto consumer, AppSimpleDto app) {
        CreditsDto creditsDto = new CreditsDto();
        creditsDto.setAppKey(app.getAppKey());
        creditsDto.setOpenId(consumer.getPartnerUserId());
        return remoteCreditsService.getCurrentMonthAddCredits(creditsDto);
    }

    @Override
    public Pair<Boolean, String> customExchangeCheck(CustomExchangeParam customExchangeParam) {
        // check升级组件商品
        String unitKey = WhiteAccessUtil.selectWhiteListJsonConfig(FujianWhiteKeyEnums.UPGRADE_KEY.getCode());
        UpgradeModuleDto upgradeModuleDto = JSON.parseObject(unitKey, UpgradeModuleDto.class);

        ItemKeyDto key = customExchangeParam.getItemKeyDto();
        ConsumerDto consumer = customExchangeParam.getConsumer();
        AppSimpleDto app = customExchangeParam.getApp();
        Long appItemId = key.getAppItem().getId();
        if (!upgradeModuleDto.getFirstAppItemId().equals(appItemId) && !upgradeModuleDto.getSecondAppItemId().equals(appItemId)) {
            return Pair.of(false, StringUtils.EMPTY);
        }

        Long credits = upgradeModuleDto.getFirstAppItemId().equals(appItemId) ? upgradeModuleDto.getFirstCredits() : upgradeModuleDto.getSecondCredits();
        Long currentMonthAddCredits = getCurrentMonthAddCredits(consumer, app);
        if (currentMonthAddCredits >= credits) {
            return Pair.of(false, StringUtils.EMPTY);
        }
        return Pair.of(true, "还差" + (credits - currentMonthAddCredits) + "积分可领");
    }

    @Override
    public boolean customExchangeInsert(CustomExchangeParam customExchangeParam) {
        return true;
    }

    private void errorResponer() {
        throw new GoodsWebException("兑换失败，请联系管理人员");
    }
}
