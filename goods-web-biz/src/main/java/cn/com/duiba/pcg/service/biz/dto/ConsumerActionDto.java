package cn.com.duiba.pcg.service.biz.dto;

import java.util.Date;

/**
 * ClassName: ConsumerActionDto <br/>
 * date: 2017年4月12日 下午5:28:58 <br/>
 *
 * @version
 * @since JDK 1.6
 */
public class ConsumerActionDto {

    /***/
    public static final String TypeVisit = "visit";
    /***/
    public static final String TypeSwipe = "swipe";

    private String             url;
    private String             type;
    private Date               time;
    private String             referer;

    /**
     * url.
     *
     * @return the url
     */
    public String getUrl() {
        return url;
    }

    /**
     * url.
     *
     * @param url the url to set
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * type.
     *
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * type.
     *
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * time.
     *
     * @return the time
     */
    public Date getTime() {
        return time;
    }

    /**
     * time.
     *
     * @param time the time to set
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * referer.
     *
     * @return the referer
     */
    public String getReferer() {
        return referer;
    }

    /**
     * referer.
     *
     * @param referer the referer to set
     */
    public void setReferer(String referer) {
        this.referer = referer;
    }

}
