package cn.com.duiba.pcg.service.biz.log;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.wolf.utils.DateUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 用户访问日志，主要用于统计PV UV
 */
public class StatConsumerAccessLog {
	private static Logger log = LoggerFactory.getLogger(StatConsumerAccessLog.class);

    /**
     * @param consumer
     * @param ip
     * @param os
     * @param url
     * @param ua
     */
    public static void log(ConsumerDto consumer,String ip, String os,String url, String ua){
		try {
            log.info(format(consumer,ip,os,url,ua));
		} catch (Exception e) {
			log.error("StatConsumerAccessLog 失败",e);
		}
	}
    
    private static String format(ConsumerDto consumer,String ip,String os,String url,String userAgent){
        JSONObject json = new JSONObject();
        json.put("url", url);
        json.put("userAgent", userAgent);
        json.put("os", os);
        json.put("ip", ip);
        json.put("appId", consumer.getAppId());
        json.put("consumerId", consumer.getId());
        json.put("logTime", DateUtils.getSecondStr(new Date()));
        return json.toJSONString();
    }
}
