package cn.com.duiba.pcg.service.biz.vo.ccb;

import java.util.List;

/**
 * 采购下单响应
 * <AUTHOR>
 */
public class CcbDrawCouponResponseVo {

    private String code;
    private String msg;
    private String orderId;
    private String asynFlag;
    private String respFlag;
    private String useType;
    private String completeTm;
    private List<CouponInfo> coupons;

    public List<CouponInfo> getCoupons() {
        return coupons;
    }

    public void setCoupons(List<CouponInfo> coupons) {
        this.coupons = coupons;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getAsynFlag() {
        return asynFlag;
    }

    public void setAsynFlag(String asynFlag) {
        this.asynFlag = asynFlag;
    }

    public String getRespFlag() {
        return respFlag;
    }

    public void setRespFlag(String respFlag) {
        this.respFlag = respFlag;
    }

    public String getUseType() {
        return useType;
    }

    public void setUseType(String useType) {
        this.useType = useType;
    }

    public String getCompleteTm() {
        return completeTm;
    }

    public void setCompleteTm(String completeTm) {
        this.completeTm = completeTm;
    }

    public static class CouponInfo {

        private String couponCode;
        private String password;
        private String verfCode;
        private String link;

        public CouponInfo(String couponCode) {
            this.couponCode = couponCode;
        }

        public CouponInfo(String couponCode,String link) {
            this.couponCode = couponCode;
            this.link = link;
        }

        public String getCouponCode() {
            return couponCode;
        }

        public void setCouponCode(String couponCode) {
            this.couponCode = couponCode;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getVerfCode() {
            return verfCode;
        }

        public void setVerfCode(String verfCode) {
            this.verfCode = verfCode;
        }

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }
    }
}

