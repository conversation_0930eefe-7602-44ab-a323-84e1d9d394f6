package cn.com.duiba.pcg.service.biz.vo.oppo;

import cn.com.duiba.pcg.service.biz.vo.cmb.CmbItemConfigVO;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xutao on 2020/5/7.
 */
public class OppoVO {
    private String appKey;

    private String aesKey;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }

    public static void main(String[] args) throws Exception{
        OppoVO oppoVO = new OppoVO();
        oppoVO.setAesKey("JuGi3FCECD1dA2BPL1lCWC==");
        oppoVO.setAppKey("oTtXFe2fVHCqt4yYreD6DfdckV8");
        List<OppoVO> list = new ArrayList<>();
        list.add(oppoVO);
        System.out.println(JSONObject.toJSONString(list));
    }
}
