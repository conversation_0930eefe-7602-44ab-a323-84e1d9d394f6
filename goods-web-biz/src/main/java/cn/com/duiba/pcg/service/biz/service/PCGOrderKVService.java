package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.dcommons.enums.GoodsTypeEnum;

/**
 * Created by zzy on 2017/2/14.
 */
public interface PCGOrderKVService {
    /**
     * 设置兑换记录
     *
     * @param consumerId
     * @param goodsTypeEnum
     * @param orderId
     * @param pcgId
     * @return boolean
     */
    boolean set(Long consumerId, GoodsTypeEnum goodsTypeEnum, Long pcgId, Long orderId);

    /**
     * 查询兑换记录：先查询mysql，查询不到会查询redis，查询redis代码在60天以后删除
     *
     * @param consumerId
     * @param goodsTypeEnum
     * @param pcgId
     * @return String
     */
    String get(Long consumerId, GoodsTypeEnum goodsTypeEnum, Long pcgId);
}
