package cn.com.duiba.pcg.service.biz.config;

import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@Configuration
@ConfigurationProperties("weizhong")
public class WeiZhongConfig {

    /**
     * 应用id
     */
    private Set<Long> appIds = Sets.newHashSet(96475L);

    /**
     * 推送地址
     */
    private String pushUrl = "http://121.37.42.90:5002/bean-mgr/duiba/order";

    /**
     * 订单额外保存字段
     */
    private String filed = "bizSeqNo";

    /**
     * 地址信息
     */
    private String addrName = "测试";
    private String addrCode = "120101002";
    private String addrDetail = "鼓楼";
    private String addrPhone = "18120522222";
    private String addrProvince = "天津市";
    private String addrCity = "辖区";
    private String addrArea = "和平区";
    private String addrStreet = "小白楼街道";

}
