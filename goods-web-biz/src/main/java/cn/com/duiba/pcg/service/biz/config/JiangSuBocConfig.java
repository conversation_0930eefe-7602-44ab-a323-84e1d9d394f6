package cn.com.duiba.pcg.service.biz.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 江苏中行
 * <AUTHOR>
 * @date 2023/09/05
 */
@Configuration
@ConfigurationProperties("jsboc")
public class JiangSuBocConfig {

    private String key = "ms2zbt5k";

    private Long appId = 19523L;

    private String doTest = "1";

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getDoTest() {
        return doTest;
    }

    public void setDoTest(String doTest) {
        this.doTest = doTest;
    }
}
