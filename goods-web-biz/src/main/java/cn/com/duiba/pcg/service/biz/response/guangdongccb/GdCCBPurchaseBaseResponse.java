package cn.com.duiba.pcg.service.biz.response.guangdongccb;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/8/16 14:37
 */
public class GdCCBPurchaseBaseResponse {
    /**
     * 加签后字符串（SM2签名）
     */
    private String sign;

    /**
     * 状态码
     * 01: 受理成功
     * 10: 发货成功(针对直接响应卡密场景，此处应返回发货成功，后续无需再调用发货结果反馈接口)
     * 09: 受理失败（充值中心将做退款处理）
     */
    private String retStatus;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 业务数据
     */
    private String bizData;


    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRetStatus() {
        return retStatus;
    }

    public void setRetStatus(String retStatus) {
        this.retStatus = retStatus;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getBizData() {
        return bizData;
    }

    public void setBizData(String bizData) {
        this.bizData = bizData;
    }
}
