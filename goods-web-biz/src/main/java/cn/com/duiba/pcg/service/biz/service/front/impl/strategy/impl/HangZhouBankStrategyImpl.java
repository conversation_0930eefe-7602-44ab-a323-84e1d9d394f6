package cn.com.duiba.pcg.service.biz.service.front.impl.strategy.impl;


import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.pcg.service.biz.config.HzBankConfig;
import cn.com.duiba.pcg.service.biz.config.chongqingtaibao.ChongqingTaiBaoConfig;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.supply.QuerySupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.response.supply.cqtb.ChongQingTaiBaoSupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.response.supply.cqtb.ChongqingTaiBaoSubOrderResponse;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategy;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategyRegistry;
import cn.com.duiba.supplier.center.api.dto.DuiBaSubSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSubSupplyOrderService;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import cn.com.duiba.supplier.center.api.request.order.DuiBaSupplyOrderPurchaseRequest;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

@Service
public class HangZhouBankStrategyImpl extends ExtraStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(HangZhouBankStrategyImpl.class);

    @Autowired
    private HzBankConfig hzBankConfig;
    @Resource
    private RemoteDuiBaSubSupplyOrderService remoteDuiBaSubSupplyOrderService;

    @PostConstruct
    private void init() {
        ExtraStrategyRegistry.register(Lists.newArrayList(hzBankConfig.getAppId()), this);
    }

    @Override
    public Object extraFild(QuerySupplyOrderResponse response, DuiBaSupplyOrdersDto order) {
        JSONObject jsonObject = JSON.parseObject(order.getExpressInfo());
        if(Objects.nonNull(jsonObject)) {
            response.setSource(jsonObject.getString(HzBankConfig.SOURCE));
        }
        return response;
    }

    @Override
    public void customPurchaseHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(HzBankConfig.SOURCE, request.getSource());
        r.setExtraInfo(jsonObject.toJSONString());
    }

    @Override
    public void customPurchaseAfterHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request, Long supplyOrderNum) {
    }

}
