package cn.com.duiba.pcg.service.biz.service.weibo;

import cn.com.duiba.pcg.service.biz.config.WeiboConfig;
import cn.com.duiba.pcg.tool.HttpClientUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
@Slf4j
@Service
public class WeiboLoginService {

    @Resource
    private WeiboConfig weiboConfig;


    /**
     * code换token
     * @param code 授权码
     * @return accessToken
     */
    public String codeToUid(String code) {
        try {
            Map<String, String> params = new HashMap<>();
            String res = HttpClientUtil.sendPost(weiboConfig.getCode2TokenUrl(), params);
            log.info("微博登录 codeToToken res={}",res);
            return JSONObject.parseObject(res).getString("uid");
        }catch (Exception e){
            log.error("微博登录 codeToToken 异常",e);
            return null;
        }
    }
}
