package cn.com.duiba.pcg.service.biz.enums;

/**
 * @Author: fss
 * @Date: 2021/12/27 11
 * @Description:
 */
public enum PrivacyEnums {
    NO(0, "未勾选"),
    YES(1, "已勾选");
    private int code;
    private String desc;

    PrivacyEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @return code
     */
    public int getCode() {
        return code;
    }

    /**
     * @return desc
     */
    public String getDesc() {
        return desc;
    }


    public static PrivacyEnums getByCode(int code) {
        for (PrivacyEnums unitEnum : PrivacyEnums.values()) {
            if (unitEnum.getCode() == code) {
                return unitEnum;
            }
        }
        return NO;
    }
}
