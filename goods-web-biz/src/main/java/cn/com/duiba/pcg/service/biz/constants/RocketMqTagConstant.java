package cn.com.duiba.pcg.service.biz.constants;

/**
 * mq tag常量定义
 */
public class RocketMqTagConstant {
    private RocketMqTagConstant() {
    }

    /**
     * 重庆太保采购订单重试tag
     */
    public static final String CQTB_SUPPLY_ORDERS_RETRY_TAG = "CQTB_SUPPLY_ORDERS_RETRY_TAG";

    /**
     * 订单异步通知
     */
    public static final String SUPPLY_ORDERS_NOTIFY_TAG = "SUPPLY_ORDERS_NOTIFY_TAG";
    /**
     * 荣数券状态变更通知
     */
    public static final String RS_COUPON_CHANGE_NOTIFY_TAG = "RS_COUPON_CHANGE_NOTIFY_TAG";
}
