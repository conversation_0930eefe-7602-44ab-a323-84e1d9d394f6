package cn.com.duiba.pcg.service.biz.service.custom.jhj;

import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.jhj.JhjConfig;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.BaseParam;
import cn.com.duiba.pcg.service.biz.processor.extrange.ExtrangeBaseProcessor;
import cn.com.duiba.thirdpartyvnew.api.jhj.RemoteJhjService;
import cn.com.duiba.thirdpartyvnew.dto.jhj.request.JhjBaseRequest;
import cn.com.duiba.thirdpartyvnew.dto.jhj.request.JhjCreateOrderRequest;
import cn.com.duiba.thirdpartyvnew.dto.jhj.request.JhjExpressRequest;
import cn.com.duiba.thirdpartyvnew.dto.jhj.response.JhjAddressResponseData;
import cn.com.duiba.thirdpartyvnew.dto.jhj.response.JhjCreateOrderResponseData;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/5/17 16:01
 */
@Slf4j
@Component
public class JhjService {
    @Autowired
    private RemoteConsumerExtraService consumerExtraService;
    @Resource
    private RemoteAppItemSkuService remoteAppItemSkuService;

    @Resource
    private RemoteJhjService remoteJhjService;

    @Autowired
    private JhjConfig jhjConfig;


    /**
     * 金徽酒积分商城定制-商品映射
     */
    private static final String JHJ_GOODS_WHITE = "JHJJFSCDZ-SPYS_zhangdaqing";

    public Long getDefaultAddress() {
        return jhjConfig.getDefaultAddressId();
    }

    /**
     * 构建金徽酒默认地址
     *
     * @return {@link JhjExpressRequest}
     */
    public JhjExpressRequest buildJhjDefaultAddress() {
        return JSON.parseObject(jhjConfig.getJhjDefaultAddress(), JhjExpressRequest.class);
    }

    public boolean isJhjApp(Long appId) {
        return jhjConfig.getAppIds().contains(appId);
    }

    public JhjExpressRequest getJhjAddressRequestById(JhjBaseRequest jhjBaseRequest, Long addressId) {
        List<JhjAddressResponseData> jhjAddressResponseDataList = remoteJhjService.listAddress(jhjBaseRequest);
        Optional<JhjAddressResponseData> first = jhjAddressResponseDataList.stream().filter(u -> u.getIndex().equals(addressId)).findFirst();
        if (first.isPresent()) {
            JhjAddressResponseData jhjAddressResponseData = first.get();
            return BeanUtil.copyProperties(jhjAddressResponseData, JhjExpressRequest.class);
        }
        return null;
    }


    public Long thirdCreateOrderNew(JhjBaseRequest jhjBaseRequest, JhjExpressRequest jhjExpressRequest, Long appItemId, String userId) {
        if (jhjBaseRequest == null) {
            return null;
        }
        if (jhjExpressRequest == null) {
            throw new GoodsWebException("用户地址信息不存在");
        }
        JhjCreateOrderRequest jhjCreateOrderRequest = new JhjCreateOrderRequest();
        jhjCreateOrderRequest.setRegister(jhjExpressRequest);
        //商城id
        jhjCreateOrderRequest.setPointsMallId(jhjConfig.getPointsMallId());
        Long jhjThirdGoodsId = getJhjThirdGoodsId(appItemId);
        if (jhjThirdGoodsId == null) {
            // 取商品编码
            List<AppItemSkuDto> skuByItemIdList = remoteAppItemSkuService.findSkuByItemIdList(Collections.singletonList(appItemId));
            log.info("金徽酒查询AppItemSkuDto:{},userId:{}", JSON.toJSONString(skuByItemIdList), userId);
            if (CollectionUtils.isEmpty(skuByItemIdList)) {
                return null;
            }
            String merchantCoding = skuByItemIdList.get(0).getMerchantCoding();
            if (StringUtils.isBlank(merchantCoding)) {
                return null;
            } else {
                jhjThirdGoodsId = Long.valueOf(merchantCoding);
            }
        }
        jhjCreateOrderRequest.setPresentId(jhjThirdGoodsId);
        jhjCreateOrderRequest.setUserId(userId);
        jhjCreateOrderRequest.setToken(jhjBaseRequest.getToken());
        jhjCreateOrderRequest.setSerialId(jhjBaseRequest.getSerialId());
        log.info("金徽酒创建第三方订单 request:{}", JSON.toJSONString(jhjCreateOrderRequest));
        JhjCreateOrderResponseData order = remoteJhjService.createOrder(jhjCreateOrderRequest);
        log.info("金徽酒创建第三方订单 request:{}, response:{}", JSON.toJSONString(jhjCreateOrderRequest), order);
        if (order == null || order.getId()==null || order.getId() <= 0) {
            throw new GoodsWebException("创建客户订单失败");
        }
        return order.getId();
    }

    public JhjBaseRequest buildJhjBaseRequest(String userId) {
        JhjBaseRequest jhjBaseRequest = new JhjBaseRequest();
        DubboResult<ConsumerExtraDto> consumerExtraDtoDubboResult = consumerExtraService.findByConsumerId(Long.valueOf(userId));
        if (!consumerExtraDtoDubboResult.isSuccess()) {
            throw new GoodsWebException("用户扩展信息异常");
        }
        ConsumerExtraDto result = consumerExtraDtoDubboResult.getResult();
        String json = result.getJson();

        if (StringUtils.isBlank(json)) {
            throw new GoodsWebException("用户扩展信息异常");
        }
        JSONObject jsonObj = JSON.parseObject(json);
        jhjBaseRequest.setUserId(userId);
        String token = jsonObj.getString("token");
        jhjBaseRequest.setToken(token);
        String serialId = jsonObj.getString("serialId");
        jhjBaseRequest.setSerialId(serialId);
        return jhjBaseRequest;
    }

    /**
     * 金徽酒下单
     *
     * @param addressId
     * @return {@link Long}
     */
    public Long thirdCreateOrder(Long addressId, Long appItemId, String userId) {
        JhjBaseRequest jhjBaseRequest = new JhjBaseRequest();
        DubboResult<ConsumerExtraDto> consumerExtraDtoDubboResult = consumerExtraService.findByConsumerId(Long.valueOf(userId));
        if (!consumerExtraDtoDubboResult.isSuccess()) {
            return null;
        }
        ConsumerExtraDto result = consumerExtraDtoDubboResult.getResult();
        String json = result.getJson();
        log.info("金徽酒创建第三方订单start:userId:{},appItemId:{},addressId:{},json:{}", userId, appItemId, addressId, json);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        JSONObject jsonObj = JSON.parseObject(json);
        jhjBaseRequest.setUserId(userId);
        String token = jsonObj.getString("token");
        jhjBaseRequest.setToken(token);
        String serialId = jsonObj.getString("serialId");
        jhjBaseRequest.setSerialId(serialId);
        log.info("金徽酒创建第三方订单start:userId:{},appItemId:{},addressId:{}", userId, appItemId, addressId);
        List<JhjAddressResponseData> jhjAddressResponseDataList = remoteJhjService.listAddress(jhjBaseRequest);
        log.info("金徽酒创建第三方订单start:userId:{},appItemId:{},addressId:{},jhjAddressResponseDataList:{}", userId, appItemId, addressId, JSON.toJSONString(jhjAddressResponseDataList));
        Optional<JhjAddressResponseData> first = jhjAddressResponseDataList.stream().filter(u -> u.getIndex().equals(addressId)).findFirst();
        if (!first.isPresent()) {
            return null;
        }
        JhjAddressResponseData jhjAddressResponseData = first.get();
        JhjCreateOrderRequest jhjCreateOrderRequest = new JhjCreateOrderRequest();
        JhjExpressRequest jhjExpressRequest = BeanUtil.copyProperties(jhjAddressResponseData, JhjExpressRequest.class);
        jhjCreateOrderRequest.setRegister(jhjExpressRequest);
        //商城id
        jhjCreateOrderRequest.setPointsMallId(jhjConfig.getPointsMallId());
        Long jhjThirdGoodsId = getJhjThirdGoodsId(appItemId);
        if (jhjThirdGoodsId == null) {
            // 取商品编码
            List<AppItemSkuDto> skuByItemIdList = remoteAppItemSkuService.findSkuByItemIdList(Collections.singletonList(appItemId));
            log.info("金徽酒查询AppItemSkuDto:{},userId:{}", JSON.toJSONString(skuByItemIdList), userId);
            if (CollectionUtils.isEmpty(skuByItemIdList)) {
                return null;
            }
            String merchantCoding = skuByItemIdList.get(0).getMerchantCoding();
            if (StringUtils.isBlank(merchantCoding)) {
                return null;
            } else {
                jhjThirdGoodsId = Long.valueOf(merchantCoding);
            }
        }
        jhjCreateOrderRequest.setPresentId(jhjThirdGoodsId);
        jhjCreateOrderRequest.setUserId(userId);
        jhjCreateOrderRequest.setToken(token);
        jhjCreateOrderRequest.setSerialId(serialId);
        log.info("金徽酒创建第三方订单 request:{}", JSON.toJSONString(jhjCreateOrderRequest));
        JhjCreateOrderResponseData order = remoteJhjService.createOrder(jhjCreateOrderRequest);
        log.info("金徽酒创建第三方订单 request:{}, response:{}", JSON.toJSONString(jhjCreateOrderRequest), order);
        return order == null ? null : order.getId();

    }

    private Long getJhjThirdGoodsId(Long appItemId) {
        String whiteListJson = WhiteAccessUtil.selectWhiteListJsonConfig(JHJ_GOODS_WHITE);
        JhjWhiteConfig jhjWhiteConfig = null;
        try {
            jhjWhiteConfig = JSONObject.parseArray(whiteListJson, JhjWhiteConfig.class).stream()
                    .filter(t -> Objects.equals(t.getAppItemId(), appItemId.toString()))
                    .findFirst().orElse(null);
        } catch (Exception e) {
            log.warn("金徽酒白名单异常，whiteListJson=[{}]", whiteListJson, e);
        }
        if (jhjWhiteConfig != null) {
            return jhjWhiteConfig.getPresentId();
        }
        return null;
    }


    public JSONObject buildOrderExtraObj(String extraInfo, Long jhjId) {
        JSONObject extraObj = new JSONObject();
        if (StringUtils.isNotBlank(extraInfo)) {
            extraObj = JSONObject.parseObject(extraInfo);
        }
        extraObj.put("jhj_third_order_id", jhjId);
        return extraObj;
    }


    /**
     * 金徽酒
     *
     * <AUTHOR>
     * @date 2025/05/20
     */
    public static class JhjWhiteConfig {
        private String appItemId;
        /**
         * 客户商品id
         */
        private Long presentId;

        public String getAppItemId() {
            return appItemId;
        }

        public void setAppItemId(String appItemId) {
            this.appItemId = appItemId;
        }


        public Long getPresentId() {
            return presentId;
        }

        public void setPresentId(Long presentId) {
            this.presentId = presentId;
        }
    }

}

