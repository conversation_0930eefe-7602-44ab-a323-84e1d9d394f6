/**
 * 文件名： JsonUtils.java 此类描述的是： 作者: leiliang 创建时间: 2016年3月23日 上午10:48:33
 */
package cn.com.duiba.pcg.service.biz.service.purchase.mall;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * ClassName: JsonUtils <br/>
 * 
 * 
 * date: 2016年7月13日 上午9:57:39 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.6
 */
public class JsonUtils {

    /**
     */
    public static final ObjectMapper mapper     = new ObjectMapper();
    /**
     */
    private static String       jsonString = null;
    private static final Logger log = LoggerFactory.getLogger(JsonUtils.class);
    static {
        //创建输出全部属性到Json字符串的Mapper.
        mapper.setSerializationInclusion(Include.ALWAYS);
    }

    /**
     * 对象转JSON
     * 
     * @param response
     * @param obj
     */
    public static void objToJSON(HttpServletResponse response, final Object obj) {
        try {
            setResponse(response);
            jsonString = objectToString(obj);
            PrintWriter out = response.getWriter();
            out.print(jsonString);
            out.close();
        } catch (IOException e) {
            log.error("objToJSON failed", e);
        }
    }

    /**
     * 如果对象为Null, 返回"null". 如果集合为空集合, 返回"[]".
     * 
     * @param obj
     * @return xx
     */
    public static String objectToString(Object obj) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("invoke objectToString failed", e);
            return null;
        }
    }

    /**
     * 如果JSON字符串为Null或"null"字符串, 返回Null. 如果JSON字符串为"[]", 返回空集合. 如需读取集合如List/Map,
     * 且不是List<String>这种简单类型时,先使用函數constructParametricType构造类型.
     * 
     * @see #constructParametricType(Class, Class...)
     * @param clazz
     * @param json
     * @return XX
     */
    public static <T> T jsonToObject(Class<T> clazz, String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        try {
            return mapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("invoke jsonToObject failed", e);
            return null;
        }
    }

    /**
     * @param json
     * @param typeReference
     * @return xx
     */
    public static <T> T parseJsonToObject(String json, TypeReference<T> typeReference) {
        T result = null;
        try {
            result = mapper.readValue(json, typeReference);
        } catch (IOException e) {
            log.error("invoke parseJsonToObject failed", e);
            return null;
        }
        return result;
    }

    /**
     * 设置编码格式
     * 
     * @param response
     */
    public static void setResponse(HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html");
    }
}
