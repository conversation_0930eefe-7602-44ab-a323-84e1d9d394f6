package cn.com.duiba.pcg.service.biz.service;


import java.util.List;
import java.util.Map;

/**
 * @ClassName: YingyongbaoService
 * @Description: 应用宝商品推荐自定义
 * @author: fxr
 * @date: 2018/4/28 10:00
 * @version: V1.0.0
 */
public interface YingyongbaoService {

    /**
     * 获取应用宝自推荐商品列表
     *
     * @param uid
     * @param transfer
     * @param list
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * @throw
     */
    List<Map<String, Object>> customizeItemList(String uid, String transfer, List<Map<String, Object>> list);
}
