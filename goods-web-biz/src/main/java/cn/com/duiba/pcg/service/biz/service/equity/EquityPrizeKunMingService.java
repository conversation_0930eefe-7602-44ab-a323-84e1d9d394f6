package cn.com.duiba.pcg.service.biz.service.equity;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.pcg.service.biz.request.equity.MatchIdentityExchangeReq;
import cn.com.duiba.pcg.service.biz.vo.equity.MatchIdentityEquityVo;
import cn.com.duiba.pcg.service.biz.vo.equity.MatchIdentityExchangeVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface EquityPrizeKunMingService {

    List<MatchIdentityEquityVo> getDevConsumerKunMingEquityList(ConsumerDto consumerDto, String whiteFlagStr, String codesStr);

    MatchIdentityExchangeVo matchIdentityExchange(MatchIdentityExchangeReq param, ConsumerDto consumerDto, String codesStr, HttpServletRequest request, HttpServletResponse response) throws BizException;

}
