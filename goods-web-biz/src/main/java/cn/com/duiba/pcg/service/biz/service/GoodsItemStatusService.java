package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.activity.custom.center.api.dto.hsbc.HsbcAppItemTagDto;
import cn.com.duiba.activity.custom.center.api.remoteservice.hsbc.RemoteHsbcAppItemTagService;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.exception.StatusException;
import cn.com.duiba.pcg.service.biz.ConsumerExchangeLimit;
import cn.com.duiba.pcg.service.biz.config.HFBankConfig;
import cn.com.duiba.pcg.service.biz.config.hsbc.HsbcConfig;
import cn.com.duiba.pcg.service.biz.constants.RedisKeyFactory;
import cn.com.duiba.pcg.service.biz.enums.LimitExtrangeStatusEnum;
import cn.com.duiba.pcg.service.biz.ordervalidator.OrderValidatorException;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.BaseParam;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.ItemParam;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.vo.ExchangeButtonControlInfoVO;
import cn.com.duiba.stock.service.api.remoteservice.RemoteStockService;
import cn.com.duiba.thirdparty.api.hsbc.RemoteHsbcBankServcie;
import cn.com.duiba.thirdparty.dto.hsbc.HsbcTagCheckDto;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.utils.SecurityUtils;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.StringUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 商品当前状态判断，包括：库存、兑换状态、日期时间限制等 Created by xiaoxuda on 2016/12/20.
 */
@Service("goodsItemStatusService")
public class GoodsItemStatusService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GoodsItemStatusService.class);

    @Autowired
    private QuantityLimitService         quantityLimitService;
    @Autowired
    private ConsumerExchangeLimitService consumerExchangeLimitService;
    @Autowired
    private GoodsItemTimeLimitService    goodsItemTimeLimitService;
    @Autowired
    private GoodsItemAddrLimitService    goodsItemAddrLimitService;
    @Autowired
    private RemoteItemKeyService         remoteItemKeyService;
    @Autowired
    private RemoteAppItemSkuService      remoteAppItemSkuService;
    @Autowired
    private ConsumerAccountExchangeLimitService consumerAccountExchangeLimitService;
    @Resource(name = "stringRedisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Resource
    private RemoteHsbcBankServcie remoteHsbcBankServcie;

    @Autowired
    private RemoteHsbcAppItemTagService remoteHsbcAppItemTagService;

    @Autowired
    private HsbcConfig hsbcConfig;

    @Resource
    private HFBankConfig hfBankConfig;

    @Autowired
    private RemoteStockService remoteStockService;

    private Cache<String, Long>          stockCache = CacheBuilder.newBuilder().maximumSize(200).expireAfterWrite(2, TimeUnit.SECONDS).build();

    private Cache<String, Map<Long,Long>>  mallStockCache = CacheBuilder.newBuilder().maximumSize(200).expireAfterWrite(2, TimeUnit.SECONDS).build();


    /**已下架*/
    public static final int              IS_OFF_SHELVES           = 1;                  
    /**已兑完*/
    public static final int              IS_REMAINING_0           = 2;                                                   
    /**已兑换*/
    public static final int              IS_HAVE_EXCHANGE_FOREVER = 3;                                                    
    /**今日已兑完*/
    public static final int              IS_TODAY_NONE            = 4; 
    /**今日已兑换*/
    public static final int              IS_HAVE_EXCHANGE_TODAY   = 5;                                                    
    /**今日已结束*/
    public static final int              IS_TODAY_OVER            = 6;                                                    
    /**即将开始*/
    public static final int              IS_GOING_TO_START        = 7;                                                    
    /**马上兑换*/
    public static final int              GOTO_EXCHANGE            = 8;                                                    
    /**暂不可用*/
    public static final int              TEMP_NOT_AVAILABLE       = 9;                                                    
    /**已结束*/
    public static final int              IS_END                   = 10;                                                   
    /**未登录*/
    public static final int              IS_NOTLOGIN              = 11;
    /**免费兑换*/
    public static final int              GOTO_FREE_EXCAHNGE       = 14;
    /**积分不足*/
    public static final int              IS_LOW_CREDITS           = 15;                                                   
    /**地域限制*/
    public static final int IS_ADDR_LIMIT = 16;

    private static final String HUIFENG_ACCESS_KEY = "HBGJCZSP_wangyongxia";


    /**
     * 给前端的一些展示状态，用于统一给用户的视觉效果
     */
    public enum StatusTypeEnum {
        /**已下架*/
        IS_OFF_SHELVES(1, "商品已下架"),
        /**已兑完*/
        IS_REMAINING_0(2, "商品售罄"),
        /**已兑换*/
        IS_HAVE_EXCHANGE_FOREVER(3, "已达兑换次数上限"),
        /**今日已兑完*/
        IS_TODAY_NONE(4, "每日限量已兑完"),
        /**今日已兑换*/
        IS_HAVE_EXCHANGE_TODAY(5, "今日已达兑换上限"),
        /**今日已结束*/
        IS_TODAY_OVER(6, "今日已结束"), 
        /**即将开始*/
        IS_GOING_TO_START(7, "即将开始"),
        /**马上兑换*/
        GOTO_EXCHANGE(8, "马上兑换"),
        /**暂不可用*/
        TEMP_NOT_AVAILABLE(9, "暂不可用"), 
        /**已结束*/
        IS_END(10, "已结束"), 
        /**该地区不可兑*/
        FORBID_BY_ADDR(11, "该区域不支持兑换"),
        /**会员等级不可兑*/
        VIP_UNMATCH(12, "未达到会员等级要求"),
        /**已兑换*/
        IS_HAVE_EXCHANGE_PERIOD(13,"已达兑换次数上限"),
        /**免费兑换*/
        GOTO_FREE_EXCHANGE(14, "免费兑换"),
        /**时间限制*/
        TIME_LIMIT(17, "限定日期内兑换"),
        /**
         * 会员商品无兑换权限
         */
        VIP_GOODS_CAN_NOT_EXCHANGE(18, "无兑换权限"),
        /**
         * 会员商品暂无兑换权限
         */
        VIP_GOODS_CAN_NOT_EXCHANGE_NOW(19, "暂无兑换权限"),

        /**已兑换*/
        IS_HAVE_EXCHANGE_BATCH(20, "该批次已达兑换上限"),

        /**已兑换*/
        IS_HAVE_EXCHANGE_WEEK(21, "已达本周兑换次数上限"),

        /**已兑换*/
        IS_HAVE_EXCHANGE_MONTH(22, "已达本月兑换次数上限"),

        /**商品标签*/
        GOODS_TAG_LIMIT_EXCHANGE(23, "无法兑换"),
        /**
         * 自定义金额虚拟商品兑换金额不能为空
         */
        CUSTOM_VIRTUAL_EXCHANGE_AMOUNT_NULL(24, "请先输入金额"),
        // 定制 110 开头
        /**
         * 汇丰定制，已兑完
         */
        IS_REMAINING_HF(1102, "商品暂无库存"),
        ;

        private Integer value;
        private String  name;

        StatusTypeEnum(Integer value, String name) {
            this.value = value;
            this.name = name;
        }

        /**
         * @return value
         */
        public Integer getValue() {
            return value;
        }

        /**
         * @return name
         */
        public String getName() {
            return name;
        }
    }

    /**
     * 获取商品兑换状态类型
     * @param itemKey
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getItemStatusType(ItemKeyDto itemKey) {
        return this.getItemStatusType(itemKey,RequestTool.getIpAddr(RequestLocal.getRequest()));
    }

    /**
     * 获取商品兑换状态类型
     * @param itemKey
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getItemStatusTypeNew(ItemKeyDto itemKey) {
        // 判断是否 已下架
        if (isOffShelves(itemKey) || ItemKeyUtil.isDeleted(itemKey)) {
            return StatusTypeEnum.IS_OFF_SHELVES;
        }

        // 判断是否暂不可用
        if (isTempNotAvailable(itemKey)) {
            return StatusTypeEnum.TEMP_NOT_AVAILABLE;
        }
        //今日已兑完
        if(todayNone(itemKey)){
            return StatusTypeEnum.IS_TODAY_NONE;
        }
        // 判断是否 已兑完
        if (isRemaining0(itemKey)) {
            if (hfBankConfig.getAppIds().contains(itemKey.getAppId())){
                return StatusTypeEnum.IS_REMAINING_HF;
            }
            return StatusTypeEnum.IS_REMAINING_0;
        }

        StatusTypeEnum statusType = timeLimit(itemKey);
        if (statusType != null) {
            return statusType;
        }
        if (isAutoOffShelves(itemKey)) {
            return StatusTypeEnum.IS_OFF_SHELVES;
        }

        // 马上兑换
        return StatusTypeEnum.GOTO_EXCHANGE;
    }

    /**
     * 获取商品兑换状态类型
     * @param itemKey
     * @param ip
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getItemStatusType(ItemKeyDto itemKey,String ip) {
        // 判断是否 已下架
        if (isOffShelves(itemKey) || ItemKeyUtil.isDeleted(itemKey)) {
            return StatusTypeEnum.IS_OFF_SHELVES;
        }

        // 判断是否暂不可用
        if (isTempNotAvailable(itemKey)) {
            return StatusTypeEnum.TEMP_NOT_AVAILABLE;
        }

        // 兑吧优惠券地域限制
        if ((itemKey.isItemMode() || itemKey.isDuibaAppItemMode())
            && ItemDto.TypeCoupon.equals(itemKey.getItemDtoType())) {
            boolean re = goodsItemAddrLimitService.couponCanExchange(itemKey, ip);
            if (!re) {
                return StatusTypeEnum.FORBID_BY_ADDR;
            }
        }
        //今日已兑完
        if(todayNone(itemKey)){
            return StatusTypeEnum.IS_TODAY_NONE;
        }
        // 判断是否 已兑完
       if (isRemaining0(itemKey)) {
           if (hfBankConfig.getAppIds().contains(itemKey.getAppId())){
               return StatusTypeEnum.IS_REMAINING_HF;
           }
            return StatusTypeEnum.IS_REMAINING_0;
        }

        StatusTypeEnum statusType = timeLimit(itemKey);
        if (statusType != null) {
            return statusType;
        }
        if (isAutoOffShelves(itemKey)) {
            return StatusTypeEnum.IS_OFF_SHELVES;
        }

        // 马上兑换
        return StatusTypeEnum.GOTO_EXCHANGE;
    }

    @Nullable
    public StatusTypeEnum timeLimit(ItemKeyDto itemKey) {
        // 新版本商品逻辑，原则新增只需要考虑新版本
        if(itemKey.isV1()){
            // 判断是否 兑换时间限制
            if (!goodsItemTimeLimitService.limitDayCanTakeOrder(itemKey)
                    || !goodsItemTimeLimitService.limitStartTimeCanTakeOrder(itemKey)
                    || !goodsItemTimeLimitService.limitTimeCanTakeOrder(itemKey)
                    || !goodsItemTimeLimitService.limitCircleTimeCanTakeOrder(itemKey)) {
                return StatusTypeEnum.TIME_LIMIT;
            }
        }else{
            // 判断日期限制是否 结束
            if (goodsItemTimeLimitService.limitDayIsOver(itemKey)) {
                return StatusTypeEnum.IS_END;
            }

            // 判断是否 今日已结束
            if (goodsItemTimeLimitService.limitDayCanTakeOrder(itemKey) && isTodayOver(itemKey)) {
                if (WhiteAccessUtil.matchWhiteList(itemKey.getAppId(), HUIFENG_ACCESS_KEY)) {
                    return StatusTypeEnum.TIME_LIMIT;
                }
                return StatusTypeEnum.IS_TODAY_OVER;
            }
            Pair<Boolean, StatusTypeEnum> pair = goingToStart(itemKey);
            if(pair.getLeft()){
                return pair.getRight();
            }
        }
        return null;
    }

    private boolean todayNone(ItemKeyDto itemKey){
        // 判断是否 今日已兑完（兑吧商品每日预分配限量库存不足）
        if (isPreTodayNone(itemKey)) {
            return true;
        }
        // 判断已抢光
        if (goodsItemTimeLimitService.timeLimitSwitch(itemKey)) {
            return true;
        }

        if(isTodayNone(itemKey)){
            return true;
        }
        return false;
    }

    public Pair<Boolean, StatusTypeEnum> goingToStart(ItemKeyDto itemKey){
        // 判断日期限制是否 即将开始
        if (goodsItemTimeLimitService.limitDayIsStart(itemKey)) {
            return Pair.of(true, StatusTypeEnum.IS_GOING_TO_START);
        }

        // 判断限时是否 即将开始
        if (isGoingToStart(itemKey)) {
            if (WhiteAccessUtil.matchWhiteList(itemKey.getAppId(), HUIFENG_ACCESS_KEY)) {
                return Pair.of(true, StatusTypeEnum.TIME_LIMIT);
            }
            return Pair.of(true, StatusTypeEnum.IS_GOING_TO_START);
        }

        // 判断日期限制
        if (!goodsItemTimeLimitService.limitDayCanTakeOrder(itemKey)) {
            return Pair.of(true, StatusTypeEnum.IS_GOING_TO_START);
        }
        // 判断日期限制
        if (!goodsItemTimeLimitService.canTakeOrder(itemKey)) {
            return Pair.of(true, StatusTypeEnum.IS_GOING_TO_START);
        }
        return Pair.of(false, null);
    }

    /**
     * @param statusTypeEnum
     */
    public void  exchangeChangeConvertor(StatusTypeEnum statusTypeEnum) {
        if (statusTypeEnum == null){
            return;
        }
        if (StatusTypeEnum.GOTO_EXCHANGE.getValue().equals(statusTypeEnum.getValue())) {
            return;
        }
        if (StatusTypeEnum.IS_OFF_SHELVES.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeAppItemOffline).getMessage());
        }
        if (StatusTypeEnum.TEMP_NOT_AVAILABLE.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException("对不起，系统繁忙了，请稍后再试哦");
        }
        if (StatusTypeEnum.IS_REMAINING_0.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeQuantityLimitNoStock).getMessage());
        }

        if (StatusTypeEnum.FORBID_BY_ADDR.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeInAddrLimit).getMessage());
        }

        if (StatusTypeEnum.IS_TODAY_NONE.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeQuantityLimitNoStock).getMessage());
        }

        if (StatusTypeEnum.IS_END.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(StatusTypeEnum.IS_END.getName());
        }

        if (StatusTypeEnum.IS_TODAY_OVER.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeQuantityLimitNoStock).getMessage());
        }

        if (StatusTypeEnum.IS_GOING_TO_START.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeNotInLimitTime).getMessage());
        }

        if (StatusTypeEnum.IS_OFF_SHELVES.getValue().equals(statusTypeEnum.getValue())) {
            throw new GoodsWebException(new StatusException(StatusException.CodeAppItemOffline).getMessage());
        }
        throw new GoodsWebException("对不起，系统繁忙了，请稍后再试哦");
    }

    /**
     * 获取用户兑换状态，（是否可兑，今日已兑，永久已兑）
     *
     * @param itemKey
     * @param consumer
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getUserExtrangeStatus(ItemKeyDto itemKey, ConsumerDto consumer) {
        // 判断用户是否已兑完
        BaseParam param = null;
        if(itemKey.getAppItem()!=null&&itemKey.getAppItem().getAppItemSkuDtoList()!=null){
            List<AppItemSkuDto>  skuList = itemKey.getAppItem().getAppItemSkuDtoList();
            List<AppItemSkuDto> skuListFilter = skuList.stream().filter(AppItemSkuDto->AppItemSkuDto.getSaleStatus()==null||AppItemSkuDto.getSaleStatus().intValue()==1).collect(Collectors.toList());
            if(skuListFilter.size()==1){
                AppItemSkuDto appItemSkuDto = skuListFilter.get(0);
                param = new BaseParam();
                List<ItemParam> itemParams = new ArrayList<>();
                ItemParam itemParam = new ItemParam();
                itemParam.setAppItemId(appItemSkuDto.getAppItemId());
                itemParam.setSkuId(appItemSkuDto.getId());
                itemParams.add(itemParam);
                param.setItemParam(itemParams);
            }
        }
        ConsumerExchangeLimit consumerExchangeLimit = isHaveExchange(consumer, itemKey,param);
        if (!consumerExchangeLimit.getCanExchange()) {
            if (LimitExtrangeStatusEnum.DAILAYUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_TODAY;
            } else if (LimitExtrangeStatusEnum.FOREVERUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_FOREVER;
            }  else if (LimitExtrangeStatusEnum.BATCHUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_BATCH;
            } else if(LimitExtrangeStatusEnum.PERIODUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_PERIOD;
            } else if(LimitExtrangeStatusEnum.WEEKUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_WEEK;
            } else if(LimitExtrangeStatusEnum.MONTHUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_MONTH;
            }
        }
        //判断商品是否已经对完

        return StatusTypeEnum.GOTO_EXCHANGE;
    }

    /**
     * 获取账户兑换状态，（是否可兑，今日已兑，永久已兑）
     *
     * @param itemKey
     * @param consumer
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getAccountExchangeStatus(ItemKeyDto itemKey, ConsumerDto consumer, String accountNo) {
        // 未配置快速失败
        String accountLimitScope = Optional.ofNullable(itemKey).map(ItemKeyDto::getAppItem).map(AppItemDto::getAccountLimitScope).orElse(null);
        Integer accountLimitCount = Optional.ofNullable(itemKey).map(ItemKeyDto::getAppItem).map(AppItemDto::getAccountLimitCount).orElse(null);
        if (StringUtils.isEmpty(accountLimitScope) || Objects.isNull(accountLimitCount)) {
            return StatusTypeEnum.GOTO_EXCHANGE;
        }

        // 判断商品是否已经对完
        ConsumerExchangeLimit consumerExchangeLimit = isHaveAccountExchange(consumer, itemKey, accountNo);
        if (!consumerExchangeLimit.getCanExchange()) {
            if (LimitExtrangeStatusEnum.DAILAYUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_TODAY;
            } else if (LimitExtrangeStatusEnum.FOREVERUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_FOREVER;
            } else if (LimitExtrangeStatusEnum.BATCHUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_BATCH;
            } else if (LimitExtrangeStatusEnum.PERIODUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_PERIOD;
            } else if (LimitExtrangeStatusEnum.WEEKUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_WEEK;
            } else if (LimitExtrangeStatusEnum.MONTHUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_MONTH;
            }
        }

        // 账号限购下单,并发控制让其200ms自动失效,incrBy只在首次变更过期时间，所以并发执行时，过期时间也不会一直延长
        if (consumerExchangeLimit.getCanExchange()) {
            String accountRedisKey = RedisKeyFactory.K202.join(consumerExchangeLimitService.getKey(itemKey), accountNo);
            Long num = redisAtomicClient.incrBy(accountRedisKey, 1, 200, TimeUnit.MILLISECONDS);
            if (num > 1) {
                throw new OrderValidatorException(ErrorCode.E2001001);
            }
        }

        return StatusTypeEnum.GOTO_EXCHANGE;
    }

    /**
     * 虚拟商品账号限制
     * @param consumer
     * @param itemKey
     * @param accountNo
     * @return
     */
    private ConsumerExchangeLimit isHaveAccountExchange(ConsumerDto consumer, ItemKeyDto itemKey, String accountNo) {
        ConsumerExchangeLimit consumerExchangeLimit = consumerAccountExchangeLimitService
                .getConsumerExchangeLimit(consumer, itemKey, accountNo);
        return consumerExchangeLimit;
    }

    /**
     * 获取用户兑换状态，（是否可兑，今日已兑，永久已兑）
     *
     * @param itemKey
     * @param consumer
     * @return StatusTypeEnum
     */
    public StatusTypeEnum getUserExtrangeStatus(ItemKeyDto itemKey, ConsumerDto consumer,BaseParam param) {
        // 判断用户是否已兑完
        ConsumerExchangeLimit consumerExchangeLimit = isHaveExchange(consumer, itemKey,param);
        if (!consumerExchangeLimit.getCanExchange()) {
            if (LimitExtrangeStatusEnum.DAILAYUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_TODAY;
            } else if (LimitExtrangeStatusEnum.FOREVERUSED.getCode().equals(consumerExchangeLimit.getLimitType())) {
                return StatusTypeEnum.IS_HAVE_EXCHANGE_FOREVER;
            } else if(LimitExtrangeStatusEnum.PERIODUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_PERIOD;
            } else if(LimitExtrangeStatusEnum.BATCHUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_BATCH;
            } else if(LimitExtrangeStatusEnum.WEEKUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_WEEK;
            }  else if(LimitExtrangeStatusEnum.MONTHUSED.getCode().equals(consumerExchangeLimit.getLimitType())){
                return StatusTypeEnum.IS_HAVE_EXCHANGE_MONTH;
            }
        }
        //判断商品是否已经对完

        return StatusTypeEnum.GOTO_EXCHANGE;
    }

    /**
     * 判断是否 已下架 返回true表示已下架，返回false表示未下架
     */
    public Boolean isOffShelves(ItemKeyDto itemKey) {
        if (itemKey.isItemMode()) {
            if (!itemKey.getItem().getEnable()) {
                return true;
            }
            return false;
        }
        if (itemKey.isSelfAppItemMode()) {
            if (!AppItemDto.StatusOn.equals(itemKey.getAppItem().getStatus())) {
                return true;
            }
            return false;
        }
        if (itemKey.isDuibaAppItemMode()) {
            if (!AppItemDto.StatusOn.equals(itemKey.getAppItem().getStatus()) || !itemKey.getItem().getEnable()) {// NOSONAR
                return true;
            }
        }
        return false;
    }

    /**
     * 是否已删除
     *
     * @param itemKey
     * @return boolean
     */
    public boolean isDeleted(ItemKeyDto itemKey) {
        if (itemKey.isItemMode()) {
            return itemKey.getItem().getDeleted() != null && itemKey.getItem().getDeleted();
        } else if (itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().getDeleted() != null && itemKey.getItem().getDeleted()) {
                return true;
            } else if (itemKey.getAppItem().getDeleted() != null && itemKey.getAppItem().getDeleted()) {
                return true;
            } else {
                return false;
            }
        } else if (itemKey.isSelfAppItemMode()) {
            return itemKey.getAppItem().getDeleted() != null && itemKey.getAppItem().getDeleted();
        }
        return true;
    }

    /**
     * 判断是否暂不可用
     *
     * @param itemKey
     * @return
     */
    public Boolean isTempNotAvailable(ItemKeyDto itemKey) {
        if (itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().getShutDown() == null) {
                return false;
            }
            if (itemKey.getItem().getShutDown()) {
                return true;
            }
        }
        if (itemKey.isSelfAppItemMode()) {
            return false;
        }
        return false;
    }

    /**
     * 判断是否 已兑完（库存不足） 返回false表示已兑完，返回ture表示兑完 如果是定向商品就取定向库存
     * @param itemKey
     * @return Boolean
     */
    public Boolean isRemaining0(ItemKeyDto itemKey) {

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        boolean b = huifengDayLimitCheck(consumer.getAppId(), consumer.getPartnerUserId(), itemKey);
        AppItemDto appItem = itemKey.getAppItem();
        boolean opTypeAppItem = false;
        if(Objects.isNull(appItem)) {
            b = true;
        } else {
            opTypeAppItem = appItem.isOpTypeAppItem(ItemDto.OpTypeQuantityLimit);
        }
        try {
            if(!b){
                if(opTypeAppItem){
                    appItem.ungrantOpTypeAppItem(ItemDto.OpTypeQuantityLimit);
                }
            }

            Long stock = getStockByCache(itemKey);

            return stock != null && stock <= 0;
        }finally {
            if(opTypeAppItem){
                appItem.grantOpTypeAppItem(ItemDto.OpTypeQuantityLimit);
            }
        }

    }

    /**
     * ItemKeyDto 库存缓存
     * @param itemKey
     * @return
     */
    public Long getStockByCache( final ItemKeyDto itemKey){
        try {
            String appItemId = itemKey.getAppItem()!=null?String.valueOf(itemKey.getAppItem().getId()):"";
            String itemId = itemKey.getItem()!=null?String.valueOf(itemKey.getItem().getId()):"";
            String key = appItemId+"_"+itemId+"_"+itemKey.getAppId();
            return stockCache.get(key, new Callable<Long>() {
                @Override
                public Long call() throws Exception {
                    return remoteItemKeyService.findStock(itemKey).getResult();
                }
            });
        } catch (Exception e) {
            return remoteItemKeyService.findStock(itemKey).getResult();
        }
    }



    /**
     * 查询自有商品sku维度库存
     * @param skuDtos
     * @return
     */
    public Map<Long,Long> getAppItemSkuStock(List<AppItemSkuDto> skuDtos){
        if(CollectionUtils.isEmpty(skuDtos)){
            return Maps.newHashMap();
        }
        List<Long> stockIds = skuDtos.stream().map(AppItemSkuDto::getStockId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(stockIds)){
            return Maps.newHashMap();
        }
        String cacheKey = SecurityUtils.encode2StringByMd5(stockIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        try{
            return mallStockCache.get(cacheKey, () -> remoteStockService.findBatch(stockIds).getResult());
        }catch (Exception e){
            LOGGER.warn("查询自有商品sku维度库存,参数={}",stockIds.toString(),e);
            return remoteStockService.findBatch(stockIds).getResult();
        }

    }

    /**
     * 判断是否 已兑完（库存不足） 返回false表示已兑完，返回ture表示兑完 如果是定向商品就取定向库存
     * 备注：优惠券的库存不适用于此方法
     * @param skuId
     * @return Boolean
     */
    public Boolean isSkuRemaining(Long skuId) {

        Long stock = remoteAppItemSkuService.findStock(skuId);

        if (stock != null && stock <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取剩余库存数，0表示不足
     * @param itemKey
     * @return
     */
    public long getStock(ItemKeyDto itemKey) {
        DubboResult<Long> ret = remoteItemKeyService.findStock(itemKey);
        if (!ret.isSuccess()) {
            throw new GoodsWebException(ret.getMsg());
        }
        Long stock = ret.getResult();
        if (stock != null && stock > 0) {
            return stock.longValue();
        }
        return 0;
    }

    /**
     * @param itemKey
     * @return Boolean
     */
    public Boolean isAutoOffShelves(ItemKeyDto itemKey) {
        if (itemKey.isSelfAppItemMode()) {
            if (itemKey.getAppItem().getAutoOffDate() != null
                && itemKey.getAppItem().getAutoOffDate().before(new Date())) {
                return true;
            }
        } else if (itemKey.isItemMode()) {
            if (itemKey.getItem().getAutoOffDate() != null && itemKey.getItem().getAutoOffDate().before(new Date())) {
                return true;
            }
        } else if (itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().getAutoOffDate() != null && itemKey.getItem().getAutoOffDate().before(new Date())) {
                return true;
            }
            if (itemKey.getAppItem().getAutoOffDate() != null
                       && itemKey.getAppItem().getAutoOffDate().before(new Date())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回用户针对给定商品的兑换情况
     *
     * @param consumer
     * @param itemKey
     * @return ConsumerExchangeLimit
     */
    public ConsumerExchangeLimit isHaveExchange(ConsumerDto consumer, ItemKeyDto itemKey,BaseParam param) {
        ConsumerExchangeLimit consumerExchangeLimit = consumerExchangeLimitService
                .getConsumerExtrangeLimit(consumer, itemKey,param);

        boolean exchange = consumerExchangeLimit.getCanExchange();
        consumerExchangeLimit.setCanExchange(exchange);
        if (itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().getLimitCount() != null) {
                setDuibaConsumerExchangeLimit(consumerExchangeLimit,itemKey.getItem().getLimitScope());
            } else if (itemKey.getAppItem() != null && itemKey.getAppItem().getLimitCount() != null) {
                setDuibaConsumerExchangeLimit(consumerExchangeLimit,itemKey.getAppItem().getLimitScope());
            }
        } else if (itemKey.isSelfAppItemMode() && itemKey.getAppItem().getLimitCount() != null) {
                if (itemKey.getAppItem().getLimitScope().equals(ItemDto.LimitTypeEveryday)) {
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypeEveryday);
                } else if (itemKey.getAppItem().getLimitScope().equals(ItemDto.LimitTypeForever)) {
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypeForever);
                } else if(ItemDto.LimitTypeBatch.equals(itemKey.getAppItem().getLimitScope())){
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypeBatch);
                } else if(ItemDto.LimitTypeWeek.equals(itemKey.getAppItem().getLimitScope())){
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypeWeek);
                } else if(ItemDto.LimitTypeMonth.equals(itemKey.getAppItem().getLimitScope())){
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypeMonth);
                } else {
                    consumerExchangeLimit.setLimitType(ItemDto.LimitTypePeriod);
                }
        }

        return consumerExchangeLimit;
    }

    private void setDuibaConsumerExchangeLimit(ConsumerExchangeLimit consumerExchangeLimit,String limitScope){
        if (ItemDto.LimitTypeEveryday.equals(limitScope)) {
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypeEveryday);
        } else if(ItemDto.LimitTypeBatch.equals(limitScope)){
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypeBatch);
        } else if(ItemDto.LimitTypePeriod.equals(limitScope)){
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypePeriod);
        } else if(ItemDto.LimitTypeMonth.equals(limitScope)){
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypeMonth);
        } else if(ItemDto.LimitTypeWeek.equals(limitScope)){
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypeWeek);
        } else {
            consumerExchangeLimit.setLimitType(ItemDto.LimitTypeForever);
        }
    }


    /**
     * 判断是否 今日已兑完（每日限量库存不足或总库存不足） 返回true表示今日已兑完 返回false表示今日未兑完
     * @param itemKey
     * @return Boolean
     */
    public Boolean isTodayNone(ItemKeyDto itemKey) {

        ConsumerDto consumer = RequestLocal.getConsumerDO();
        boolean b = huifengDayLimitCheck(consumer.getAppId(), consumer.getPartnerUserId(), itemKey);
        if(!b){
            return false;
        }

        Integer todayStock = quantityLimitService.getQuantityLimitItemRemaining(itemKey);
        if (todayStock == null) {
            return false;
        }
        if (todayStock <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否 今日已兑完（每日限量库存不足或总库存不足） 返回true表示今日已兑完 返回false表示今日未兑完
     * @param itemKey
     * @return Boolean
     */
    public Boolean wandaIsTodayNone(ItemKeyDto itemKey,Integer quantity) {
        Integer todayStock = quantityLimitService.getQuantityLimitItemRemaining(itemKey);
        if (todayStock == null) {
            return false;
        }
        if (todayStock <= quantity) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否预分配每日限量 今日已兑完（每日限量库存不足或总库存不足） 返回true表示今日已兑完 返回false表示今日未兑完
     * @param itemKey 
     * 
     * @return Boolean
     */
    public Boolean isPreTodayNone(ItemKeyDto itemKey) {
        Integer preTodayStock = quantityLimitService.getPreEeveydayStock(itemKey);
        if (preTodayStock == null) {
            return false;
        }
        if (preTodayStock <= 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否 今日已结束 返回true表示今日已结束，返回false表示今日未结束
     * 
     * @param itemKey
     * @return xx
     */
    public boolean isTodayOver(ItemKeyDto itemKey) {
        return goodsItemTimeLimitService.isTodayOver(itemKey);
    }

    /**
     * 判断是否 即将开始 返回true表示即将开始，返回false表示非即将开始
     */
    private boolean isGoingToStart(ItemKeyDto itemKey) {
        return goodsItemTimeLimitService.isGoingToStart(itemKey);
    }

    public void setExchangeButtonControlInfoVO(ItemKeyDto key,ExchangeButtonControlInfoVO exchangeButtonControlInfoVO,String buttonTest){
        if (exchangeButtonControlInfoVO.getExchangeStatus() == GoodsItemStatusService.StatusTypeEnum.GOTO_EXCHANGE.getValue()) {
            exchangeButtonControlInfoVO.setExchangeText(buttonTest);
            exchangeButtonControlInfoVO.setExchangeEnable(true);
            exchangeButtonControlInfoVO.setLock(false);
        } else {
            exchangeButtonControlInfoVO.setExchangeEnable(false);
            exchangeButtonControlInfoVO.setLock(true);
            if(exchangeButtonControlInfoVO.getExchangeStatus().intValue() == GoodsItemStatusService.StatusTypeEnum.TIME_LIMIT.getValue().intValue()){
                if (!goodsItemTimeLimitService.limitDayCanTakeOrder(key)) {
                    String[] limitDate = goodsItemTimeLimitService.getLimitDay(key);
                    if (limitDate != null) {
                        exchangeButtonControlInfoVO.setExchangeText("限" + limitDate[0] + "至" + limitDate[1] + "日期内兑换");
                    }
                } else if (!goodsItemTimeLimitService.limitDayCanTakeOrder(key)){
                    String everyDayLimitTime = (Objects.nonNull(key.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotEmpty(key.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) ? key.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime() : key.getAppItem().getLimitTimeBetween();
                    String[] limitDate = everyDayLimitTime.split("-");
                    exchangeButtonControlInfoVO.setExchangeText("限" + limitDate[0] + "至" + limitDate[1] + "内兑换");
                }else if(!goodsItemTimeLimitService.limitStartTimeCanTakeOrder(key)){
                    String limitStartTime = key.getAppItem().getMarketingItemCreditsDto().getLimitStartTime();
                    exchangeButtonControlInfoVO.setExchangeText(limitStartTime +"可兑换");
                }else if (!goodsItemTimeLimitService.limitCircleTimeCanTakeOrder(key)){
                    exchangeButtonControlInfoVO.setExchangeText(goodsItemTimeLimitService.getLimitCircleTimeTip(key));
                }
            }
        }
    }





    /**
     * 汇丰定制 是否每日限制
     * @param appId
     * @param partnerUserId
     * @param itemKeyDto
     * @return true要限制 false不限制
     */
    public boolean huifengDayLimitCheck(Long appId, String partnerUserId, ItemKeyDto itemKeyDto){

        if(!hsbcConfig.getAppIds().contains(appId)){
            return true;
        }

        Long id = itemKeyDto.getAppItem().getId();
        List<HsbcAppItemTagDto> hsbcAppItemTagDtos = remoteHsbcAppItemTagService.listAllByAppId(appId);

        // 关联的商品标签，在可见可兑列表内的
        List<String> tagCodes=new ArrayList<>();
        List<HsbcAppItemTagDto> aboutItems = hsbcAppItemTagDtos.stream().filter(f -> {
            // 只要不限制的数据
            if(f.isSwitchOpen(HsbcAppItemTagDto.EVERYDAY_STOCK_LIMIT)){
                return false;
            }
            String exchangeAppItemIds = f.getExchangeAppItemIds();
            if (org.apache.commons.lang.StringUtils.isNotBlank(exchangeAppItemIds)) {
                boolean b = JSON.parseArray(exchangeAppItemIds).contains(id);
                if (b) {
                    tagCodes.add(f.getTagCode());
                    return true;
                }
            }
            String noExchangeAppItemIds = f.getNoExchangeAppItemIds();
            if (org.apache.commons.lang.StringUtils.isNotBlank(noExchangeAppItemIds)) {
                boolean b = JSON.parseArray(noExchangeAppItemIds).contains(id);
                if (b) {
                    tagCodes.add(f.getTagCode());
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());

        Optional<HsbcAppItemTagDto> min = aboutItems.stream().min(Comparator.comparing(HsbcAppItemTagDto::getSort));
        if (min.isPresent()) {
            // 权重值最低的标签
            aboutItems = aboutItems.stream().filter(f -> f.getSort().equals(min.get().getSort())).collect(Collectors.toList());
        }else {
            return true;
        }

        // 获得用户标签
        List<HsbcTagCheckDto> hsbcTagCheckDtos = remoteHsbcBankServcie.checkUserTag(appId, partnerUserId, tagCodes);
        List<String> userCheckSuccessTagCodes = tagCodes.stream().filter(f -> hsbcTagCheckDtos.stream().anyMatch(m -> m.getTagFlag() && m.getTagCode().equals(f))).collect(Collectors.toList());


        // 标签规则校验
        Optional<HsbcAppItemTagDto> result = aboutItems.stream().filter(f1 -> {
            // 用户关联了此标签
            boolean b1 = userCheckSuccessTagCodes.contains(f1.getTagCode());
            if (b1) {
                return true;
            }
            return false;
        }).max(Comparator.comparing(HsbcAppItemTagDto::getGmtModified));

        // 是否在不限制名单里
        return !result.isPresent();

    }


}
