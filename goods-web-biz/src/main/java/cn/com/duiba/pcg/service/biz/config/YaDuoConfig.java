package cn.com.duiba.pcg.service.biz.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2020/5/28 3:27 下午
 */
@Configuration
@ConfigurationProperties(prefix = "yaduo")
@RefreshScope
public class YaDuoConfig {
	/**
	 * appIds
	 */
	private Long appId = 46368L;

	/**
	 * 查询url
	 */
	private String queryUrl = "https://sandbox.wangdian.cn/openapi2/api_goods_stock_change_query.php";

	/**
	 * 回写url
	 */
	private String ackUrl = "https://sandbox.wangdian.cn/openapi2/api_goods_stock_change_ack.php";

	/**
	 * 卖家账号
	 */
	private String sid = "apidevnew2";

	/**
	 * 接口账号
	 */
	private String appkey = "gongyu2-test";

	/**
	 * 接口秘药
	 */
	private String appsecret = "12345";

	/**
	 * 店铺编码
	 */
	private String shopNo = "gongyu2-test";

	/**
	 * 获取数据条数
	 */
	private Integer limit = 1000;


	public String getQueryUrl() {
		return queryUrl;
	}

	public void setQueryUrl(String queryUrl) {
		this.queryUrl = queryUrl;
	}

	public String getAckUrl() {
		return ackUrl;
	}

	public void setAckUrl(String ackUrl) {
		this.ackUrl = ackUrl;
	}

	public String getSid() {
		return sid;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	public String getAppkey() {
		return appkey;
	}

	public void setAppkey(String appkey) {
		this.appkey = appkey;
	}

	public Integer getLimit() {
		return limit;
	}

	public void setLimit(Integer limit) {
		this.limit = limit;
	}

	public String getShopNo() {
		return shopNo;
	}

	public void setShopNo(String shopNo) {
		this.shopNo = shopNo;
	}

	public String getAppsecret() {
		return appsecret;
	}

	public void setAppsecret(String appsecret) {
		this.appsecret = appsecret;
	}

	public Long getAppId() {
		return appId;
	}

	public void setAppId(Long appId) {
		this.appId = appId;
	}

}
