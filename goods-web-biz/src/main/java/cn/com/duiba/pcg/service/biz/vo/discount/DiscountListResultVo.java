package cn.com.duiba.pcg.service.biz.vo.discount;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by zhangyongjie on 2021/11/24 4:05 下午
 */
public class DiscountListResultVo {
    private Long id;

    /**
     * 优惠券名称
     */
    private String discountTitle;

    /**
     * 库存id
     */
    private Long stockId;

    /**
     * 优惠券类型
     */
    private Integer discountType;

    /**
     * 优惠券类型名称
     */
    private String discountTypeName;



    /**
     * 优惠券使用范围
     */
    private Integer discountScope;

    /**
     * 优惠券使用范围名称
     */
    private String discountScopeName;


    /**
     * 库存
     */
    private Long stock;

    /**
     * 总数量（发放数量）
     */
    private Long totalStock;


    /**
     * 领取数量
     */
    private Long receiveNum;

    /**
     * 核销数量
     */
    private Long exchangeNum;



    /**
     * 有效期开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date effectiveStartDate;

    /**
     * 有效期结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date effectiveEndDate;


    /**
     * 满减金额（单位分）、满折百分比
     */
    private Long discountAmount;

    /**
     * 上下架状态
     */
    private Integer discountStatus;

    /**
     * 上下架状态名称
     */
    private String discountStatusName;

    public String getDiscountScopeName() {
        return discountScopeName;
    }

    public void setDiscountScopeName(String discountScopeName) {
        this.discountScopeName = discountScopeName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDiscountTitle() {
        return discountTitle;
    }

    public void setDiscountTitle(String discountTitle) {
        this.discountTitle = discountTitle;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public String getDiscountTypeName() {
        return discountTypeName;
    }

    public void setDiscountTypeName(String discountTypeName) {
        this.discountTypeName = discountTypeName;
    }



    public Integer getDiscountScope() {
        return discountScope;
    }

    public void setDiscountScope(Integer discountScope) {
        this.discountScope = discountScope;
    }

    public Long getStock() {
        return stock;
    }

    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public Long getTotalStock() {
        return totalStock;
    }

    public void setTotalStock(Long totalStock) {
        this.totalStock = totalStock;
    }

    public Long getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Long receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Long getExchangeNum() {
        return exchangeNum;
    }

    public void setExchangeNum(Long exchangeNum) {
        this.exchangeNum = exchangeNum;
    }

    public Date getEffectiveStartDate() {
        return effectiveStartDate;
    }

    public void setEffectiveStartDate(Date effectiveStartDate) {
        this.effectiveStartDate = effectiveStartDate;
    }

    public Date getEffectiveEndDate() {
        return effectiveEndDate;
    }

    public void setEffectiveEndDate(Date effectiveEndDate) {
        this.effectiveEndDate = effectiveEndDate;
    }

    public Long getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Long discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Integer getDiscountStatus() {
        return discountStatus;
    }

    public void setDiscountStatus(Integer discountStatus) {
        this.discountStatus = discountStatus;
    }

    public String getDiscountStatusName() {
        return discountStatusName;
    }

    public void setDiscountStatusName(String discountStatusName) {
        this.discountStatusName = discountStatusName;
    }
}