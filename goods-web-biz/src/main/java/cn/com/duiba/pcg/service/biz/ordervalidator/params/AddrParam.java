package cn.com.duiba.pcg.service.biz.ordervalidator.params;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class AddrParam {
    //纬度
    private Double latitude;
    //经度
    private Double longitude;

    private Long appItemId;

    private String ip;

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Long getAppItemId() {
        return appItemId;
    }

    public void setAppItemId(Long appItemId) {
        this.appItemId = appItemId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
