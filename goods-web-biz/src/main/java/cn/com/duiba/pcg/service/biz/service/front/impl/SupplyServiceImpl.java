package cn.com.duiba.pcg.service.biz.service.front.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimaps;
import com.google.common.collect.Sets;

import cn.com.duiba.activity.center.api.dto.alipayactivityredpack.AlipayActivityConfigDto;
import cn.com.duiba.activity.center.api.dto.alipayactivityredpack.AlipayRepackCardDto;
import cn.com.duiba.activity.center.api.enums.alipayactivityredpack.DrawStatusEnum;
import cn.com.duiba.activity.center.api.enums.alipayactivityredpack.RedpackCouponSourceEnum;
import cn.com.duiba.activity.center.api.params.alipayactivityredpack.AlipayActivityConfigPageParam;
import cn.com.duiba.activity.center.api.remoteservice.alipayactivityredpack.RemoteAlipayActivityConfigService;
import cn.com.duiba.activity.center.api.remoteservice.alipayactivityredpack.RemoteRepackCardService;
import cn.com.duiba.api.bo.KeyValueDto;
import cn.com.duiba.api.bo.KeyValueEntity;
import cn.com.duiba.api.bo.page.Page;
import cn.com.duiba.api.enums.GoodsTypeEnum;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.enums.authority.VersionStatusEnum;
import cn.com.duiba.developer.center.api.domain.param.authority.VerisonAppListInfoDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.authority.RemoteAuthorityService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.geo.api.dto.PhoneIspAndAreaDto;
import cn.com.duiba.geo.api.remoteservice.RemotePhoneAreaService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteAddrLimitService;
import cn.com.duiba.goods.center.api.remoteservice.RemoteSupplierGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.dto.AddrLimitDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.CouponModifySkuValidDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.CouponSkuValidDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.SupplierProductDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemAppSpecifyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemBaseDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDescConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemQueries;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.PriceDegreeDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.ItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.enums.ExchangeTypeEnum;
import cn.com.duiba.goods.center.api.remoteservice.enums.ItemNewExtraEnum;
import cn.com.duiba.goods.center.api.remoteservice.enums.VisualItemChannelEnum;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsBackendService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteAppItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteDuibaItemGoodsService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemAppSpecifyService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemKeyService;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemNewExtraService;
import cn.com.duiba.goods.center.api.remoteservice.param.AppItemSkuRemainingParam;
import cn.com.duiba.goods.center.api.remoteservice.param.SupplyItemQueryParam;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteItemSkuService;
import cn.com.duiba.goods.center.api.remoteservice.tool.UnitUtils;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbConsisHashKvService;
import cn.com.duiba.order.center.api.dto.UniqueOrderCheckDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteUniqueOrderCheckService;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.coupon.WxCouponQueryResp;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWechatCouponService;
import cn.com.duiba.pcg.constant.AppIdConstant;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.cache.DeveloperCacheService;
import cn.com.duiba.pcg.service.biz.cache.GoodsCacheService;
import cn.com.duiba.pcg.service.biz.config.Common2Config;
import cn.com.duiba.pcg.service.biz.config.CommonConfig;
import cn.com.duiba.pcg.service.biz.config.DuXiaoManConfig;
import cn.com.duiba.pcg.service.biz.config.HzBankConfig;
import cn.com.duiba.pcg.service.biz.config.JiangSuBocConfig;
import cn.com.duiba.pcg.service.biz.config.JinChengConfig;
import cn.com.duiba.pcg.service.biz.config.LtConfig;
import cn.com.duiba.pcg.service.biz.config.MinShengConfig;
import cn.com.duiba.pcg.service.biz.config.NewMngConfig;
import cn.com.duiba.pcg.service.biz.config.SupplyConfig;
import cn.com.duiba.pcg.service.biz.config.XianDouConfig;
import cn.com.duiba.pcg.service.biz.config.ccb.CcbHeadquartersConfig;
import cn.com.duiba.pcg.service.biz.config.ccb.GdCcbConfig;
import cn.com.duiba.pcg.service.biz.config.ccb.SszCcbConfig;
import cn.com.duiba.pcg.service.biz.config.cgb.CgbConfig;
import cn.com.duiba.pcg.service.biz.config.chongqingyouchu.ChongQingYouChuConfig;
import cn.com.duiba.pcg.service.biz.config.hsbc.HsbcConfig;
import cn.com.duiba.pcg.service.biz.constants.RedisKeyFactory;
import cn.com.duiba.pcg.service.biz.dto.ShortUrlReplaceConfEntity;
import cn.com.duiba.pcg.service.biz.dto.minsheng.MinShengPrizeRuleNoConfig;
import cn.com.duiba.pcg.service.biz.dto.minsheng.PrizeRuleNoConfig;
import cn.com.duiba.pcg.service.biz.enums.preorder.PreOrderStatusEnum;
import cn.com.duiba.pcg.service.biz.param.front.FrontItemItemStockVO;
import cn.com.duiba.pcg.service.biz.param.front.FrontItemSkuStockVO;
import cn.com.duiba.pcg.service.biz.param.front.FrontItemSkuVO;
import cn.com.duiba.pcg.service.biz.param.front.FrontItemSpuStockVO;
import cn.com.duiba.pcg.service.biz.param.front.FrontItemSpuVO;
import cn.com.duiba.pcg.service.biz.param.front.MultiDegreeInfoVO;
import cn.com.duiba.pcg.service.biz.param.front.PhonebillInfoVO;
import cn.com.duiba.pcg.service.biz.processor.extrange.VirtualExtrangeProcessor;
import cn.com.duiba.pcg.service.biz.request.cornucopia.ProjectXQueryOrderParam;
import cn.com.duiba.pcg.service.biz.request.front.FrontItemSpuDetailRequest;
import cn.com.duiba.pcg.service.biz.request.front.FrontItemSpuPageRequest;
import cn.com.duiba.pcg.service.biz.request.minsheng.MsPurchaseDTO;
import cn.com.duiba.pcg.service.biz.request.redpack.WxRedPackOrderDrawParam;
import cn.com.duiba.pcg.service.biz.request.supply.HzBankSupplyPurchaseRetryRequest;
import cn.com.duiba.pcg.service.biz.request.supply.PreOrderDrawParam;
import cn.com.duiba.pcg.service.biz.request.supply.QuerySupplyOrderRequest;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyBaseRequest;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.cornucopia.ProjectXQueryRespDto;
import cn.com.duiba.pcg.service.biz.response.front.FrontItemSpuPageResponse;
import cn.com.duiba.pcg.service.biz.response.front.FrontItemSpuStockResponse;
import cn.com.duiba.pcg.service.biz.response.supply.QuerySupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.response.supply.SupplyOrderExtraResponse;
import cn.com.duiba.pcg.service.biz.response.supply.SupplyPurchaseResponse;
import cn.com.duiba.pcg.service.biz.response.supply.cgb.CgbSupplyPurchaseResponse;
import cn.com.duiba.pcg.service.biz.response.supply.preorder.PreOrderResponse;
import cn.com.duiba.pcg.service.biz.service.ConsumerItemRenderService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemActualPriceCalculateService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemDataService;
import cn.com.duiba.pcg.service.biz.service.ItemFrontDataService;
import cn.com.duiba.pcg.service.biz.service.SupplierProductService;
import cn.com.duiba.pcg.service.biz.service.front.SupplyService;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategy;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategyRegistry;
import cn.com.duiba.pcg.service.biz.service.hsbc.HsbcService;
import cn.com.duiba.pcg.service.biz.service.impl.PlatFormCouponServiceImpl;
import cn.com.duiba.pcg.service.biz.util.CaffeineBuilder;
import cn.com.duiba.pcg.service.biz.util.Conditions;
import cn.com.duiba.pcg.service.biz.util.CookieUtil;
import cn.com.duiba.pcg.service.biz.util.ItemKeyUtil;
import cn.com.duiba.pcg.service.biz.util.LocalCacheUtil;
import cn.com.duiba.pcg.service.biz.util.duxiaoman.AESUtils;
import cn.com.duiba.pcg.service.biz.util.duxiaoman.RSAUtils;
import cn.com.duiba.pcg.service.biz.vo.oppo.OppoVO;
import cn.com.duiba.pcg.service.biz.vo.redpack.WxRedPackVO;
import cn.com.duiba.pcg.tool.AESCS7Util;
import cn.com.duiba.pcg.tool.HttpClientSSLUtil;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.enums.DuiBaSupplyOrdersStatusEnum;
import cn.com.duiba.supplier.center.api.enums.ErrorCodeEnums;
import cn.com.duiba.supplier.center.api.enums.PurchaseTypeEnum;
import cn.com.duiba.supplier.center.api.enums.SupplyTypeEnumUtil;
import cn.com.duiba.supplier.center.api.params.PhonebillParam;
import cn.com.duiba.supplier.center.api.params.WxCouponParam;
import cn.com.duiba.supplier.center.api.remoteservice.orders.RemoteOrderOperationRecordService;
import cn.com.duiba.supplier.center.api.remoteservice.supply.RemoteDuiBaSupplyOrderService;
import cn.com.duiba.supplier.center.api.request.order.DuiBaSupplyOrderPurchaseRequest;
import cn.com.duiba.supplier.center.api.request.order.PreOrderDrawRequest;
import cn.com.duiba.supplier.center.api.request.order.SupplyOrderInfoRequest;
import cn.com.duiba.supplier.center.api.request.order.WxRedpackOrderDrawRequest;
import cn.com.duiba.supplier.center.api.response.CgbPurchaseOrderResponse;
import cn.com.duiba.supplier.center.api.response.PurchaseOrderResponse;
import cn.com.duiba.thirdparty.enums.virtual.VirtualItemChannelEnum;
import cn.com.duiba.wechat.server.api.dto.WeAppDto;
import cn.com.duiba.wechat.server.api.remoteservice.RemoteWxThirdPartyService;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.redis.RedisAtomicClient;
import cn.com.duiba.wolf.redis.RedisLock;
import cn.com.duiba.wolf.utils.BlowfishUtils;
import cn.com.duiba.wolf.utils.ConcurrentUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.RandomUtil;

@Service
public class SupplyServiceImpl implements SupplyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SupplyServiceImpl.class);

    /**
     * 旧版本的app，可以允许实物下单时 不传 省、市、区字段
     * 15168	蜻蜓FM 正式
     * 44281	杜婷婷测试一
     * 54162	兑吧测试账号
     * 55901	张张兑吧项目测试
     * 61130	去哪儿
     * 61283	K米
     * 62456	组件化
     * 63217	苏宁金融会员兑换权益
     * 64441	黑街权益
     * 64513	苏宁金融APP
     * 65356	省点花
     * 65421	鸿泰
     * 65664	11111
     * 65749	账户+2.0营销（测试）
     * 65750	账户+2.0营销（生产）
     * 66067	应用宝
     * 66080	应用宝
     * 66240	block
     * 66368	小猿搜题
     * 66790	tk权益兑换测试
     * 69929	约惠圈
     */
    private static final Set<Long> OLD_VERSION_APP_IDS = Sets.newHashSet(15168L, 44281L, 54162L, 55901L, 61130L, 61283L, 62456L, 63217L, 64441L, 64513L, 65356L, 65421L, 65664L, 65749L, 65750L, 66067L, 66080L, 66240L, 66368L, 66790L, 69929L);
    @Autowired
    private DeveloperCacheService developerCacheService;

    @Autowired
    private RemoteHbConsisHashKvService hBaseService;

    @Autowired
    private XianDouConfig xianDouConfig;

    @Autowired
    private RemoteAuthorityService remoteAuthorityService;
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Autowired
    private DuXiaoManConfig duXiaoManConfig;

    @Autowired
    private RemoteAppService remoteAppService;

    @Autowired
    private GdCcbConfig gdCcbConfig;

    @Autowired
    private SupplyConfig supplyConfig;
    @Autowired
    private RemoteRepackCardService remoteRepackCardService;

    @Autowired
    private RemoteAlipayActivityConfigService remoteAlipayActivityConfigService;

    @Autowired
    private RemoteAppItemGoodsBackendService remoteAppItemGoodsBackendService;
    @Autowired
    private RemoteAppItemSkuService remoteAppItemSkuService;
    @Autowired
    private RemoteAppItemGoodsService remoteAppItemGoodsService;
    @Autowired
    private GoodsCacheService goodsCacheService;
    @Autowired
    private RemoteDuiBaSupplyOrderService remoteDuiBaSupplyOrderService;
    @Autowired
    private RemoteUniqueOrderCheckService remoteUniqueOrderCheckService;
    @Autowired
    private RemoteItemKeyService remoteItemKeyService;
    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
    @Autowired
    private ItemFrontDataService itemFrontDataService;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private CgbConfig cgbConfig;
    @Resource(name = "stringRedisTemplate")
    private RedisAtomicClient redisAtomicClient;

    @Resource
    private SszCcbConfig sszCcbConfig;
    @Autowired
    private RemoteAddrLimitService remoteAddrLimitService;
    @Autowired
    private GoodsItemDataService goodsItemDataService;
    @Autowired
    private RemoteItemAppSpecifyService remoteItemAppSpecifyService;

    @Autowired
    private RemoteDuibaItemGoodsService remoteDuibaItemGoodsService;

    @Autowired
    private RemoteHbConsisHashKvService remoteHbConsisHashKvService;
    @Autowired
    private RemotePhoneAreaService remotePhoneAreaService;
    @Autowired
    private GoodsItemActualPriceCalculateService goodsItemActualPriceCalculateService;
    @Autowired
    private SupplierProductService supplierProductService;
    @Autowired
    private ConsumerItemRenderService consumerItemRenderService;
    @Autowired
    private HsbcService hsbcService;
    @Autowired
    private VirtualExtrangeProcessor virtualExtrangeProcessor;
    @Autowired
    private RemoteItemNewExtraService remoteItemNewExtraService;
    @Autowired
    private RemoteSupplierGoodsService remoteSupplierGoodsService;
    @Autowired
    private NewMngConfig newMngConfig;
    @Autowired
    private HsbcConfig hsbcConfig;
    @Autowired
    private HzBankConfig hzBankConfig;
    @Resource
    private RemoteOrderOperationRecordService remoteOrderOperationRecordService;
    @Resource
    private RemoteItemSkuService remoteItemSkuService;
    @Autowired
    private ExecutorService executorService;

    @Autowired
    private MinShengConfig minShengConfig;
    @Autowired
    private JiangSuBocConfig jiangSuBocConfig;
    @Resource
    private RemoteWechatCouponService remoteWechatCouponService;
    @Resource
    private RemoteWxThirdPartyService remoteWxThirdPartyService;

    @Autowired
    private CcbHeadquartersConfig ccbHeadquartersConfig;

    @Autowired
    private ChongQingYouChuConfig chongQingYouChuConfig;

    @Resource
    private JinChengConfig jinChengConfig;

    @Resource
    private LtConfig ltConfig;

    @Autowired
    private HttpClientSSLUtil httpClientUtil;

    /**
     * 聚宝盆卡券状态本地缓存
     */
    private final LoadingCache<String, Integer> cornucopiaStatusCache = CaffeineBuilder.build(1000, 10, TimeUnit.MINUTES, 9, TimeUnit.MINUTES, this::cornucopiaCardStatus);

    private static final String cornucopia_url = "https://s.duiba.cn";
    private static final String cornucopia_test_url = "https://s.duibatest.com.cn";

    @Resource
    private Common2Config common2Config;

    //接口appKey，应用的唯一标识
    protected static final String APPKEY_KEY = "appKey";
    //1970-01-01开始的时间戳，毫秒为单位
    protected static final String TIMESTAMP_KEY = "timestamp";
    //签名的key
    protected static final String SIGN_KEY = "sign";

    private static final String XIAN_DOU_SUPPLY_PREFIX = "XIAN_DOU_SUPPLY_PREFIX_";

    /**
     * 采购业务白名单unicode
     */
    private static final String PURCHASE_WHITE_LIST_CODE = "CGJKBMD_zhangyongjie";

    /**
     * 预下单页面白名单
     */
    private static final String PRE_ORDER_BY_APPID = "YXDYMZSYY_yangyukang";

    /**
     * 优惠券限制code
     */
    private static final String COUPON_LIMIT_UNICODE = "CGJKYHQXDXZ_zhangyongjie";

    /**
     * 根据商品校验订单号
     */
    private static final String PURCHASE_ITEM_CHECK_ORDER_NO_CODE = "GJSPXYDDH_yangyukang";

    private static final String HBASE_LIMIT_KEY_PREFIX = "hbase_coupon_limit_prefix";

    private static final String RETRY = "retry";

    private static final String HUIFENG_CAX_GOODS_CODE = "601";

    private static final String HUIFENG_OTHER_GOODS_CODE = "602";

    private static final String HUIFENG_CAX_GOODS_KEY = "caxGoods";

    private final static String PRE_ORDER_WHITE_LIST = "CGYXDSP_yangyukang";

    private final static String APP_ITEM = "appItem";

    private final static String PURCHASE_REQUEST = "purchaseRequest";

    private final static String SKU = "sku";



    @Override
    public FrontItemSpuPageResponse querySpuPageByIds(FrontItemSpuPageRequest request, AppSimpleDto app) {
        // 对分页 添加五分钟缓存
        String ids;
        if (CollUtil.isEmpty(request.getIds())) {
            ids = "";
        } else {
            ids = request.getIds().toString();
        }
        String key = generateRedisKeyToHsbc(app.getId(), request.getPageNo(), request.getPageSize(), ids, request.getType(), request.getAppItemName());
        return advancedCacheClient.getWithCacheLoader(key, 5, TimeUnit.MINUTES, () -> hsbcService.getFrontItemSpuPageResponseNoCache(request, app));
    }

    @Override
    public FrontItemSpuPageResponse querySpuPage(FrontItemSpuPageRequest request, AppSimpleDto app) {
        // 对分页 添加五分钟缓存
        return advancedCacheClient.getWithCacheLoader(generateRedisKey(app.getId(), request.getPageNo(), request.getPageSize(), request.getIds(),request.getType(), request.getAppItemName()), 5, TimeUnit.MINUTES, () -> getFrontItemSpuPageResponseNoCache(request, app));
    }

    @Override
    public FrontItemSpuVO querySpuSingle(FrontItemSpuDetailRequest request, AppSimpleDto app) throws BizException {
        Long appItemId = request.getAppItemId();
        AppItemDto appItem = goodsCacheService.findAppItemById(appItemId);
        // 商品空判断
        Conditions.notNull(appItem, ErrorCode.E1100009);
        // 非兑吧商品无法查询
        Conditions.notNull(appItem.getItemId(), ErrorCode.E1100018);
        // 商品app归属判断
        Conditions.isTrue(Objects.equals(app.getId(), appItem.getAppId()), ErrorCode.E1100019);
        // 返回商品
        return advancedCacheClient.getWithCacheLoader(generateSpuDetailRedisKey(appItemId), 5, TimeUnit.MINUTES, () -> {
            try {
                return getFrontItemSpuPageResponseResult(request.getAppItemId(), app);
            } catch (Exception e) {
                // catch 所有异常认为查询失败  返回null值  允许击穿
                LOGGER.warn("查询采购商品详情出错 appItemId={}, appName={}, appId={}", appItemId, app.getName(), app.getId(), e);
                return null;
            }
        });
    }

    @NotNull
    private FrontItemSpuPageResponse getFrontItemSpuPageResponseNoCache(FrontItemSpuPageRequest request, AppSimpleDto app) {
        FrontItemSpuPageResponse response = new FrontItemSpuPageResponse();
        SupplyItemQueryParam param = new SupplyItemQueryParam();
        param.setAppId(app.getId());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setAppItemName(request.getAppItemName());
        param.setIds(request.getIds());
        //分组查询信息
        String type = request.getType();
        // 1.查询有效的itemId列表
        Page<Long> page;
        if (StringUtils.isBlank(type)) {
            page = remoteAppItemGoodsService.findValidIdsForSupply(param);
        } else {
            page = itemFrontDataService.getClassifyInfoFourSupply(param, type, request.getPageNo(), request.getPageSize());
        }
        response.setTotalNum(page.getTotalCount());
        List<Long> appItemIds = page.getList();
        if (CollectionUtils.isEmpty(appItemIds)) {
            return response;
        }
        // 2.并发封装返回数据
        // 根据id 封装执行任务
        List<Callable<FrontItemSpuVO>> tasks = appItemIds.stream()
                .map(id -> (Callable<FrontItemSpuVO>) () -> getFrontItemSpuPageResponseResult(id, app))
                .collect(Collectors.toList());
        List<FrontItemSpuVO> list = null;
        try {
            list = ConcurrentUtils.submitTasksBlocking(executorService, tasks);
        } catch (Exception e) {
            // catch所有异常 返回空列表
            LOGGER.warn("查询商品失败, appId={}", app.getId(), e);
        }
        response.setList(list);
        return response;
    }

    @Override
    public SupplyPurchaseResponse purchase(SupplyPurchaseRequest request, AppSimpleDto app) throws BizException {
        boolean isDxmFlag = isDuXiaoMan(request);
        if (isDxmFlag) {
            request = customParseRequest(request);
        }
        Pair<Triple<AppItemDto, DuiBaSupplyOrderPurchaseRequest, AppItemSkuDto>, SupplyPurchaseResponse> pair =
                checkAndConvertPurchaseRequest(request, app);
        SupplyPurchaseResponse supplyPurchaseResponse = pair.getRight();
        if(Objects.nonNull(supplyPurchaseResponse)) {
            return supplyPurchaseResponse;
        }
        Triple<AppItemDto, DuiBaSupplyOrderPurchaseRequest, AppItemSkuDto> triple = pair.getLeft();
        AppItemDto appItem = triple.getLeft();
        AppItemSkuDto sku = triple.getRight();
        DuiBaSupplyOrderPurchaseRequest r = triple.getMiddle();


        PurchaseOrderResponse response = remoteDuiBaSupplyOrderService.purchaseOrder(r);
        if (response instanceof CgbPurchaseOrderResponse) {
            CgbPurchaseOrderResponse response1 = (CgbPurchaseOrderResponse) response;
            if (response.isSuccess()) {
                CgbSupplyPurchaseResponse r2 = new CgbSupplyPurchaseResponse();
                r2.setOrderNum(String.valueOf(response1.getSupplyOrderNum()));
                r2.setCouponCode(response1.getCouponCode());
                r2.setStartDay(response1.getStartDay());
                r2.setEndDay(response1.getEndDay());
                return r2;
            }
        } else {
            if (response.isSuccess()) {
                //走到这里说明采购单下单成功，增加优惠券次数
                increaseCouponPurchaseCount(app.getId(), appItem);
                SupplyPurchaseResponse r2 = new SupplyPurchaseResponse();
                r2.setOrderNum(String.valueOf(response.getSupplyOrderNum()));
                // 保存一些快照扩展信息
                saveExtra(appItem,sku,app,response.getSupplyOrderNum());
                //定制下单后的逻辑
                customPurchaseAfterHandle(r, request, response.getSupplyOrderNum());
                return r2;
            }
        }
        throw new BizException(response.getResultMessage()).withCode(ErrorCode.********.getCode());
    }

     private Pair<Triple<AppItemDto, DuiBaSupplyOrderPurchaseRequest, AppItemSkuDto>, SupplyPurchaseResponse> checkAndConvertPurchaseRequest(SupplyPurchaseRequest request, AppSimpleDto app) throws BizException {
         //校验前置
         AppItemDto appItem = checkAndGetAppItem(request, app.getId());
         AppItemSkuDto sku = checkAndGetAppItemSku(request, appItem);
         PhonebillParam phonebillParam = checkAndGetPhonebillParam(request, appItem, app);
         WxCouponParam wxCouponParam = checkAndGetWxCouponParam(request);


         try (RedisLock lock = redisAtomicClient.getLock(getPurchaseKey(app,request), 3)) {
             if (lock == null) {
                 throw new GoodsWebException("请勿频繁操作");
             }
             //校验优惠券下单次数（当天）
             checkCouponPurchaseCount(app.getId(), appItem);
             DuiBaSupplyOrderPurchaseRequest r = new DuiBaSupplyOrderPurchaseRequest();
             r.setAppItem(appItem);
             r.setAppItemSku(sku);
             r.setAppId(app.getId());
             r.setAppName(app.getName());
             r.setPhone(request.getPhone());
             r.setAddress(request.getAddress());
             r.setName(request.getName());
             r.setThirdOrderNum(request.getThirdOrderNum());
             r.setDeveloperId(app.getDeveloperId());
             r.setProvince(request.getProvince());
             r.setCity(request.getCity());
             r.setDistrict(request.getDistrict());
             r.setStreet(request.getStreet());
             r.setPhonebillParam(phonebillParam);
             r.setAccount(request.getAccount());
             r.setWxCouponParam(wxCouponParam);
             r.setAmount(request.getAmount());
             r.setPartnerUserId(request.getPartnerUserId());
             r.setExtraInfo(request.getExtraInfo());
             //定制处理
             customPurchaseHandle(r, request);
             // 充值商品，商品账号格式限制校验,根据配置决定是否过滤空格
             if (StringUtils.isNotEmpty(r.getAccount()) && Objects.equals(appItem.getType(), ItemDto.TypeVirtual)) {
                 String account = virtualExtrangeProcessor.checkAccountFormat(Optional.of(appItem).map(AppItemDto::getId).orElse(null), null, r.getAccount());
                 if (StringUtils.isEmpty(account)) {
                     throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
                 }
                 r.setAccount(account);
             }
             //是否预下单
             r.setPreOrder(checkPreOrder(request.getAppItemId()));

             //请求唯一性判断
             Boolean dubboResult = getUniqueOrderCheck(request, app);
             if (!dubboResult) {
                 // 江苏中行幂等定制
                 if (jiangSuBocConfig.getAppId().equals(app.getId())){
                     return Pair.of(null, getJiangsuSupplyPurchaseResponse(request, app));
                 }
                 if (cgbConfig.getAppId().equals(app.getId())) {
                     //针对广发中心重复发送订单情况，需要回查优惠券
                     return Pair.of(null, getCgbSupplyPurchaseResponse(request, app));
                 }
                 if (hzBankConfig.getAppId().contains(app.getId())) {
                     Pair<Boolean, SupplyPurchaseResponse> pair = checkRepeatOrder(request, app);
                     if (pair.getLeft()) {
                         LOGGER.info("杭州银行-采购响应，appId={}，thirdOrderNum={}，response={}", app.getId(), request.getThirdOrderNum(), JSON.toJSONString(pair.getRight()));
                         return Pair.of(null, pair.getRight());
                     }
                 }
                 if (gdCcbConfig.getAppId().equals(app.getId())) {
                     return Pair.of(null, getSupplyPurchaseResponse(request, app));
                 }
                 // 山西建行幂等定制（返回订单号）
                 if (Objects.equals(app.getId(), sszCcbConfig.getAppId())) {
                     return Pair.of(null, getsxjhSupplyPurchaseResponse(request, app));
                 } else {
                     throw new BizException(ErrorCode.E1100004.getDesc()).withCode(ErrorCode.E1100004.getCode());
                 }
             }
             return Pair.of(Triple.of(appItem, r, sku), null);
         } catch (BizException e){
             LOGGER.error("purchase error",e);
             throw e;
         } catch (Exception e){
             LOGGER.error("purchase error",e);
             throw new BizException(ErrorCode.E2002001.getDesc()).withCode(ErrorCode.E2002001.getCode());
         }
     }


    private void saveExtra(AppItemDto appItem, AppItemSkuDto sku, AppSimpleDto app,Long supplyOrderNum) {
        try {
            Long salePrice = null;
            Long costPrice = null;
            String channelName = null;
            // 根据skuId找到itemSkuId
            if (sku == null){
                return;
            }
            ItemSkuDto itemSkuDto = remoteItemSkuService.findSkuById(sku.getItemSkuId());
            if (itemSkuDto == null) {
                return;
            }
            salePrice = itemSkuDto.getSalePrice();
            costPrice  = itemSkuDto.getCostPrice();
            // 根据itemSkuId找到item（售价、成本价）
            ItemKeyDto itemKey = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItem.getId(), appItem.getItemId(), app);

            // 实物
            if (Objects.equals(appItem.getType(), ItemDto.TypeObject)) {
                if (itemKey.getItem() != null && itemKey.getItem().getItemExtraDto() != null ) {
                    ItemExtraDto itemExtra = itemKey.getItem().getItemExtraDto();
                    if(itemExtra.getSupplierId()!=null){
                        String supplierName = SupplyTypeEnumUtil.getSupplierNameInfoById(itemExtra.getSupplierId().toString());
                        channelName = supplierName;
                    }
                }
            }
            // 虚拟商品
            if (Objects.equals(appItem.getType(), ItemDto.TypeVirtual)) {
                if(itemKey.getItem() != null){
                    channelName = Optional.ofNullable(VirtualItemChannelEnum.getByCode(itemKey.getItem().getMerchantCoding())).map(VirtualItemChannelEnum::getDesc).orElse(null);
                }
            }
            // 保存到供应商订单
            DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findBySupplyOrderNum(supplyOrderNum);
            if (duiBaSupplyOrdersDto != null){
                String expressInfo = duiBaSupplyOrdersDto.getExpressInfo();
                JSONObject expressInfoJson = Optional.ofNullable(JSONObject.parseObject(expressInfo)).orElse(new JSONObject());
                JSONObject extraInfoJson = Optional.ofNullable(JSONObject.parseObject(duiBaSupplyOrdersDto.getExtraInfo())).orElse(new JSONObject());
                expressInfoJson.put("channelName",channelName);
                expressInfoJson.put("salePrice",salePrice);
                expressInfoJson.put("costPrice",costPrice);
                expressInfoJson.putAll(extraInfoJson);
                SupplyOrderInfoRequest request = new SupplyOrderInfoRequest();
                request.setSupplyOrderNum(supplyOrderNum);
                request.setExtraInfo(JSON.toJSONString(expressInfoJson));
                remoteDuiBaSupplyOrderService.updateOrderInfo(request);
            }
        }catch (Exception e){
            LOGGER.warn("保存商品信息失败 supplyOrderNum={}",supplyOrderNum,e);
        }
    }


    /**
     * 预下单校验
     * @param appItemId
     * @return
     */
    @Override
    public boolean checkPreOrder(Long appItemId) {
        Map<String, String> preOrderAppItemJson = getPreOrderAppItemJson();
        if(Objects.isNull(preOrderAppItemJson) || preOrderAppItemJson.isEmpty()) {
            return false;
        }
        String appItemIdStr = String.valueOf(appItemId);
        boolean result = false;
        for (String key : preOrderAppItemJson.keySet()) {
            if (key.contains(appItemIdStr)) {
                result = true;
                break;
            }
        }
        LOGGER.info("获取白名单信息为空678,appItemStr={}, appItemId={}, result={}", appItemIdStr, appItemId, result);
        return result;
    }

    private Map<String, String> getPreOrderAppItemJson() {
        //获取白名单信息
        String jsonConfig = WhiteAccessUtil.selectWhiteListJsonConfig(PRE_ORDER_WHITE_LIST);
        if(StringUtils.isBlank(jsonConfig)){
            return null;
        }
        Map<String, String> appItemMaps = null;
        try {
            appItemMaps = JSON.parseObject(jsonConfig, new TypeReference<HashMap<String, String>>() {});
            if(appItemMaps.isEmpty()) {
                return appItemMaps;
            }
        } catch (Exception e) {
            LOGGER.info("预下单获取白名单解析json异常", e);
        }
        return appItemMaps;
    }

    /**
     * 预下单-订单详情
     * @param orderRealNum
     * @return
     * @throws BizException
     */
    @Override
    public PreOrderResponse queryOrderPreInfo(String orderRealNum) throws BizException {
        //查询采购订单Dto
        Pair<DuiBaSupplyOrdersDto, ItemDescConfigDto> pair = querySupplyAndItemDesc(orderRealNum, "预下单订单详情");
        ItemDescConfigDto itemDescConfigDto = pair.getRight();
        DuiBaSupplyOrdersDto supplyOrdersDto = pair.getLeft();
        ItemDto itemDto = goodsCacheService.findItemById(itemDescConfigDto.getItemId());
        if(Objects.isNull(itemDto)){
            LOGGER.warn("[预下单订单详情] itemDto商品为空 = {}", orderRealNum);
            throw new BizException(ErrorCodeEnums.RC_01018.getMessage()).withCode(ErrorCodeEnums.RC_01018.getCode());
        }
        ItemSkuDto itemSkuDto = goodsCacheService.findSkuByItemId(itemDescConfigDto.getItemId());
        if(Objects.isNull(itemSkuDto)){
            LOGGER.warn("[预下单订单详情] itemDto商品为空 = {}", orderRealNum);
            throw new BizException(ErrorCodeEnums.RC_01018.getMessage()).withCode(ErrorCodeEnums.RC_01018.getCode());
        }
        Map<Long, Integer> appItemStockMap = getAppItemStockMap(Collections.singletonList(supplyOrdersDto.getAppItemId()), supplyOrdersDto.getAppId());
        Integer stock = appItemStockMap.get(supplyOrdersDto.getAppItemId());
        //批次信息
        WxCouponQueryResp wxCouponQueryResp = getWxCouponQueryResp(itemDto.getMerchantCoding(), itemSkuDto.getMerchantCoding(), null);
        LOGGER.info("[预下单订单详情] 批次信息 = {},orderRealNum={}", JSON.toJSONString(wxCouponQueryResp), orderRealNum);
        PreOrderResponse preOrderResponse = new PreOrderResponse();
        preOrderResponse.setDesc(itemDescConfigDto.getDescription());
//        DateTime endTime = cn.hutool.core.date.DateUtil.parse(wxCouponQueryResp.getAvailableEndTime(), "yyyy-MM-dd'T'HH:mm:ssXXX");
//        DateTime beginTime = cn.hutool.core.date.DateUtil.parse(wxCouponQueryResp.getAvailableBeginTime(), "yyyy-MM-dd'T'HH:mm:ssXXX");
//
//        preOrderResponse.setAvailableBeginTime(beginTime.toString());
//        preOrderResponse.setAvailableEndTime(endTime.toString());
        preOrderResponse.setCouponAmount(UnitUtils.transformFen2Yuan(wxCouponQueryResp.getStockUseRule().getFixedNormalCoupon().getCouponAmount()));
        preOrderResponse.setTransactionMinimum(UnitUtils.transformFen2Yuan(wxCouponQueryResp.getStockUseRule().getFixedNormalCoupon().getTransactionMinimum()));
        preOrderResponse.setStatus(getPreOrderStatus(supplyOrdersDto.getOrderStatus(), stock));
        preOrderResponse.setThirdOrderId(supplyOrdersDto.getThirdOrderNum());
        return preOrderResponse;
    }

    private Pair<DuiBaSupplyOrdersDto, ItemDescConfigDto> querySupplyAndItemDesc(String orderRealNum, String logPrefix) throws BizException {
        //查询采购订单Dto
        DuiBaSupplyOrdersDto supplyOrdersDto = remoteDuiBaSupplyOrderService.findBySupplyOrderNum(Long.valueOf(orderRealNum));
        if(Objects.isNull(supplyOrdersDto)){
            LOGGER.warn("[{}] orderNum 获取supplyOrdersDto 为空 = {}", logPrefix, orderRealNum);
            throw new BizException(ErrorCodeEnums.RC_01046.getMessage()).withCode(ErrorCodeEnums.RC_01046.getCode());
        }
        AppItemDto appItem = goodsCacheService.findAppItemById(supplyOrdersDto.getAppItemId());
        if(Objects.isNull(appItem)){
            LOGGER.warn("[{}] appItem商品为空 = {}", logPrefix, orderRealNum);
            throw new BizException(ErrorCodeEnums.RC_01018.getMessage()).withCode(ErrorCodeEnums.RC_01018.getCode());
        }
        ItemDescConfigDto itemDescConfigDto = goodsCacheService.findItemDescByItemId(appItem.getItemId());
        if(Objects.isNull(itemDescConfigDto)){
            LOGGER.warn("[{}] 商品为空 = {}", logPrefix, orderRealNum);
            throw new BizException(ErrorCodeEnums.RC_01018.getMessage()).withCode(ErrorCodeEnums.RC_01018.getCode());
        }
        return Pair.of(supplyOrdersDto, itemDescConfigDto);
    }

    @NotNull
    @Override
    public WxCouponQueryResp getWxCouponQueryResp(String subject, String stockId, String createMchId) {
        String key = RedisKeyFactory.K410.join(subject, stockId);
        return advancedCacheClient.getWithCacheLoader(key, 1, TimeUnit.HOURS, () -> {
            WxCouponQueryRequest wxCouponQueryRequest = new WxCouponQueryRequest();
            wxCouponQueryRequest.setStockId(stockId);
            wxCouponQueryRequest.setActSubject(subject);
            wxCouponQueryRequest.setCreateMchId(createMchId);
            return remoteWechatCouponService.getWechatActivityInfo(wxCouponQueryRequest);
        });
    }

    @Override
    public DuiBaSupplyOrdersDto queryOrderInfo(String thirdOrderId, Long appId) {
        return remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(thirdOrderId, appId);
    }

    private Map<Long, Integer> getAppItemStockMap(List<Long> appItems, Long appId) throws BizException {
        List<AppItemSkuRemainingParam> skuStock = remoteAppItemGoodsService.findRemainingByAppItemIdsAndAppId(appItems, appId);
        if(CollectionUtils.isEmpty(skuStock)) {
            throw new BizException(ErrorCodeEnums.RC_01018.getMessage()).withCode(ErrorCodeEnums.RC_01018.getCode());
        }
        return skuStock.stream().collect(Collectors.toMap(AppItemSkuRemainingParam::getAppItemId, AppItemSkuRemainingParam::getRemaining, (n,o)->n));
    }



    //获取预下单订单状态
    private Integer getPreOrderStatus(Integer orderStatus, Integer stock) {
        if(Objects.equals(orderStatus, DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode())) {
            return PreOrderStatusEnum.ALREADY_RECEIVE.getStauts();
        }
        if(orderStatus > DuiBaSupplyOrdersStatusEnum.PRE.getCode()) {
            return PreOrderStatusEnum.RECEIVEING.getStauts();
        }
        if(stock <= 0) {
            return PreOrderStatusEnum.NO_STOCK.getStauts();
        }
        return PreOrderStatusEnum.CAN_RECEIVE.getStauts();
    }

    /**
     * 预下单-领取
     * @param preOrderDrawParam
     * @return
     * @throws BizException
     */
    @Override
    public boolean preOrderDraw(PreOrderDrawParam preOrderDrawParam) throws BizException {
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findBySupplyOrderNum(preOrderDrawParam.getOrderRealNum());
        if (Objects.isNull(duiBaSupplyOrdersDto)) {
            throw new BizException(ErrorCode.E3002002.getDesc()).withCode(ErrorCode.E3002002.getCode());
        }
        Map<String, String> preOrderAppItemJson = getPreOrderAppItemJson();
        if(Objects.isNull(preOrderAppItemJson) || preOrderAppItemJson.isEmpty()) {
            throw new BizException(ErrorCode.E3002003.getDesc()).withCode(ErrorCode.E3002003.getCode());
        }
        String jsonStr = preOrderAppItemJson.get(duiBaSupplyOrdersDto.getAppItemId().toString());
        if(StringUtils.isBlank(jsonStr)) {
            throw new BizException(ErrorCode.E3002003.getDesc()).withCode(ErrorCode.E3002003.getCode());
        }
        JSONObject appItemJson = JSONObject.parseObject(jsonStr);
        if(Objects.isNull(appItemJson) || appItemJson.isEmpty()) {
            throw new BizException(ErrorCode.E3002003.getDesc()).withCode(ErrorCode.E3002003.getCode());
        }
        Long appItemId = appItemJson.getLong(preOrderDrawParam.getCardType());
        if(Objects.isNull(appItemId)) {
            throw new BizException(ErrorCode.E3002003.getDesc()).withCode(ErrorCode.E3002003.getCode());
        }
        Map<Long, Integer> appItemStockMap = getAppItemStockMap(Collections.singletonList(appItemId), duiBaSupplyOrdersDto.getAppId());
        Integer stock = appItemStockMap.get(appItemId);
        if(stock <= 0) {
            throw new BizException(ErrorCode.E3002004.getDesc()).withCode(ErrorCode.E3002004.getCode());
        }
        if(!Objects.equals(DuiBaSupplyOrdersStatusEnum.PRE.getCode(), duiBaSupplyOrdersDto.getOrderStatus())) {
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        PreOrderDrawRequest preOrderDrawRequest = new PreOrderDrawRequest();
        preOrderDrawRequest.setAccount(preOrderDrawParam.getAccount());
        preOrderDrawRequest.setAppItemId(appItemId);
        preOrderDrawRequest.setSupplyOrderNum(preOrderDrawParam.getOrderRealNum());
        return remoteDuiBaSupplyOrderService.preOrderDraw(preOrderDrawRequest);
    }

    @Override
    public String converPreOrderlink(String orderNum, Long appId, String source) {
        //通用链接页
        String supplyOrderPreUrl = StringUtils.EMPTY;
        //获取白名单里面的配置
        String str = WhiteAccessUtil.selectWhiteListJsonConfig(PRE_ORDER_BY_APPID);
        JSONObject jsonObject = JSONObject.parseObject(str);
        if(Objects.nonNull(jsonObject) && !jsonObject.isEmpty()) {
            String url = "";
            if(appId != null) {
                url = jsonObject.getString(appId.toString());
            }
            if(StringUtils.isNotBlank(source)) {
                url = jsonObject.getString(source);
            }
            if(StringUtils.isNotBlank(url)) {
                supplyOrderPreUrl = url;
            }
        }
        //广东建行卡部
        if (Objects.equals(appId, gdCcbConfig.getAppId()) && StringUtils.isBlank(supplyOrderPreUrl)) {
            supplyOrderPreUrl = gdCcbConfig.getCouponUrl();
        }
        //广东建行网金定制
        if (Objects.equals(appId, ccbHeadquartersConfig.getAppId()) && StringUtils.isBlank(supplyOrderPreUrl)) {
            supplyOrderPreUrl = commonConfig.getSupplyOrderPreUrl();
        }
        String url = supplyOrderPreUrl + BlowfishUtils.encryptBlowfish(orderNum + RandomUtil.randomString(5), commonConfig.getSupplyOrderPreBlowfishKey());
        LOGGER.info("返回预下单领取链接,appId={}, supplyOrderPreUrl={},orderNum={}", appId, url, orderNum);
        return url;
    }

    @Override
    public WxRedPackVO queryWxRedPackOrderInfo(String orderRealNum) throws BizException {
        Pair<DuiBaSupplyOrdersDto, ItemDescConfigDto> pair = querySupplyAndItemDesc(orderRealNum, "微信红包订单详情");
        ItemDescConfigDto itemDescConfigDto = pair.getRight();
        DuiBaSupplyOrdersDto supplyOrdersDto = pair.getLeft();
        WxRedPackVO wxRedPackVO = new WxRedPackVO();
        wxRedPackVO.setFailreason(supplyOrdersDto.getFailReason());
        wxRedPackVO.setPrice(queryRedPackAmount(supplyOrdersDto));
        wxRedPackVO.setRule(itemDescConfigDto.getDescription());
        wxRedPackVO.setStatus(getRedPackStatus(supplyOrdersDto.getOrderStatus()));
        wxRedPackVO.setOrderNum(orderRealNum);
        return wxRedPackVO;
    }

    private Integer getRedPackStatus(Integer orderStatus) {
        if(Objects.equals(orderStatus, DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode())) {
            return PreOrderStatusEnum.ALREADY_RECEIVE.getStauts();
        }
        if(orderStatus > DuiBaSupplyOrdersStatusEnum.CREATE.getCode()) {
            return PreOrderStatusEnum.RECEIVEING.getStauts();
        }
        return PreOrderStatusEnum.CAN_RECEIVE.getStauts();
    }

    private String queryRedPackAmount (DuiBaSupplyOrdersDto supplyOrdersDto) {
        String extraInfo = supplyOrdersDto.getExpressInfo();
        JSONObject jsonObject = JSONObject.parseObject(extraInfo);
        Long price = jsonObject.getLong(DuiBaSupplyOrdersDto.EXPRESS_INFO_CHARGE_AMOUNT);
        return new DecimalFormat("#0.00").format(price / 100.0);
    }

    @Override
    public String getRealOrderNum(String orderNum) throws BizException {
        String orderRealNum = BlowfishUtils.decryptBlowfish(orderNum, commonConfig.getSupplyOrderPreBlowfishKey());
        if(StringUtils.isBlank(orderRealNum)) {
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        orderRealNum = orderRealNum.substring(0, orderRealNum.length() - 5);
        LOGGER.info("[查询真实订单号] ,orderNum={},orderRealNum={}", orderNum, orderRealNum);
        return orderRealNum;
    }

    @Override
    public boolean wxRedPackDraw(WxRedPackOrderDrawParam param) throws BizException {
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findBySupplyOrderNum(param.getOrderRealNum());
        if (Objects.isNull(duiBaSupplyOrdersDto)) {
            throw new BizException(ErrorCode.E3002002.getDesc()).withCode(ErrorCode.E3002002.getCode());
        }
        if(!Objects.equals(DuiBaSupplyOrdersStatusEnum.CREATE.getCode(), duiBaSupplyOrdersDto.getOrderStatus())) {
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        WxRedpackOrderDrawRequest wxRedpackOrderDrawRequest = new WxRedpackOrderDrawRequest();
        wxRedpackOrderDrawRequest.setAccount(param.getAccount());
        wxRedpackOrderDrawRequest.setSupplyOrderNum(param.getOrderRealNum());
        return remoteDuiBaSupplyOrderService.wxOrderDraw(wxRedpackOrderDrawRequest);
    }

    @Override
    public Triple<ErrorCode, SupplyPurchaseResponse, Long> purchaseHandle(SupplyPurchaseRequest request, HttpServletRequest httpRequest) throws BizException {
        Pair<ErrorCode, AppSimpleDto> purchase = purchase(request, httpRequest);
        ErrorCode errorCode = purchase.getLeft();
        if(Objects.nonNull(errorCode)) {
            return Triple.of(errorCode, null, null);
        }
        AppSimpleDto app = purchase.getRight();
        SupplyPurchaseResponse response = purchase(request, app);
        return Triple.of(null, response, app.getId());
    }

    private Pair<ErrorCode, AppSimpleDto> purchase(SupplyPurchaseRequest request, HttpServletRequest httpRequest) throws BizException {
        boolean isDxmFlag = isDuXiaoMan(request);
        if (isDxmFlag) {
            request = customParseRequest(request);
        }
        if (request.getSkuId() == null) {
            return Pair.of(ErrorCode.********, null);
        }
        if (StringUtils.isBlank(request.getThirdOrderNum())) {
            return Pair.of(ErrorCode.********, null);
        }
        // 查询app信息
        //处理定制逻辑
        handleCustomLogic(request);
        AppSimpleDto app;
        if (isDxmFlag) {
            app = checkSignAndGetApp(request);
        } else if (request.getJinchengAppId()!=null && jinChengConfig.getAppIds().contains(request.getJinchengAppId())){
            app = remoteAppService.getSimpleApp(request.getJinchengAppId()).getResult();
        }else if (request.getLtAppId()!=null && ltConfig.getClientId().equals(request.getLtAppId())) {
            app = remoteAppService.getSimpleApp(request.getLtAppId()).getResult();
        } else if (request.getGdCcbAppId()!=null){
            app = remoteAppService.getSimpleApp(request.getGdCcbAppId()).getResult();
        } else {
            app = checkSignAndGetApp(request,httpRequest, request.getSkipSign());
        }
        //如果 skuid 不等于 0  客户可以不传 appitemid  通过 skuid 反查出 appitem 进行赋值

        if(request.getAppItemId() == null ){
            if(request.getSkuId() == 0){
                LOGGER.info("appitem={} skuid = {}",request.getAppItemId(),request.getSkuId());
                return Pair.of(ErrorCode.E1100005, null);
            }
            AppItemSkuDto skuDto = remoteAppItemSkuService.findSkuById(request.getSkuId());
            if(skuDto == null){
                LOGGER.info("skuDto=null error skuid = {}",request.getSkuId());
                return Pair.of(ErrorCode.********, null);
            }
            request.setAppItemId(skuDto.getAppItemId());
            LOGGER.info("根据skuid 反查 appitem skuid = {},appitem = {}",request.getSkuId(),skuDto.getAppItemId());
        }

        //收集调用采购接口的appId信息
        collectAppIdInfo(app.getId());

        //校验app白名单权限
        checkPermission(app.getId());
        //地域限制校验
        verifyAreaLimit(request, app);
        handleCustomLogic(request, app);
        return Pair.of(null, app);
    }

    @Override
    public Triple<ErrorCode, SupplyPurchaseResponse, Long> syncPurchaseHandle(SupplyPurchaseRequest request, HttpServletRequest httpRequest) throws BizException {
        // 查询app信息
        String appKey = request.getAppKey();
        AppSimpleDto app = developerCacheService.getAppByAppKey(appKey);
        Triple<ErrorCode, SupplyPurchaseResponse, Long> triple = checkOrderExist(request);
        if (triple != null) {
            return triple;
        }
        Pair<ErrorCode, AppSimpleDto> purchase = purchase(request, httpRequest);
        ErrorCode errorCode = purchase.getLeft();
        if(Objects.nonNull(errorCode)) {
            return Triple.of(errorCode, null, null);
        }
        SupplyPurchaseResponse response = syncPurchase(request, app);
        return Triple.of(null, response, app.getId());
    }

    @Nullable
    @Override
    public Triple<ErrorCode, SupplyPurchaseResponse, Long> checkOrderExist(SupplyPurchaseRequest request) throws BizException {
        String appKey = request.getAppKey();
        AppSimpleDto app = developerCacheService.getAppByAppKey(appKey);
        //如果订单已经存在，直接return
        DuiBaSupplyOrdersDto ordersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
        if (Objects.isNull(ordersDto)) {
            return null;
        }
        SupplyPurchaseResponse response = new SupplyPurchaseResponse();
        response.setOrderNum(String.valueOf(ordersDto.getSupplyOrderNum()));
        if(DuiBaSupplyOrdersStatusEnum.FAIL.getCode().equals(ordersDto.getOrderStatus())) {
            throw new BizException(ordersDto.getFailReason()).withCode(ErrorCode.E1001010.getCode());
        }
        return Triple.of(null, response, app.getId());
    }

    @Override
    public SupplyPurchaseResponse syncPurchase(SupplyPurchaseRequest request, AppSimpleDto app) throws BizException {
        Pair<Triple<AppItemDto, DuiBaSupplyOrderPurchaseRequest, AppItemSkuDto>, SupplyPurchaseResponse> pair = checkAndConvertPurchaseRequest(request, app);
        SupplyPurchaseResponse supplyPurchaseResponse = pair.getRight();
        if(Objects.nonNull(supplyPurchaseResponse)) {
            return supplyPurchaseResponse;
        }
        Triple<AppItemDto, DuiBaSupplyOrderPurchaseRequest, AppItemSkuDto> triple = pair.getLeft();
        AppItemDto appItem = triple.getLeft();
        AppItemSkuDto sku = triple.getRight();
        DuiBaSupplyOrderPurchaseRequest r = triple.getMiddle();

        PurchaseOrderResponse response = remoteDuiBaSupplyOrderService.syncPurchaseOrder(r);
        if (response.isSuccess()) {
            //走到这里说明采购单下单成功，增加优惠券次数
            increaseCouponPurchaseCount(app.getId(), appItem);
            SupplyPurchaseResponse r2 = new SupplyPurchaseResponse();
            r2.setOrderNum(String.valueOf(response.getSupplyOrderNum()));
            // 保存一些快照扩展信息
            saveExtra(appItem,sku,app,response.getSupplyOrderNum());
            //定制下单后的逻辑
            customPurchaseAfterHandle(r, request, response.getSupplyOrderNum());
            return r2;
        }
        throw new BizException(response.getResultMessage()).withCode(ErrorCode.********.getCode());
    }

    @Override
    public String getCookieOpenId (String appkey, HttpServletRequest request) throws BizException {
        WeAppDto weAppDto = remoteWxThirdPartyService.getByAppKey(appkey);
        if(Objects.isNull(weAppDto)) {
            LOGGER.warn("查询微信授权信息异常 appkey={}", appkey);
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        String openId = CookieUtil.getCookieForOpenId(request, weAppDto.getWechatAppid(), commonConfig.getCookieEncryptKey());
        LOGGER.info("微信授权获取openId={}, weAppDto={}", openId, JSON.toJSONString(weAppDto));
        if(StringUtils.isBlank(openId)) {
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        return openId;
    }

    /**
     * 民生权益采购
     *
     * @param app
     * @param msPurchaseDTO
     * @return
     * @throws BizException
     */
    @Override
    public SupplyPurchaseResponse minShengPurchase(AppSimpleDto app, MsPurchaseDTO msPurchaseDTO) throws BizException {
        //已下单的商品 对方通过下单接口来重试
        DuiBaSupplyOrdersDto mainOrder = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(msPurchaseDTO.getThirdOrderNum(), app.getId());
        if (mainOrder != null) {
            LOGGER.info("{}, 订单号开始重试, thirdOrderNum:[{}]", MinShengConfig.PROJECT_NAME, msPurchaseDTO.getThirdOrderNum());
            remoteDuiBaSupplyOrderService.minShengRetry(mainOrder.getSupplyOrderNum());
            SupplyPurchaseResponse supplyPurchaseResponse = new SupplyPurchaseResponse();
            supplyPurchaseResponse.setOrderNum(String.valueOf(mainOrder.getSupplyOrderNum()));
            return supplyPurchaseResponse;
        }

        //如果主子订单有多个商品类型 校验一下商品
        List<MinShengPrizeRuleNoConfig> allPrizeType = minShengConfig.getPrizeConfigByRuleNo(msPurchaseDTO.getRuleNo());
        for (MinShengPrizeRuleNoConfig prizeType : allPrizeType) {
            SupplyPurchaseRequest request = new SupplyPurchaseRequest();
            request.setAppItemId(prizeType.getAppItemId());
            request.setSkuId(prizeType.getSkuId());
            request.setThirdOrderNum(msPurchaseDTO.getThirdOrderNum());
            request.setAccount(msPurchaseDTO.getAccount());
            //校验商品
            AppItemDto appItem = checkAndGetAppItem(request, app.getId());
            //校验商品sku
            checkAndGetAppItemSku(request, appItem);
        }

        //同一个类型的奖品可能发多个  拆成每一个
        List<PrizeRuleNoConfig> allPrizeOrder = new ArrayList<>();
        for (MinShengPrizeRuleNoConfig prizeConfig : allPrizeType) {
            for (Integer i = 0; i < prizeConfig.getSendNum(); i++) {
                PrizeRuleNoConfig prizeRuleNoConfig = new PrizeRuleNoConfig();
                prizeRuleNoConfig.setAppItemId(prizeConfig.getAppItemId());
                prizeRuleNoConfig.setAppItemSkuId(prizeConfig.getSkuId());
                prizeRuleNoConfig.setRuleNo(prizeConfig.getRuleNo());
                allPrizeOrder.add(prizeRuleNoConfig);
            }
        }
        //主订单
        PrizeRuleNoConfig mainOrderPrize = allPrizeOrder.get(0);
        //子订单(2~n以子订单下单的方式)
        List<PrizeRuleNoConfig> subOrderPrize = allPrizeOrder.subList(1, allPrizeOrder.size());
        Pair<PrizeRuleNoConfig, List<PrizeRuleNoConfig>> fullOrderInfo = Pair.of(mainOrderPrize, subOrderPrize);

        //主订单下单(有定制子订单下单逻辑)
        SupplyPurchaseRequest mainOrderReq = new SupplyPurchaseRequest();
        mainOrderReq.setFullOrderInfo(fullOrderInfo);
        mainOrderReq.setAppItemId(mainOrderPrize.getAppItemId());
        mainOrderReq.setSkuId(mainOrderPrize.getAppItemSkuId());
        mainOrderReq.setThirdOrderNum(msPurchaseDTO.getThirdOrderNum());
        mainOrderReq.setAccount(msPurchaseDTO.getAccount());
        SupplyPurchaseResponse response = purchase(mainOrderReq, app);
        return response;
    }

    //定制下单时的逻辑
    private void customPurchaseHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request) {
        ExtraStrategy route = ExtraStrategyRegistry.route(r.getAppId());
        if (Objects.isNull(route)) {
            return;
        }
        route.customPurchaseHandle(r, request);
    }

    //定制下单后的逻辑
    private void customPurchaseAfterHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request, Long supplyOrderNum) {
        LOGGER.info("进入定制逻辑,supplyOrderNum={},r={},request={}", supplyOrderNum, JSON.toJSONString(r), JSON.toJSONString(request));
        ExtraStrategy route = ExtraStrategyRegistry.route(r.getAppId());
        if (Objects.isNull(route)) {
            return;
        }
        route.customPurchaseAfterHandle(r, request, supplyOrderNum);
    }

    private Boolean getUniqueOrderCheck(SupplyPurchaseRequest request, AppSimpleDto app) {
        UniqueOrderCheckDto uoc = new UniqueOrderCheckDto(true);
        uoc.setAppId(app.getId());
        uoc.setDeveloperBizId(app.getId() + "_" + request.getThirdOrderNum());
        uoc.setSource("duiba-supply-order");
        DubboResult<UniqueOrderCheckDto> dubboResult = remoteUniqueOrderCheckService.insert(uoc);
        return dubboResult.isSuccess();
    }
    @Override
    public WxCouponParam checkAndGetWxCouponParam(SupplyPurchaseRequest request) {
        if (request.getWxCouponValue() == null || request.getWxCouponMinimum() == null) {
            return null;
        }
        WxCouponParam wxCouponParam = new WxCouponParam();
        wxCouponParam.setOpenId(request.getAccount());
        wxCouponParam.setWxCouponValue(request.getWxCouponValue());
        wxCouponParam.setWxCouponMinimum(request.getWxCouponMinimum());
        return wxCouponParam;
    }

    private PhonebillParam checkAndGetPhonebillParam(SupplyPurchaseRequest request, AppItemDto appItem, AppSimpleDto app) throws BizException {
        //非话费商品，无需校验
        if (!StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebill)
                && !StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebillDingzhi)) {
            return null;
        }
        if (StringUtils.isBlank(request.getPhone())) {
            throw new BizException(ErrorCode.E1100027.getDesc()).withCode(ErrorCode.E1100027.getCode());
        }
        //多档位话费校验
        if (StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebill)) {
            if (request.getPhoneDegree() == null) {
                throw new BizException(ErrorCode.E1100026.getDesc()).withCode(ErrorCode.E1100026.getCode());
            }
            PriceDegreeDto pd = new PriceDegreeDto(appItem.getCustomPrice());
            Map<String, Map<String, String>> map = PlatFormCouponServiceImpl.parseDegreeCredits(pd, app);
            //请求参数中单位是分，多档位在数据库中是元。
            String key = String.valueOf(request.getPhoneDegree() / 100);
            Map<String, String> value = map.get(key);
            if (value == null) {
                throw new BizException(ErrorCode.E1100032.getDesc()).withCode(ErrorCode.E1100032.getCode());
            }
        }
        //获取话费商品的充值金额
        Integer facePrice;
        if (StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebill)) {
            //多档位话费取传递值。
            facePrice = request.getPhoneDegree();
        } else {
            //非多档位话费取商品配置的档位（非多档位话费，档位只有一个）
            PriceDegreeDto pd = new PriceDegreeDto(appItem.getCustomPrice());
            String minDegree = pd.getMinDegreeKey();
            if (StringUtils.isBlank(minDegree)) {
                LOGGER.warn("话费采购 话费商品档位配置异常 appItemId={}", appItem.getId());
                throw new BizException(ErrorCode.E1100030.getDesc()).withCode(ErrorCode.E1100030.getCode());
            }
            //数据库中存的是元，需转换成分
            facePrice = Integer.valueOf(minDegree) * 100;
        }

        //获取手机号的运营商和归属地
        PhoneIspAndAreaDto phoneArea = remotePhoneAreaService.findPhoneIspAndArea(request.getPhone());
        if (phoneArea == null) {
            LOGGER.warn("话费采购 手机号码归属查询失败 phone={}", request.getPhone());
            throw new BizException(ErrorCode.E1100028.getDesc()).withCode(ErrorCode.E1100028.getCode());
        }
        String catName = phoneArea.getCarrier();
        String province = phoneArea.getProvince();

        //获取兑吧供应商产品
        SupplierProductDto sp = supplierProductService.queryProduct(catName, province, facePrice, appItem.getAppId());
        if (sp == null) {
            LOGGER.warn("话费采购 没有合适供应商 phone={} catName={} facePrice={}", request.getPhone(), catName, facePrice);
            throw new BizException(ErrorCode.E1100031.getDesc()).withCode(ErrorCode.E1100031.getCode());
        }

        //获取需要扣除开发者的金额
        Integer actualPrice = goodsItemActualPriceCalculateService
                .calculatePhonebillActualPrice(catName, province, facePrice, appItem.getAppId());
        if (actualPrice == null) {
            LOGGER.warn("话费采购 该地区的价格未设置 无法进行充值兑换 phone={} catName={} province={} facePrice={} appId={}",
                    request.getPhone(), catName, province, facePrice, appItem.getAppId());
            throw new BizException(ErrorCode.E1100029.getDesc()).withCode(ErrorCode.E1100029.getCode());
        }
        PhonebillParam param = new PhonebillParam();
        param.setPhoneNumber(request.getPhone());
        param.setPhoneDegree(request.getPhoneDegree());
        param.setPhoneCatName(catName);
        param.setPhoneProvince(province);
        param.setPhoneActualPrice(actualPrice);
        param.setPhoneFacePrice(facePrice);
        param.setSupplierProductId(sp.getId());
        return param;
    }


    private void checkCouponPurchaseCount(Long appId, AppItemDto appItemDto) throws BizException {
        String jsonConfig = WhiteAccessUtil.selectWhiteListJsonConfig(COUPON_LIMIT_UNICODE);
        JSONObject jsonObject = JSON.parseObject(jsonConfig);
        Long count = null;
        if (jsonObject == null
                || !ItemDto.TypeCoupon.equals(appItemDto.getType())) {
            return;
        }

        if (Objects.nonNull(jsonObject.get(appId))) {
            count = Long.valueOf((String) jsonObject.get(appId));
        }

        if (Objects.isNull(count)) {
            return;
        }

        Long todayCount = 0L;
        if ((todayCount = remoteHbConsisHashKvService.getLongByKey(getCouponLimitKey(appId))) != null
                && todayCount >= count) {
            throw new BizException(ErrorCode.E1100024.getDesc()).withCode(ErrorCode.E1100024.getCode());
        }
    }

    private void increaseCouponPurchaseCount(Long appId, AppItemDto appItemDto) {
        try {
            if (ItemDto.TypeCoupon.equals(appItemDto.getType())) {
                remoteHbConsisHashKvService.increaseByKey(getCouponLimitKey(appId), 1L);
            }
        } catch (Exception e) {
            LOGGER.warn("用户采购优惠券，计数失败", e);
        }

    }

    private String getCouponLimitKey(Long appId) {
        Date date = new Date();
        //按天 + app的纬度记录采购优惠券的次数
        return HBASE_LIMIT_KEY_PREFIX + DateUtils.getDayDate(date) + appId;
    }


    private CgbSupplyPurchaseResponse getCgbSupplyPurchaseResponse(SupplyPurchaseRequest request, AppSimpleDto app) {
        //重新回查下订单
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
        CgbSupplyPurchaseResponse cgbSupplyPurchaseResponse = new CgbSupplyPurchaseResponse();
        cgbSupplyPurchaseResponse.setOrderNum(String.valueOf(duiBaSupplyOrdersDto.getSupplyOrderNum()));
        cgbSupplyPurchaseResponse.setStartDay(new Date());
        String expressInfo = duiBaSupplyOrdersDto.getExpressInfo();
        if (StringUtils.isNotEmpty(expressInfo)) {
            cgbSupplyPurchaseResponse.setCouponCode((String) JSON.parseObject(expressInfo).get(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_CODE));
            Long endDayString = (Long) JSON.parseObject(expressInfo).get(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_OVERDUE);
            cgbSupplyPurchaseResponse.setEndDay(new Date(endDayString));

        }
        return cgbSupplyPurchaseResponse;
    }

    private SupplyPurchaseResponse getSupplyPurchaseResponse(SupplyPurchaseRequest request, AppSimpleDto app) {
        //重新回查下订单
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
        SupplyPurchaseResponse response = new SupplyPurchaseResponse();
        response.setOrderNum(String.valueOf(duiBaSupplyOrdersDto.getSupplyOrderNum()));
        return response;
    }

    /**
     * 山西建行幂等定制
     */
    private SupplyPurchaseResponse getsxjhSupplyPurchaseResponse(SupplyPurchaseRequest request, AppSimpleDto app) {
        // 触发幂等，返回订单号
        DuiBaSupplyOrdersDto supplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
        SupplyPurchaseResponse supplyPurchaseResponse = new SupplyPurchaseResponse();
        supplyPurchaseResponse.setOrderNum(String.valueOf(supplyOrdersDto.getSupplyOrderNum()));
        // 对处理中状态的立减金采购做重试
        executorService.submit(() -> tryWxCouponOrderRetry(supplyOrdersDto));
        return supplyPurchaseResponse;
    }

    /**
     * 江苏中行定制幂等返回
     */
    private SupplyPurchaseResponse getJiangsuSupplyPurchaseResponse(SupplyPurchaseRequest request, AppSimpleDto app) {
        DuiBaSupplyOrdersDto supplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
        // 没有找到订单信息，忽略(理论不存在因为前面已经触发幂等)
        if (supplyOrdersDto == null) {
            return null;
        }
        Long supplyOrderNum = supplyOrdersDto.getSupplyOrderNum();
        SupplyPurchaseResponse supplyPurchaseResponse = new SupplyPurchaseResponse();
        supplyPurchaseResponse.setOrderNum(String.valueOf(supplyOrdersDto.getSupplyOrderNum()));

        // 不是处理中状态，忽略
        if (!Objects.equals(DuiBaSupplyOrdersStatusEnum.FAIL.getCode(), supplyOrdersDto.getOrderStatus())) {
            return supplyPurchaseResponse;
        }

        // 不是虚拟商品，忽略
        if (!Objects.equals(PurchaseTypeEnum.VIRTUAL.getItemType(), supplyOrdersDto.getItemType())) {
            return supplyPurchaseResponse;
        }

        // 如果充值账号与原信息不相符，更新充值账号
        if (!StringUtils.equals(request.getAccount(),supplyOrdersDto.getRechargeAccount())){
            SupplyOrderInfoRequest supplyOrderInfoRequest = new SupplyOrderInfoRequest();
            supplyOrderInfoRequest.setSupplyOrderNum(supplyOrderNum);
            supplyOrderInfoRequest.setRechargeAccount(request.getAccount());
            remoteDuiBaSupplyOrderService.updateOrderInfo(supplyOrderInfoRequest);
        }

        // 根据订单号做重试
        executorService.submit(() -> virtualOrderRetry(supplyOrderNum,app));

        // 返回原订单号
        return supplyPurchaseResponse;
    }

    /**
     * 虚拟商品重试
     */
    private void virtualOrderRetry(Long supplyOrderNum, AppSimpleDto app) {
        try {
            remoteDuiBaSupplyOrderService.virtualRetryAndNoPrice(supplyOrderNum);
        }catch (Exception e){
            LOGGER.warn("[江苏中行], 虚拟商品重试异常，appId={}，thirdOrderNum={}", app.getId(),supplyOrderNum, e);
        }
    }

    private void tryWxCouponOrderRetry(DuiBaSupplyOrdersDto supplyOrdersDto) {
        // 状态不是处理中，忽略
        if (!Objects.equals(DuiBaSupplyOrdersStatusEnum.PROCESSING.getCode(), supplyOrdersDto.getOrderStatus())) {
            return;
        }
        // 找到对应的appItem
        List<AppItemDto> appItemDtoList = remoteAppItemGoodsBackendService.findSimpleByIds(Lists.newArrayList(supplyOrdersDto.getAppItemId()));
        if (CollectionUtils.isEmpty(appItemDtoList)) {
            return;
        }
        Long itemId = appItemDtoList.get(0).getItemId();
        List<KeyValueDto> keyValueDtoList = remoteItemNewExtraService.findItemAllApi(itemId);
        List<KeyValueDto> list = keyValueDtoList.stream().filter(i -> Objects.equals(i.getPropName(), ItemNewExtraEnum.WX_COUPON.getType())).collect(Collectors.toList());
        // 不是微信立减金，忽略
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 根据订单号做重试
        try {
            LOGGER.info("山西建行立减金重试,订单号={}", supplyOrdersDto.getSupplyOrderNum());
            remoteOrderOperationRecordService.wxCouponReTry(supplyOrdersDto.getSupplyOrderNum().toString(), null);
        } catch (Exception e) {
            LOGGER.warn("山西建行立减金重试异常,订单号={}", supplyOrdersDto.getSupplyOrderNum(), e);
        }
    }


    @Override
    public AppItemSkuDto checkAndGetAppItemSku(SupplyPurchaseRequest request, AppItemDto appItem) throws BizException {
        //话费商品没有sku，无需校验
        if (StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebill)
                || StringUtils.equals(appItem.getType(), ItemBaseDto.TypePhonebillDingzhi)) {
            return null;
        }

        // version = 0 表示是老商品 允许skuId传0
        if (Objects.equals(appItem.getVersion(), 0L) && request.getSkuId() == 0) {
            return null;
        } else if (Objects.equals(appItem.getVersion(), 1L) && request.getSkuId() == 0) {
            //新商品，并且入口是广发定制采购接口
            List<AppItemSkuDto> appItemSkuList = goodsCacheService.findAppItemSkuByAppItemId(appItem.getId());
            if (CollectionUtils.isEmpty(appItemSkuList)) {
                return null;
            }
            //如果sku不为空的，不管后面是不是加了多个，都取第一个
            return appItemSkuList.get(0);
        }
        AppItemSkuDto sku = goodsCacheService.findAppItemSkuById(request.getSkuId());
        if (sku == null || !Objects.equals(sku.getAppItemId(), request.getAppItemId())) {
            // 商品不存在
            throw new BizException(ErrorCode.E1100009.getDesc()).withCode(ErrorCode.E1100009.getCode());
        }
        if (!Objects.equals(sku.getSaleStatus(), 1)) {
            // 商品已下架
            throw new BizException(ErrorCode.E1100010.getDesc()).withCode(ErrorCode.E1100010.getCode());
        }
        return sku;
    }

    @Override
    public  AppItemDto checkAndGetAppItem(SupplyPurchaseRequest request, Long appId) throws BizException {
        AppItemDto appItem = goodsCacheService.findAppItemById(request.getAppItemId());
        if (appItem == null || !Objects.equals(appId, appItem.getAppId())) {
            // 商品不存在
            throw new BizException(ErrorCode.E1100009.getDesc()).withCode(ErrorCode.E1100009.getCode());
        }
        if (Objects.equals(appItem.getDeleted(), true) || !Objects.equals(appItem.getStatus(), AppItemDto.StatusOn)) {
            // 商品已下架
            throw new BizException(ErrorCode.E1100010.getDesc()).withCode(ErrorCode.E1100010.getCode());
        }
        if (appItem.getItemId() == null) {
            // 不支持购买自有商品
            throw new BizException(ErrorCode.E1100010.getDesc()).withCode(ErrorCode.E1100010.getCode());
        }

        //校验兑吧商品状态
        ItemDto itemDto = remoteDuibaItemGoodsService.findSimpleWithoutStockAndWithoutCache(appItem.getItemId());
        if (itemDto == null || BooleanUtils.isNotTrue(itemDto.getEnable()) || BooleanUtils.isTrue(itemDto.getDeleted())) {
            // 商品已下架
            throw new BizException(ErrorCode.E1100010.getDesc()).withCode(ErrorCode.E1100010.getCode());
        }

        if (Objects.equals(appItem.getType(), ItemDto.TypeObject)) {
            if (StringUtils.isBlank(request.getAddress()) || StringUtils.isBlank(request.getPhone()) || StringUtils.isBlank(request.getName())) {
                // 实物商品收货信息不能为空
                throw new BizException(ErrorCode.E1100007.getDesc()).withCode(ErrorCode.E1100007.getCode());
            }
            // 新版接口增加了省、市、区的传参，为必传，但是此前已经对接的app可以不传
            if (!OLD_VERSION_APP_IDS.contains(appId)
                    && (StringUtils.isBlank(request.getProvince()) || StringUtils.isBlank(request.getCity()) || StringUtils.isBlank(request.getDistrict()))
            ) {
                // 实物商品收货信息不能为空
                throw new BizException(ErrorCode.E1100007.getDesc()).withCode(ErrorCode.E1100007.getCode());
            }
        }

        //如果是虚拟商品，判断手机号不能为空
        if (Objects.equals(appItem.getType(), ItemDto.TypeVirtual)
                && !(StringUtils.isNotBlank(request.getAccount()) || StringUtils.isNotBlank(request.getPhone()))) {
            throw new BizException(ErrorCode.E1100022.getDesc()).withCode(ErrorCode.E1100022.getCode());

        }

        //商品库存预校验
        FrontItemSpuStockResponse frontItemSpuStockResponse = queryStockNoCache(Lists.newArrayList(appItem.getId()), appId);
        if (CollectionUtils.isNotEmpty(frontItemSpuStockResponse.getList())) {
            FrontItemSpuStockVO frontItemSpuStockVO = frontItemSpuStockResponse.getList().get(0);
            List<FrontItemSkuStockVO> skuList = frontItemSpuStockVO.getSku();
            for (FrontItemSkuStockVO sku : skuList) {
                if (sku.getSkuId().equals(request.getSkuId())
                        && sku.getStock() < 1) {
                    throw new BizException(ErrorCode.E1100025.getDesc()).withCode(ErrorCode.E1100025.getCode());
                }
            }
        }
        // 微信立减金校验
        wxCouponCheck(request, itemDto);
        // 微信红包校验
        wxRedpackCheck(request, itemDto);
        // 根据商品校验订单号格式
        orderNoCheck(request, appItem);
        return appItem;
    }

    private void orderNoCheck(SupplyPurchaseRequest request, AppItemDto appItemDto) throws BizException {
        String str = WhiteAccessUtil.selectWhiteListJsonConfig(PURCHASE_ITEM_CHECK_ORDER_NO_CODE);
        if(StringUtils.isBlank(str)) {
            return;
        }
        Map<String, String> paramMap = JSON.parseObject(str, new TypeReference<Map<String, String>>() {});
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            if (!k.contains(appItemDto.getId().toString())) {
                continue;
            }
            if (!request.getThirdOrderNum().matches(v)) {
                throw new BizException(ErrorCode.E3004002.getDesc()).withCode(ErrorCode.E3004002.getCode());
            }
        }
    }

    private void wxRedpackCheck(SupplyPurchaseRequest request, ItemDto itemDto) throws BizException {
        if(!Objects.equals(itemDto.getMerchantCoding(), VisualItemChannelEnum.WX_REDPACK.getCode())) {
            return;
        }
        if(request.getAmount() == null) {
            throw new BizException(ErrorCode.E1100036.getDesc()).withCode(ErrorCode.E1100036.getCode());
        }
        if(request.getAmount() < 100) {
            throw new BizException(ErrorCode.E1100037.getDesc()).withCode(ErrorCode.E1100037.getCode());
        }
        if(request.getAmount() > 49900) {
            throw new BizException(ErrorCode.E1100038.getDesc()).withCode(ErrorCode.E1100038.getCode());
        }
    }

    /**
     * 福建兑吧微信立减金校验
     *
     * @param request
     * @param item
     */
    @Override
    public void wxCouponCheck(SupplyPurchaseRequest request, ItemDto item) throws BizException {
        if(Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.DUI_BA_CORNUCOPIA.getCode())) {
            return;
        }
        if (!Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.FJ_DB_WXLJJ.getCode()) &&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.DB_FJ_WXLJJ.getCode()) &&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.DB_FJ_WXLJJ_002.getCode()) &&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_001.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_002.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_003.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_004.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_004.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_005.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_006.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_DB_WXLJJ_007.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.SHANDONG_DT_WXLJJ_001.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.SHANDONG_DT_WXLJJ_002.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.ANHUI_ZY_WXLJJ_001.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.FUJIAN_DA_WXLJJ_001.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.FUJIAN_DA_WXLJJ_002.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.HZDBSY_DB_WXLJJ_001.getCode())&&
                !Objects.equals(item.getMerchantCoding(), VisualItemChannelEnum.JIANGSU_DB_WXLJJ_001.getCode())) {
            request.setWxCouponMinimum(null);
            request.setWxCouponValue(null);
            return;
        }
        List<KeyValueDto> keyValueDtoList = remoteItemNewExtraService.findItemAllApi(item.getId());
        if (CollectionUtils.isEmpty(keyValueDtoList)) {
            request.setWxCouponMinimum(null);
            request.setWxCouponValue(null);
            return;
        }
        List<KeyValueDto> list = keyValueDtoList.stream().filter(i -> Objects.equals(i.getPropName(), ItemNewExtraEnum.WX_COUPON.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            request.setWxCouponMinimum(null);
            request.setWxCouponValue(null);
            return;
        }
        // 没有用默认配置
        KeyValueDto keyValueDto = list.get(0);
        JSONObject wxCoupon = JSON.parseObject(keyValueDto.getPropValue());
        if (wxCoupon == null || wxCoupon.getInteger("wxCouponUseType") == null || wxCoupon.getInteger("quotaType") == null) {
            request.setWxCouponMinimum(null);
            request.setWxCouponValue(null);
            return;
        }
        if (wxCoupon.getInteger("wxCouponUseType") != 2) {
            throw new BizException(ErrorCode.E1100033.getDesc()).withCode(ErrorCode.E1100033.getCode());
        }
        if (wxCoupon.getInteger("quotaType") == 2 && (request.getWxCouponValue() == null || request.getWxCouponMinimum() == null)) {
            throw new BizException(ErrorCode.E1100034.getDesc()).withCode(ErrorCode.E1100034.getCode());
        }
        if (wxCoupon.getInteger("quotaType") != 2 && (request.getWxCouponValue() != null || request.getWxCouponMinimum() != null)) {
            throw new BizException(ErrorCode.E1100035.getDesc()).withCode(ErrorCode.E1100035.getCode());
        }
    }

    @Override
    public Object queryOrder(QuerySupplyOrderRequest request, Long appId) throws BizException {
        DuiBaSupplyOrdersDto order = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), appId);
        if (order == null) {
            throw new BizException(ErrorCode.E1100013.getDesc()).withCode(ErrorCode.E1100013.getCode());
        }
        SupplyOrderExtraResponse response = this.convertResponse(order);
        //微信红包商品处理
        setExtraUrlByWxRedpack(order, response);
        //预下单商品处理
        setPreOrderExtraUrl(order, response);
        //替换response中字段为code的域名，把s.duiba.cn 替换为 s.fenvj.cn
        replaceCodeUrl(response);
        ExtraStrategy route = ExtraStrategyRegistry.route(order.getAppId());
        if (Objects.isNull(route)) {
            return response;
        }
        return route.extraFild(response, order);
    }

    /**
     * 替换code中的域名
     * @param response
     */
    private void replaceCodeUrl(SupplyOrderExtraResponse response) {
        String code = response.getCode();
        try {
            if(StringUtils.isBlank(code)) {
                return;
            }
            String shortUrlReplaceConf = common2Config.getShortUrlReplaceConf();
            if(StringUtils.isBlank(shortUrlReplaceConf)) {
                return;
            }
            List<ShortUrlReplaceConfEntity> shortUrlReplaceConfList = JSON.parseArray(shortUrlReplaceConf, ShortUrlReplaceConfEntity.class);
            if(CollectionUtils.isEmpty(shortUrlReplaceConfList)) {
                return;
            }
            for (ShortUrlReplaceConfEntity shortUrlReplaceConfEntity : shortUrlReplaceConfList) {
                code = code.replace(shortUrlReplaceConfEntity.getUrl(), shortUrlReplaceConfEntity.getReplaceUrl());
            }
            response.setCode(code);
        } catch (Exception e) {
            LOGGER.warn("短链转换异常, code={}", code, e);
        }
    }

    @Override
    public SupplyOrderExtraResponse convertResponse(DuiBaSupplyOrdersDto order) {
        SupplyOrderExtraResponse response = new SupplyOrderExtraResponse();
        Integer status = order.getOrderStatus();
        response.setThirdOrderNum(order.getThirdOrderNum());
        response.setOrderNum(String.valueOf(order.getSupplyOrderNum()));
        //创建和预下单都是处理中
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.CREATE.getCode()) ||
                Objects.equals(status, DuiBaSupplyOrdersStatusEnum.PRE.getCode())) {
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_PROCESSING);
        }
        //如果是虚拟商品充值的处理中状态，返回订单状态亦是处理中
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.PROCESSING.getCode())) {
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_PROCESSING);
        }
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.WAIT_SEND.getCode())) {
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_WAIT_SEND);
        }
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.SUCCESS.getCode())) {
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_SUCCESS);
            setExpressInfo(response, order);
        }
        if (Objects.equals(status, DuiBaSupplyOrdersStatusEnum.FAIL.getCode())) {
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_FAIL);
            response.setFailReason(order.getFailReason());
        }
        return response;

    }

    private void setPreOrderExtraUrl(DuiBaSupplyOrdersDto order, SupplyOrderExtraResponse response) {
        String expressInfo = order.getExtraInfo();
        if (StringUtils.isBlank(expressInfo)) {
            return;
        }
        JSONObject info = JSONObject.parseObject(expressInfo);
        //预下单的商品。微信立减金有值
        if(Objects.nonNull(info) && !info.isEmpty() && StringUtils.equals("true", info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_PRE_ORDER))) {
            response.setLink(converPreOrderlink(String.valueOf(order.getSupplyOrderNum()), order.getAppId(), response.getSource()));
        }
    }

    private void setExtraUrlByWxRedpack(DuiBaSupplyOrdersDto order, SupplyOrderExtraResponse response) {
        String itemType = order.getItemType();
        String expressInfo = order.getExtraInfo();
        if (StringUtils.isBlank(expressInfo)) {
            return;
        }
        JSONObject info = JSONObject.parseObject(expressInfo);
        if (Objects.equals(itemType, PurchaseTypeEnum.VIRTUAL.getItemType()) && StringUtils.isNotBlank(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_WX_REDPACK_URL_ORDER))) {
            String orderNum = BlowfishUtils.encryptBlowfish(order.getSupplyOrderNum() + RandomUtil.randomString(5), commonConfig.getSupplyOrderPreBlowfishKey());
            response.setExtraUrl(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_WX_REDPACK_URL_ORDER) + orderNum);
            response.setCode(commonConfig.getWxRedpackUrl() + orderNum);
        }
    }

    @Override
    public FrontItemSpuStockResponse queryStock(List<Long> appItemIds, Long appId) {
        // 对库存信息设置5秒缓存 防止大流量
        return advancedCacheClient.getWithCacheLoader(generateQueryStockKey(appItemIds, appId), 5, TimeUnit.SECONDS, () -> queryStockNoCache(appItemIds, appId));
    }

    @Override
    public FrontItemItemStockVO getStockByAppItemId(Long appItemId, AppSimpleDto app) {
        ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemId, null, app);
        if (itemKeyDto == null || !Objects.equals(itemKeyDto.getAppItem().getAppId(), app.getId())) {
            throw new RuntimeException("无权访问");
        }
        FrontItemItemStockVO itemStockVO = new FrontItemItemStockVO();
        if (!itemKeyDto.getItem().getEnable()
                || !AppItemDto.StatusOn.equals(itemKeyDto.getAppItem().getStatus())
                || BooleanUtils.isTrue(itemKeyDto.getItem().getDeleted())) {
            //只要有一个不是上架状态或者兑吧后台删除了都算未上架
            itemStockVO.setStatus(0);
            itemStockVO.setStock(0);
            return itemStockVO;
        }
        boolean flag = itemKeyDto.getItem().isOpTypeItem(ItemDto.OpTypeSpecify);
        if (flag) {
            //说明是定向，查询定向表库存
            DubboResult<ItemAppSpecifyDto> result = remoteItemAppSpecifyService.findByItemIdAndAppId(itemKeyDto.getItem().getId(), app.getId());
            if (result.getResult() == null) {
                itemStockVO.setStatus(0);
                itemStockVO.setStock(0);
                return itemStockVO;
            }
            itemStockVO.setStock(result.getResult().getRemaining());
            return itemStockVO;
        }
        //返回剩余库存spu
        itemStockVO.setStock(itemKeyDto.getItem().getRemaining());
        return itemStockVO;
    }

    @Override
    public FrontItemSpuStockResponse queryStockNoCache(List<Long> appItemIds, Long appId) {
        List<AppItemSkuRemainingParam> skuStock = remoteAppItemGoodsService.findRemainingByAppItemIdsAndAppId(appItemIds, appId);
        if (CollectionUtils.isEmpty(skuStock)) {
            return new FrontItemSpuStockResponse(Lists.newArrayList());
        }
        ImmutableListMultimap<Long, AppItemSkuRemainingParam> mapping = Multimaps.index(skuStock, AppItemSkuRemainingParam::getAppItemId);

        List<FrontItemSpuStockVO> list = Lists.newArrayList();
        mapping.keySet().forEach(k -> {
            FrontItemSpuStockVO spu = new FrontItemSpuStockVO();
            spu.setAppItemId(k);
            spu.setSku(mapping.get(k).stream()
                    // 优惠券或卡密批次过期后remaining字段会返回null 此处设置为0
                    .map(s -> new FrontItemSkuStockVO(s.getAppSkuId(), s.getRemaining() == null ? 0 : s.getRemaining()))
                    .collect(Collectors.toList()));
            list.add(spu);
        });
        return new FrontItemSpuStockResponse(list);
    }

    private void setExpressInfo(SupplyOrderExtraResponse response, DuiBaSupplyOrdersDto order) {
        // 从expressInfo里面获取数据
        String itemType = order.getItemType();
        String expressInfo = order.getExpressInfo();
        if (StringUtils.isBlank(expressInfo)) {
            return;
        }
        JSONObject info = JSONObject.parseObject(expressInfo);
        if (Objects.equals(itemType, PurchaseTypeEnum.OBJECT.getItemType())) {
            response.setExpressNo(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_EXPRESS_NO));
            response.setExpressCompanyName(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_EXPRESS_COMPANY_NAME));
        }
        if (Objects.equals(itemType, PurchaseTypeEnum.COUPON.getItemType())) {
            response.setCode(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_CODE));
            response.setLink(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_LINK));
            response.setOverdue(info.getLong(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_OVERDUE));
            // 老优惠券 会存在设置了密码的券码券
            response.setPassword(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_PASSWORD));

            if (StringUtils.isNotBlank(response.getCode())) {
                setExtraUrl(response.getCode(), response);
            }
        }
        if (Objects.equals(itemType, PurchaseTypeEnum.VIRTUAL.getItemType())){
            String code = info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_COUPON_ID);
            if(StringUtils.isBlank(code)){
                code = info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_VOUCHER_ID);
            }
            response.setCode(code);
        }
        if (Objects.equals(itemType, PurchaseTypeEnum.CRYPTO_CARD.getItemType())) {
            response.setAccount(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_ACCOUNT));
            response.setPassword(info.getString(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_PASSWORD));
            response.setOverdue(info.getLong(DuiBaSupplyOrdersDto.EXPRESS_INFO_KEY_OVERDUE));
        }
    }

    /**
     * 设置额外url
     *
     * @param link     链接
     * @param response
     * @return {@link String}
     */
    private void setExtraUrl(String link, SupplyOrderExtraResponse response){
        if(link.contains(cornucopia_url) || link.contains(cornucopia_test_url)) {
            response.setCardVerifyStatus(cornucopiaStatusCache.get(link));
            return;
        }
        List<AlipayRepackCardDto> alipayRepackCardDtos = remoteRepackCardService.findRepackCardInfoByUrl(Collections.singletonList(link));
        if (CollectionUtils.isEmpty(alipayRepackCardDtos)){
            return;
        }
        AlipayRepackCardDto alipayRepackCardDto = alipayRepackCardDtos.get(0);
        String redpackActivityId = alipayRepackCardDto.getRedpackActivityId();
        String cardCode = alipayRepackCardDto.getCardCode();
        AlipayActivityConfigPageParam alipayActivityConfigPageParam = new AlipayActivityConfigPageParam();
        alipayActivityConfigPageParam.setRedpackActivityId(redpackActivityId);
        List<AlipayActivityConfigDto> alipayActivityConfigDtos = remoteAlipayActivityConfigService.queryByCondition(alipayActivityConfigPageParam);

        if (CollectionUtils.isEmpty(alipayActivityConfigDtos)){
            return;
        }
        AlipayActivityConfigDto alipayActivityConfigDto = alipayActivityConfigDtos.get(0);
        String url = getUrlBySource(alipayActivityConfigDto, redpackActivityId, cardCode);
        response.setExtraUrl(url);
        //判断卡券是否是已领取，0代表已领取未使用  1代表已核销  null代表未领取
        response.setCardVerifyStatus(Objects.equals(alipayRepackCardDto.getDrawStatus(), DrawStatusEnum.DRAW_STATUS_NORMAL_USED.getCode()) ? Integer.valueOf(0) : (Objects.equals(alipayRepackCardDto.getDrawStatus(), DrawStatusEnum.DRAW_STATUS_CLAIMED.getCode()) ? 1 : null));
        response.setCardVerifyEndTime(DateUtils.getSecondStr(alipayActivityConfigDto.getRedpackActivityEndTime()));
    }

    /**
     * 获取聚宝盆卡券状态
     * @return
     */
    private Integer cornucopiaCardStatus(String link) {
        try {
            ProjectXQueryOrderParam param = new ProjectXQueryOrderParam();
            param.setTimestamp(System.currentTimeMillis());
            param.setShortUrls(Collections.singletonList(link));
            Map<String, String> paramMaps = JSONObject.parseObject(JSON.toJSONString(param), new TypeReference<Map<String, String>>() {
            });
            paramMaps.put("appSecret", common2Config.getAppSecret());
            param.setSign(SignTool.sign(paramMaps));
            HttpPost httpPost = new HttpPost(common2Config.getCornucopiaGateway() + common2Config.getPath());
            StringEntity stringEntity = new StringEntity(JSON.toJSONString(param), "UTF-8");
            stringEntity.setContentEncoding("UTF-8");
            stringEntity.setContentType("application/json");
            httpPost.setEntity(stringEntity);
            String result = httpClientUtil.sendPost(httpPost, true);
            if(StringUtils.isBlank(result)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            List<ProjectXQueryRespDto> projectXQueryRespDtos = JSONArray.parseArray(jsonObject.getString("data"), ProjectXQueryRespDto.class);
            Map<String, Integer> shortUrlOrderStatusMap = projectXQueryRespDtos.stream().collect(Collectors.toMap(ProjectXQueryRespDto::getShortUrl, ProjectXQueryRespDto::getOrderStatus, (n, o) -> n));
            Integer status = shortUrlOrderStatusMap.getOrDefault(link, null);
            // 0代表已领取未使用  1代表已核销  null代表未领取
            return (Objects.isNull(status) || status == 0) ? null : (status == 11 ? 1 : 0);
        } catch (Exception e) {
            LOGGER.warn("获取聚宝盆卡券状态失败, link={}", link, e);
        }
        return null;
    }

    private String getUrlBySource(AlipayActivityConfigDto alipayActivityConfigDto, String redpackActivityId, String cardCode) {
        Integer redpackSource = alipayActivityConfigDto.getRedpackSource();
        String url = "";
        // 支付宝
        if (redpackSource.equals(RedpackCouponSourceEnum.ALIPAY.getCode())){
            url = supplyConfig.getAlipayCouponUrl().replace("{activityId}", redpackActivityId).replace("{code}", cardCode);
        } else if (redpackSource.equals(RedpackCouponSourceEnum.WECHAT.getCode())){
            // 微信
            url = supplyConfig.getWxCouponUrl().replace("{activityId}", redpackActivityId).replace("{code}", cardCode);
        }
        return url;
    }

    /**
     * 根据appItemId构建返回给开发者的信息
     */
    @Override
    public FrontItemSpuVO getFrontItemSpuPageResponseResult(Long appItemId, AppSimpleDto app) throws BizException {//NOSONAR
        AppItemDto object = remoteAppItemGoodsBackendService.findWithQueries(appItemId, new ItemQueries().queryFromCache().withDescConfig().withSku().withSupplyPrice().withCredits().withCreditsSku());
        FrontItemSpuVO spu = new FrontItemSpuVO();
        spu.setAppItemId(object.getId());
        if (Objects.equals(object.getType(), ItemDto.TypeCoupon) && Objects.equals(object.getSubType(), ItemDto.SubTypeCryptoCard)) {
            spu.setType(ItemDto.TYPE_CRYPTO_CARD);
        } else {
            spu.setType(object.getType());
        }
        if (Objects.equals(spu.getType(), ItemDto.TypeCoupon)) {
            spu.setSubType(object.getSubType());
        }
        ItemDescConfigDto descConfig = object.getItemDescConfigDto();
        if (descConfig != null) {
            spu.setImages(descConfig.getMultiImage());
            spu.setDescription(descConfig.getDescription());
        }
        if (AppIdConstant.isSunNingApp(app.getId())) {
            // 由于对接问题，对方需要在此字段传输logo字段的内容
            // 需求文档 http://cf.dui88.com/pages/viewpage.action?pageId=29133438
            spu.setSmallImage(object.getLogo());
        } else {
            spu.setSmallImage(object.getSmallImage());
        }
        spu.setItemName(object.getTitle());
        List<AppItemSkuDto> allSku = object.getAppItemSkuDtoList();
        if (CollectionUtils.isEmpty(allSku)) {
            return spu;
        }
        spu.setSpecificationList(remoteAppItemSkuService.getAttribute(allSku));
        spu.setGmtModified(object.getGmtModified().getTime());
        //积分换购信息
        MarketingItemCreditsDto marketingItemCreditsDto = object.getMarketingItemCreditsDto();
        List<MarketingItemCreditsSkuDto> marketingItemCreditsSkuDtoList = object.getMarketingItemCreditsSkuDtoList();
        //积分换购sku
        Map<Long, MarketingItemCreditsSkuDto> marketingItemCreditsSkuDtoMap = new HashMap<>();
        if (marketingItemCreditsSkuDtoList != null) {
            marketingItemCreditsSkuDtoMap = marketingItemCreditsSkuDtoList.stream().collect(Collectors.toMap(MarketingItemCreditsSkuDto -> {
                return Optional.ofNullable(MarketingItemCreditsSkuDto.getSkuId()).orElse(0L);
            }, Function.identity(), (oldv, newv) -> newv));
        }
        final Map<Long, MarketingItemCreditsSkuDto> finalMarketingItemCreditsSkuDtoMap = marketingItemCreditsSkuDtoMap;
        CouponModifySkuValidDto couponModifySkuValidDto;
        Map<String, CouponSkuValidDto> couponSkuValidMap = null;
        if (ObjectUtils.equals(ItemDto.TypeCoupon, object.getType())) {
            couponModifySkuValidDto = remoteItemKeyService.findSupplyFrontValidEndDate(object);
            if (couponModifySkuValidDto != null) {
                //券最后修改时间和主表最后修改时间比较取最大值
                if (couponModifySkuValidDto.getGmtModified() != null && object.getGmtModified().getTime() < couponModifySkuValidDto.getGmtModified().getTime()) {
                    spu.setGmtModified(couponModifySkuValidDto.getGmtModified().getTime());
                }
                //各sku有效期
                List<CouponSkuValidDto> couponSkuValidList = couponModifySkuValidDto.getCouponSkuValidDtoList();
                couponSkuValidMap = couponSkuValidList.stream().collect(Collectors.toMap(CouponSkuValid -> {
                    if (CouponSkuValid.getItemId() != null) {
                        return CouponSkuValid.getItemId() + "_" + CouponSkuValid.getSkuId();
                    }
                    return CouponSkuValid.getAppItemId() + "_" + CouponSkuValid.getSkuId();
                }, Function.identity(), (oldv, newv) -> newv));
            }
        }
        final Map<String, CouponSkuValidDto> finalCouponSkuValidMap = couponSkuValidMap;
        spu.setSku(allSku.stream().map(s -> {
            FrontItemSkuVO skuInfo = new FrontItemSkuVO();
            if (s.getId() == null) {
                // 老商品 构建一个skuId=0的对象
                skuInfo.setSkuId(0L);
            } else {
                skuInfo.setSkuId(s.getId());
            }
            skuInfo.setPrice(s.getSupplyPrice());
            skuInfo.setFacePrice(s.getFacePrice());
            skuInfo.setSalePrice(s.getSalePrice());
            if (s.getRemaining() == null || s.getRemaining() < 0) {
                skuInfo.setStock(0);
            } else {
                skuInfo.setStock(s.getRemaining());
            }
            if (!Objects.equals("0:0", s.getAttributeJson())) {
                skuInfo.setSkuInfo(s.getAttributeJson());
            }
            skuInfo.setMerchantCoding(s.getMerchantCoding());
            //sku兑换价格
            if (ObjectUtils.equals(ExchangeTypeEnum.AUTOMATIC.getCode(), marketingItemCreditsDto.getExchangeType())) {
                skuInfo.setCustomCredits((long) Math.ceil(s.getSalePrice() * (long) app.getCreditsRate() / 100.0));
                skuInfo.setCustomPrice(0L);
            } else {
                MarketingItemCreditsSkuDto marketingItemCreditsSkuDto = finalMarketingItemCreditsSkuDtoMap.get(skuInfo.getSkuId());
                if (marketingItemCreditsSkuDto != null) {
                    skuInfo.setCustomCredits(marketingItemCreditsSkuDto.getCustomCredits() == null ? 0 : marketingItemCreditsSkuDto.getCustomCredits());
                    skuInfo.setCustomPrice(marketingItemCreditsSkuDto.getCustomPrice() == null ? 0 : marketingItemCreditsSkuDto.getCustomPrice());
                }
            }
            //优惠券卡密所有批次有效期
            if (finalCouponSkuValidMap != null) {
                String key = object.getItemId() == null ? object.getId() + "_" + s.getId() : object.getItemId() + "_" + s.getItemSkuId();
                CouponSkuValidDto couponSkuValid = finalCouponSkuValidMap.get(key);
                if (couponSkuValid != null && couponSkuValid.getValidEndDateList() != null) {
                    List<Date> dateList = couponSkuValid.getValidEndDateList();
                    List<Long> validEndDateList = dateList.stream().map(validEndDate -> validEndDate.getTime()).collect(Collectors.toList());
                    skuInfo.setValidEndDateList(validEndDateList);
                }
            }
            return skuInfo;
        }).collect(Collectors.toList()));
        //设置地域限制
        setAreaLimitDesc(spu, object.getItemId());
        //设置话费信息
        setPhonebillInfo(spu, object, app);
        //扩展信息
        setExtraInfo(spu, app);
        return spu;
    }

    private void setExtraInfo(FrontItemSpuVO spu, AppSimpleDto app) {
        if (!hsbcConfig.queryVersionTagByVersion(HsbcConfig.VERSION_BY_CAX_GOODS, app.getId())) {
            return;
        }
        List<KeyValueEntity> appItemAllApi = remoteItemNewExtraService.findAppItemAllApi(spu.getAppItemId());
        Map<String, String> propMap = appItemAllApi.stream().collect(Collectors.toMap(KeyValueEntity::getPropName, obj -> org.apache.commons.lang3.ObjectUtils.defaultIfNull(obj.getPropValue(), StringUtils.EMPTY), (oldv, newv) -> newv));
        boolean caxGoods = BooleanUtil.toBoolean(propMap.getOrDefault(HUIFENG_CAX_GOODS_KEY, StringUtils.EMPTY));
        spu.setItemType(caxGoods ? HUIFENG_CAX_GOODS_CODE : HUIFENG_OTHER_GOODS_CODE);
    }

    private void setPhonebillInfo(FrontItemSpuVO spu, AppItemDto appItemDto, AppSimpleDto app) {
        if (!Objects.equals(appItemDto.getType(), ItemDto.TypePhonebillDingzhi)
                && !Objects.equals(appItemDto.getType(), ItemDto.TypePhonebill)) {
            //非话费商品，直接返回
            return;
        }
        spu.setType(ItemDto.TypePhonebill);
        spu.setSpecificationList(Lists.newArrayList());
        spu.setSku(Lists.newArrayList());
        PhonebillInfoVO infoVO = new PhonebillInfoVO();
        //单档位
        if (Objects.equals(appItemDto.getType(), ItemDto.TypePhonebillDingzhi)) {
            ItemKeyDto itemKeyDto = goodsItemDataService.getItemKeyByAppItemIdAndItemId(appItemDto.getId(), appItemDto.getItemId(), app);
            infoVO.setDegreeType("single");
            infoVO.setCredits(ItemKeyUtil.getCreditsByCache(itemKeyDto, app));
            Double price = consumerItemRenderService.getPrice(itemKeyDto, app);
            //转成分
            Long facePrice = price.longValue() * 100;
            infoVO.setFacePrice(facePrice);

        }
        //多档位
        if (Objects.equals(appItemDto.getType(), ItemDto.TypePhonebill)) {
            infoVO.setDegreeType("multi");

            PriceDegreeDto pd = new PriceDegreeDto(appItemDto.getCustomPrice());
            Map<String, Map<String, String>> map = PlatFormCouponServiceImpl.parseDegreeCredits(pd, app);


            List<MultiDegreeInfoVO> multiDegreeInfoVOList = Lists.newArrayList();
            for (String key : map.keySet()) {
                MultiDegreeInfoVO vo = new MultiDegreeInfoVO();
                //转成分
                vo.setFacePrice(Long.valueOf(key) * 100);
                vo.setCredits(Long.valueOf(map.get(key).get(PriceDegreeDto.CREDITS_KEY)));
                multiDegreeInfoVOList.add(vo);
            }
            infoVO.setMultiDegreeInfo(multiDegreeInfoVOList);
        }
        spu.setPhonebillInfo(infoVO);

    }

    private void setAreaLimitDesc(FrontItemSpuVO spu, Long itemId) {
        if (!Objects.equals(spu.getType(), ItemDto.TypeObject)) {
            return;
        }
        List<AddrLimitDto> addrLimitList = remoteAddrLimitService.findAddrLimitByGidApi(GoodsTypeEnum.DUIBA, itemId).getResult();
        if (CollectionUtils.isEmpty(addrLimitList)) {
            return;
        }
        //先将addrLimitList转成map再取keySet、再把set里的province按照顺序组合
        Optional<String> areaLimitDesc = addrLimitList.stream()
                .collect(Collectors.toMap(AddrLimitDto::getProvince, e -> e, (k1, k2) -> k1))
                .keySet().stream()
                .reduce((old, e) -> old + "、" + e);
        spu.setAreaLimitDesc(areaLimitDesc.get());
    }



    @Override
    public Pair<Boolean, SupplyPurchaseResponse> checkRepeatOrder(SupplyPurchaseRequest request, AppSimpleDto app) {
        try {
            DuiBaSupplyOrdersDto supplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(request.getThirdOrderNum(), app.getId());
            Pair<Boolean, SupplyPurchaseResponse> result = Pair.of(false, null);
            if (supplyOrdersDto != null) {
                SupplyPurchaseResponse response = new SupplyPurchaseResponse();
                response.setOrderNum(String.valueOf(supplyOrdersDto.getSupplyOrderNum()));
                result = Pair.of(true, response);
                JSONObject jsonObject = JSON.parseObject(supplyOrdersDto.getExpressInfo());
                if ((Objects.equals(PurchaseTypeEnum.COUPON.getItemType(), supplyOrdersDto.getItemType())
                        || Objects.equals(PurchaseTypeEnum.CRYPTO_CARD.getItemType(), supplyOrdersDto.getItemType()))
                        && Objects.equals(DuiBaSupplyOrdersStatusEnum.FAIL.getCode(), supplyOrdersDto.getOrderStatus())
                        && (jsonObject == null || jsonObject.get("code") == null)
                ) {
                    LOGGER.info("杭州银行-采购订单重试，appId={}，thirdOrderNum={}", app.getId(), request.getThirdOrderNum());
                    executorService.submit(() -> couponOrderRetry(request, app));
                }
            }
            return result;
        } catch (Exception e) {
            LOGGER.warn("杭州银行-采购异常，appId={}，thirdOrderNum={}", app.getId(), request.getThirdOrderNum(), e);
        }
        return Pair.of(false, null);
    }

    private void couponOrderRetry(SupplyPurchaseRequest request, AppSimpleDto app) {
        try {
            AppItemDto appItem = checkAndGetAppItem(request, app.getId());
            AppItemSkuDto sku = checkAndGetAppItemSku(request, appItem);
            PhonebillParam phonebillParam = checkAndGetPhonebillParam(request, appItem, app);
            WxCouponParam wxCouponParam = checkAndGetWxCouponParam(request);
            DuiBaSupplyOrderPurchaseRequest r = new DuiBaSupplyOrderPurchaseRequest();
            r.setAppItem(appItem);
            r.setAppItemSku(sku);
            r.setAppId(app.getId());
            r.setAppName(app.getName());
            r.setPhone(request.getPhone());
            r.setAddress(request.getAddress());
            r.setName(request.getName());
            r.setThirdOrderNum(request.getThirdOrderNum());
            r.setDeveloperId(app.getDeveloperId());
            r.setProvince(request.getProvince());
            r.setCity(request.getCity());
            r.setDistrict(request.getDistrict());
            r.setStreet(request.getStreet());
            r.setPhonebillParam(phonebillParam);
            r.setAccount(request.getAccount());
            r.setWxCouponParam(wxCouponParam);
            // 充值商品，商品账号格式限制校验,根据配置决定是否过滤空格
            if (StringUtils.isNotEmpty(r.getAccount()) && Objects.equals(appItem.getType(), ItemDto.TypeVirtual)) {
                String account = virtualExtrangeProcessor.checkAccountFormat(Optional.ofNullable(appItem).map(AppItemDto::getId).orElse(null), null, r.getAccount());
                r.setAccount(account);
            }
            remoteDuiBaSupplyOrderService.couponOrderRetry(r);
        } catch (Exception e) {
            LOGGER.warn("优惠券采购单重试异常，appId={}，thirdOrderNum={}", app.getId(), request.getThirdOrderNum(), e);
        }
    }

    @Override
    public void hzbankRetry(AppSimpleDto app, HzBankSupplyPurchaseRetryRequest hzBankSupplyPurchaseRetryRequest) throws BizException {
        // 查询订单相关信息
        DuiBaSupplyOrdersDto duiBaSupplyOrdersDto = remoteDuiBaSupplyOrderService.findByThirdOrderNumAndAppId(hzBankSupplyPurchaseRetryRequest.getThirdOrderNum(), app.getId());
        // 查询商品相关信息
        AppItemDto appItemDto = remoteAppItemGoodsService.find(duiBaSupplyOrdersDto.getAppItemId()).getResult();
        //目前只支持虚拟商品。后续需要支持话费
        if (!StringUtils.equals(ItemDto.TypeVirtual, appItemDto.getType())) {
            throw new BizException(ErrorCode.E1000038.getDesc()).withCode(ErrorCode.E1000038.getCode());
        }
        if (StringUtils.equals(ItemDto.TypeVirtual, appItemDto.getType())) {
            SupplyOrderInfoRequest supplyOrderInfoRequest = new SupplyOrderInfoRequest();
            supplyOrderInfoRequest.setSupplyOrderNum(duiBaSupplyOrdersDto.getSupplyOrderNum());
            if (StringUtils.isNotBlank(hzBankSupplyPurchaseRetryRequest.getAccount())) {
                supplyOrderInfoRequest.setRechargeAccount(hzBankSupplyPurchaseRetryRequest.getAccount());
            }
            JSONObject jsonObject = new JSONObject();
            if (StringUtils.isNotBlank(duiBaSupplyOrdersDto.getExpressInfo())) {
                jsonObject = JSON.parseObject(duiBaSupplyOrdersDto.getExpressInfo());
            }
            Integer retry = jsonObject.getInteger(RETRY);
            if (Objects.isNull(retry)) {
                retry = 0;
            }
            jsonObject.put(RETRY, ++retry);
            supplyOrderInfoRequest.setExpressInfo(jsonObject.toJSONString());
            remoteDuiBaSupplyOrderService.retrySupplierOrder(duiBaSupplyOrdersDto.getSupplyOrderNum());
            remoteDuiBaSupplyOrderService.updateOrderInfo(supplyOrderInfoRequest);
        }
    }



    private String generateRedisKey(Long appId, Integer pageNo, Integer pageSize, List<Long> ids, String type, String appItemName) {
        String idsStr = "";
        if(CollectionUtils.isNotEmpty(ids)){
            for (Long id : ids) {
                idsStr += id + "-";
            }
        }
        return RedisKeyFactory.K001.toString() + appId + "_" + pageNo + "_" + pageSize + "_" + type + "_" + idsStr + "_" + appItemName;
    }

    private String generateRedisKeyToHsbc(Long appId, Integer pageNo, Integer pageSize, String ids, String type, String appItemName) {
        return RedisKeyFactory.K001.toString() + appId + "_" + pageNo + "_" + pageSize + "_" + type + "_" + ids + "_" + appItemName;
    }

    private String generateQueryStockKey(List<Long> appItemIds, Long appId) {
        return RedisKeyFactory.K002.toString() + appId + "_" + LocalCacheUtil.generateKeyByIds(appItemIds);
    }

    private String generateSpuDetailRedisKey(Long appItemId) {
        return RedisKeyFactory.K003.toString() + appItemId;
    }


    private void handleCustomLogic(SupplyPurchaseRequest request) throws BizException {
        OppoVO oppoVO = commonConfig.getOppoVO(request.getAppKey());
        if (oppoVO != null) {
            try {
                request.setPhone(decryptOppoAddress(request.getPhone(), oppoVO.getAesKey()));
                request.setName(decryptOppoAddress(request.getName(), oppoVO.getAesKey()));
                request.setAddress(decryptOppoAddress(request.getAddress(), oppoVO.getAesKey()));
                request.setProvince(decryptOppoAddress(request.getProvince(), oppoVO.getAesKey()));
                request.setCity(decryptOppoAddress(request.getCity(), oppoVO.getAesKey()));
                request.setDistrict(decryptOppoAddress(request.getDistrict(), oppoVO.getAesKey()));
                request.setStreet(decryptOppoAddress(request.getStreet(), oppoVO.getAesKey()));
            } catch (Exception e) {
                LOGGER.info("oppo decrypt error:{}", JSONObject.toJSONString(request), e);
                throw new BizException("oppo解密字段报错");
            }
        }
    }

    private void handleCustomLogic(SupplyPurchaseRequest request, AppSimpleDto app) throws BizException {
        // appId = 72004 定制 拦截截偏远地区下单
        if (Objects.equals(72004L, app.getId()) && checkRemoteRegion(request)) {
            throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
        }
        if (xianDouConfig.getAppIdSet().contains(app.getId())) {
            //去掉后缀，如12121-11 去掉"-"及其后面的，存一个映射关系，管理后台可以根据无后缀的订单号进行查询
            String thirdOrderNum = request.getThirdOrderNum();
            int index = thirdOrderNum.indexOf("-");
            if (index > 0) {
                hBaseService.upsertKStrV(XIAN_DOU_SUPPLY_PREFIX + thirdOrderNum.substring(0, index), thirdOrderNum);
            }
        }
        if (chongQingYouChuConfig.getAppIds().contains(app.getId()) && StringUtils.isNotBlank(request.getExtraInfo())) {
            try {
                request.setExtraInfo(URLDecoder.decode(request.getExtraInfo(), "UTF-8"));
                LOGGER.info("定制逻辑，appId:{}，解码逻辑，解码后:{}", app.getId(), request.getExtraInfo());
            } catch (UnsupportedEncodingException e) {
                LOGGER.warn("");
            }
        }
    }


    private String decryptOppoAddress(String content, String key) throws Exception {
        if (StringUtils.isNotBlank(content)) {
            return AESCS7Util.AES256Decrypt(content, key);
        }

        return content;
    }



    private boolean isDuXiaoMan(SupplyPurchaseRequest request) {
        if (StringUtils.isNotBlank(request.getKey()) && StringUtils.isNotBlank(request.getEncryptParam())) {
            return true;
        }
        return false;
    }


    private SupplyPurchaseRequest customParseRequest(SupplyPurchaseRequest request) throws BizException {
        if (StringUtils.isNotBlank(request.getKey()) && StringUtils.isNotBlank(request.getEncryptParam())) {
            //如果key和encryptParam这两个字段均不为空，则走度小满定制
            String aesKey;
            try {
                String key = request.getKey();
                aesKey = RSAUtils.decrypt(duXiaoManConfig.getRsaPrivateKey(), key);
            } catch (Exception e) {
                LOGGER.info("兑吧采购单-度小满下单解密异常，request={}", JSON.toJSONString(request), e);
                throw new BizException(ErrorCode.E1002301.getDesc()).withCode(ErrorCode.E1002301.getCode());
            }
            String param;
            try {
                String encryptParam = request.getEncryptParam();
                param = AESUtils.decrypt(encryptParam, aesKey);
            } catch (Exception e) {
                LOGGER.info("兑吧采购单-度小满下单解密异常，request={}", JSON.toJSONString(request), e);
                throw new BizException(ErrorCode.E1002301.getDesc()).withCode(ErrorCode.E1002301.getCode());
            }
            request = JSON.parseObject(param, SupplyPurchaseRequest.class);
        }
        LOGGER.info("度小满下单请求 = {}",JSON.toJSONString(request));
        return request;
    }

    private void verifyAreaLimit(SupplyPurchaseRequest request, AppSimpleDto app) throws BizException {
        if (StringUtils.isBlank(request.getProvince()) && StringUtils.isBlank(request.getCity())
                && StringUtils.isBlank(request.getDistrict())) {
            return;
        }
        ItemKeyDto itemKey = goodsItemDataService.getItemKeyByAppItemIdAndItemId(request.getAppItemId(), null, app);
        List<AddrLimitDto> result = remoteAddrLimitService.findAddrLimitByGidApi(GoodsTypeEnum.DUIBA, itemKey.getItem().getId()).getResult();
        if (org.springframework.util.CollectionUtils.isEmpty(result)) {
            return;
        }
        boolean flag = result.stream().anyMatch(e ->
                StringUtils.contains(request.getProvince(), e.getProvince()) || StringUtils.contains(request.getCity(), e.getProvince())
                        || StringUtils.contains(request.getDistrict(), e.getProvince()));
        //只要入参的省市区任意一个中包含地域限制列表省份的关键字就报异常
        if (flag) {
            LOGGER.info("兑吧采购单地域限制:{}", JSON.toJSONString(request));
            throw new BizException(ErrorCode.E1100021.getDesc()).withCode(ErrorCode.E1100021.getCode());
        }
    }


    private void collectAppIdInfo(Long appId) {
        try {
            //添加redis集合
            redisTemplate.opsForSet().add(getKey(), String.valueOf(appId));
        } catch (Exception e) {
            LOGGER.info("【采购接口】appId存进redis失败", e);
        }
    }

    private String getKey() {
        return "PURCHASE" + "_" + "appId" + "_" + "collect";

    }

    private void checkPermission(Long appId) throws BizException {
        //白名单开关, 默认关闭状态
        if (supplyConfig.getPurchaseWhiteListSwitch()) {
            List<String> permitAppList = WhiteAccessUtil.selectWhiteListConfig(PURCHASE_WHITE_LIST_CODE);
            if (!permitAppList.contains(String.valueOf(appId))) {
                //如果当前调用接口的app不在业务白名单，则直接返回异常信息
                throw new BizException(ErrorCode.E1100023.getDesc()).withCode(ErrorCode.E1100023.getCode());
            }
        }

        List<VerisonAppListInfoDto.ListBean> permitList = remoteAuthorityService.listByAppIdAndAttributeType(appId, 1);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(permitList)
                && VersionStatusEnum.END.getValue().equals(permitList.get(0).getStatus())) {
            throw new BizException(ErrorCode.E1100023.getDesc()).withCode(ErrorCode.E1100023.getCode());
        }

    }

    private AppSimpleDto checkSignAndGetApp(SupplyBaseRequest request) throws BizException {
        String appKey = request.getAppKey();
        if (StringUtils.isBlank(appKey)) {
            throw new BizException(ErrorCode.E1100001.getDesc()).withCode(ErrorCode.E1100001.getCode());
        }
        if (StringUtils.isBlank(request.getSign())) {
            throw new BizException(ErrorCode.E1100014.getDesc()).withCode(ErrorCode.E1100014.getCode());
        }
        if (StringUtils.isBlank(request.getTimestamp())) {
            throw new BizException(ErrorCode.E1100015.getDesc()).withCode(ErrorCode.E1100015.getCode());
        }
        if (System.currentTimeMillis() - Long.parseLong(request.getTimestamp()) > 5 * 60 * 1000L) {
            throw new BizException(ErrorCode.E1100016.getDesc()).withCode(ErrorCode.E1100016.getCode());
        }
        // 查询app信息
        AppSimpleDto app = developerCacheService.getAppByAppKey(appKey);
        if (app == null) {
            // appKey错误
            throw new BizException(ErrorCode.E1100002.getDesc()).withCode(ErrorCode.E1100002.getCode());
        }
        // 签名验证
        Map<String, String> map = JSON.parseObject(JSON.toJSONString(request)).getInnerMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().toString()));
        map.remove("skipSign");
        if (!SpringEnvironmentUtils.isTestEnv() && !SignTool.signVerify(app.getAppSecret(), map)) {
            throw new BizException(ErrorCode.E1100003.getDesc()).withCode(ErrorCode.E1100003.getCode());
        }
        return app;
    }

    /**
     * 验证请求参数，验证失败直接抛出异常
     *
     * @param request
     * @return
     */
    private AppSimpleDto checkSignAndGetApp(SupplyPurchaseRequest purchaseRequest,HttpServletRequest request, Boolean skipSign) throws BizException {
        String appKey = request.getParameter(APPKEY_KEY) == null ?  purchaseRequest.getAppKey(): request.getParameter(APPKEY_KEY);
        if (StringUtils.isBlank(appKey)) {
            throw new BizException(ErrorCode.E1100001.getDesc()).withCode(ErrorCode.E1100001.getCode());
        }
        if (Boolean.FALSE.equals(skipSign) && StringUtils.isBlank(request.getParameter(SIGN_KEY))) {
            throw new BizException(ErrorCode.E1100014.getDesc()).withCode(ErrorCode.E1100014.getCode());
        }
        if (Boolean.FALSE.equals(skipSign) && StringUtils.isBlank(request.getParameter(TIMESTAMP_KEY))) {
            throw new BizException(ErrorCode.E1100015.getDesc()).withCode(ErrorCode.E1100015.getCode());
        }
        if (Boolean.FALSE.equals(skipSign) && System.currentTimeMillis() - Long.parseLong(request.getParameter(TIMESTAMP_KEY)) > 5 * 60 * 1000L) {
            throw new BizException(ErrorCode.E1100016.getDesc()).withCode(ErrorCode.E1100016.getCode());
        }
        // 查询app信息
        AppSimpleDto app = developerCacheService.getAppByAppKey(appKey);
        if (app == null) {
            // appKey错误
            throw new BizException(ErrorCode.E1100002.getDesc()).withCode(ErrorCode.E1100002.getCode());
        }
        // 是否需要跳过签名验证
        if(Objects.nonNull(skipSign) && skipSign) {
            return app;
        }
        // 签名验证
        //测试 暂时不签名
        if (!SignTool.signVerify(app.getAppSecret(), request)) {
            throw new BizException(ErrorCode.E1100003.getDesc()).withCode(ErrorCode.E1100003.getCode());
        }

        return app;
    }

    /**
     * 偏远地区
     */
    private boolean checkRemoteRegion(SupplyPurchaseRequest request) {
        String province = Optional.ofNullable(request.getProvince()).orElse("");
        return province.contains("新疆") || province.contains("西藏");
    }


    private String getPurchaseKey(AppSimpleDto app, SupplyPurchaseRequest request) {
        return RedisKeyFactory.K604.toString()+"_"+app.getId()+"_"+request.getThirdOrderNum();
    }
}
