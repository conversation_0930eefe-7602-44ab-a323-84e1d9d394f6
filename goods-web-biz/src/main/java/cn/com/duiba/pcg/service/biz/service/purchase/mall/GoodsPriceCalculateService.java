package cn.com.duiba.pcg.service.biz.service.purchase.mall;


import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;

import java.util.List;

/**
 * 计算订单中商品最终价格
 *
 * <AUTHOR>
 * @date 2018/7/3 10:50
 */
public interface GoodsPriceCalculateService {


    /**
     * 计算订单中商品最终价格
     *
     * @param itemsNumList
     * @return cn.com.duiba.mall.web.biz.vo.shop.GoodsPriceBO
     */
    GoodsPriceBO goodsPriceDetailList(List<GoodsNumBO> itemsNumList, List<ItemKeyDto> itemKeyDtos,Long appId);



    /**
     * 计算运费
     * 1.取商品列表中运费最大值
     */
    long getMaxExPrise(List<ItemKeyDto> itemKeyDtos, ConsumerDto dto);


    /**
     * @Desc 此方法用户计算购物车中商品的价格,如果存在sku,则同一商品按照不同sku分开传入,不得进行统一处理
     * @param itemsNumList
     * @param dto
     * @param itemKeyDtos  itemsNumList中的appItemId对应，为了少查一次.上层保证itemKeyDtos的合法性(不能为null等等）
     * @return
     */
    GoodsPriceBO goodsPriceDetailListAndExPrice(List<GoodsNumBO> itemsNumList, ConsumerDto dto, List<ItemKeyDto> itemKeyDtos);

    /**
     * 获取超过库存的itemKeyDto
     * 1.返回null表示没有超过库存
     */
    ItemKeyDto getExceedGoodsStock(List<GoodsNumBO> itemsNumList, List<ItemKeyDto> itemKeyDtos, Long appId);

    /**
     * 校验商品库存
     */
    boolean judgeGoodsStock(List<GoodsNumBO> itemsNumList, List<ItemKeyDto> itemKeyDtos, Long appId) throws BizException;


}
