package cn.com.duiba.pcg.service.biz.service.goodauth.impl;

import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.live.activity.center.api.dto.goodauth.LiveGoodAuthDto;
import cn.com.duiba.live.activity.center.api.remoteservice.goodauth.RemoteLiveGoodAuthService;
import cn.com.duiba.live.activity.center.api.util.NumberUtil;
import cn.com.duiba.pcg.service.biz.config.DuibaLivePayConfig;
import cn.com.duiba.pcg.service.biz.constants.LiveAuthConstant;
import cn.com.duiba.pcg.service.biz.service.goodauth.LiveGoodAuthService;
import cn.com.duiba.pcg.service.biz.service.goodauth.param.LiveGoodAuthParam;
import cn.com.duiba.pcg.service.biz.util.goodauth.AESUtils;
import cn.com.duiba.pcg.tool.duibalive.LiveUtils;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description
 * @Date 2022/5/20 11:30
 * <AUTHOR>
 */
@Service
public class LiveGoodAuthServiceImpl implements LiveGoodAuthService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LiveGoodAuthServiceImpl.class);

    @Resource
    private RemoteLiveGoodAuthService remoteLiveGoodAuthService;
    @Resource
    private RemoteConsumerExtraService remoteConsumerExtraService;
    @Resource
    private DuibaLivePayConfig duibaLivePayConfig;

    /**
     * 查询认证信息
     *
     * @return
     */
    @Override
    public Result<JSONObject> queryAuthStatus() {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        try {
            // 校验用户信息
            ConsumerExtraDto extraDto = remoteConsumerExtraService.findByConsumerId(consumer.getId()).getResult();
            if (extraDto == null || StringUtils.isBlank(extraDto.getJson())) {
                return ResultBuilder.fail(LiveAuthConstant.ERROR_10001);
            }
            // 认证状态
            boolean status = Boolean.TRUE;
            // 展示渠道认证
            boolean showAuth = Boolean.FALSE;

            JSONObject jsonObject = JSONObject.parseObject(extraDto.getJson());
            Boolean isAuth = jsonObject.getBoolean(LiveAuthConstant.GOOD_AUTH);
            // 指定直播appId且goodAuth=true才需要认证
            if (duibaLivePayConfig.isDuibaLiveApp(extraDto.getAppId()) && Objects.nonNull(isAuth) && isAuth) {
                LiveGoodAuthDto liveGoodAuthDto = remoteLiveGoodAuthService.findByPartnerUserIdAndAppId(LiveUtils.getPartnerUserId(consumer.getPartnerUserId()), consumer.getAppId());
                status = Objects.nonNull(liveGoodAuthDto);
                showAuth = Boolean.TRUE;
            }
            JSONObject result = new JSONObject();
            result.fluentPut("showAuth", showAuth)
                    .fluentPut("status", status);
            return ResultBuilder.success(result);
        } catch (Exception e) {
            LOGGER.warn("{} 查询认证异常, consumerId:{} ", LiveAuthConstant.LOG_PREFIX, consumer.getId(), e);
            return ResultBuilder.fail(LiveAuthConstant.NETWORK_ERROR);
        }
    }


    /**
     * 保存认证信息
     *
     * @param param
     * @return
     */
    @Override
    public Result<Boolean> saveAuthInfo(LiveGoodAuthParam param) {
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        // 校验身份证
        String idCard = validIdCard(param.getIdCard(), consumer.getId());
        if (StringUtils.isBlank(idCard)) {
            return ResultBuilder.fail(LiveAuthConstant.ERROR_10000);
        }
        // 校验手机号
        if (StringUtils.isBlank(param.getPhone()) || param.getPhone().length() != LiveAuthConstant.PHONE_LENGTH) {
            return ResultBuilder.fail(LiveAuthConstant.ERROR_10000);
        }
        // 校验姓名
        if (StringUtils.isBlank(param.getUsername()) ||
                param.getUsername().length() < LiveAuthConstant.NAME_MIN_LENGTH ||
                param.getUsername().length() > LiveAuthConstant.NAME_MAX_LENGTH) {
            return ResultBuilder.fail(LiveAuthConstant.ERROR_10001);
        }

        try {
            // 校验用户信息
            ConsumerExtraDto extraDto = remoteConsumerExtraService.findByConsumerId(consumer.getId()).getResult();
            if (extraDto == null || StringUtils.isBlank(extraDto.getJson())) {
                return ResultBuilder.fail(LiveAuthConstant.ERROR_10003);
            }
            JSONObject jsonObject = JSONObject.parseObject(extraDto.getJson());
            Boolean isAuth = jsonObject.getBoolean(LiveAuthConstant.GOOD_AUTH);

            // 指定直播appId且goodAuth=true才需要认证
            if (duibaLivePayConfig.isDuibaLiveApp(extraDto.getAppId()) && Objects.nonNull(isAuth) && isAuth) {
                // 校验是否已认证
                LiveGoodAuthDto one = remoteLiveGoodAuthService.findByPartnerUserIdAndAppId(LiveUtils.getPartnerUserId(consumer.getPartnerUserId()), consumer.getAppId());
                if (Objects.nonNull(one)) {
                    return ResultBuilder.fail(LiveAuthConstant.ERROR_10004);
                }
                Long number = remoteLiveGoodAuthService.insert(buildLiveGoodAuthDto(extraDto.getConsumerId(), extraDto.getAppId(),
                        LiveUtils.getPartnerUserId(extraDto.getPartnerUserId()), idCard, buildExtJson(param)));
                return ResultBuilder.success(NumberUtil.isNotNullOrLteZero(number));
            }
            return ResultBuilder.fail(LiveAuthConstant.ERROR_1005);
        } catch (Exception e) {
            LOGGER.warn("{} 新增认证异常, consumerId:{}, param:{}", LiveAuthConstant.LOG_PREFIX, consumer.getId(), JSONObject.toJSONString(param), e);
            return ResultBuilder.fail(LiveAuthConstant.NETWORK_ERROR);
        }
    }


    /**
     * 校验身份证
     *
     * @param idCard
     * @param consumerId
     * @return
     */
    public String validIdCard(String idCard, Long consumerId) {
        try {
            idCard = AESUtils.decrypt(idCard, LiveAuthConstant.PRIVATE_KEY);
        } catch (Exception e) {
            LOGGER.warn("{} 解密异常 consumerId:{} idCard:{}", LiveAuthConstant.LOG_PREFIX, consumerId, idCard, e);
            return null;
        }
        if (IdcardUtil.isValidCard(idCard)) {
            return idCard;
        }
        return null;
    }

    /**
     * 构建扩展json
     *
     * @param param
     * @return
     */
    public String buildExtJson(LiveGoodAuthParam param) {
        JSONObject json = new JSONObject();
        json.put("username", param.getUsername());
        json.put("phone", param.getPhone());
        return JSONObject.toJSONString(json);
    }

    /**
     * 构建LiveGoodAuthDto
     *
     * @param consumerId
     * @param appId
     * @param partnerUserId
     * @param idCard
     * @param json
     * @return
     */
    private LiveGoodAuthDto buildLiveGoodAuthDto(Long consumerId, Long appId, Long partnerUserId, String idCard, String json) {
        LiveGoodAuthDto liveGoodAuthDto = new LiveGoodAuthDto();
        liveGoodAuthDto.setConsumerId(consumerId);
        liveGoodAuthDto.setAppId(appId);
        liveGoodAuthDto.setPartnerUserId(partnerUserId);
        liveGoodAuthDto.setIdCard(idCard);
        liveGoodAuthDto.setExtInfo(json);
        return liveGoodAuthDto;
    }
}
