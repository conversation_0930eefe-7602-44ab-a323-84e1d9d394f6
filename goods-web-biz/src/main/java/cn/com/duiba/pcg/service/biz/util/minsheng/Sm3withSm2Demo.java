/**
 * 
 */
package cn.com.duiba.pcg.service.biz.util.minsheng;

import cn.com.duiba.pcg.service.biz.util.minsheng.util.EasyGmUtils;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;

import java.security.KeyPair;

/**
 * 民生测试国密验签公钥:04D6DFEA13F90243AF76516C951777CB35504D4A3737C075B6220DBAE4D1BB6E2E10E859647CB51EC3F6053DC530E708654182ECD0648F9148FB17E963D8263B6E
 *
 * @title: Sm3withSm2Demo
 * <AUTHOR>
 * @date 2022年8月29日 上午10:29:14
 */
public class Sm3withSm2Demo {

	public static void main(String[] args) throws Exception {

		// 密钥对象，用来获取公私钥对
		KeyPair kp = EasyGmUtils.getKeyPair();
		// 公私钥16进制字符串,可以存储到数据库.
		String privateKeyStr = EasyGmUtils.getPrivateKey(kp);
		String publicKeyStr = EasyGmUtils.getPublicKey(kp);

		System.out.println("私钥:----" + privateKeyStr);

		System.out.println("公钥----" + publicKeyStr);

		// 获取密钥字节数组方式1
		// byte[] privatekeyBytes1=((BCECPrivateKey)kp.getPrivate()).getD().toByteArray();
		// byte[] publicKeyBytes1=((BCECPublicKey)kp.getPublic()).getQ().getEncoded(false);
		// 获取密钥字节数组方式2
		byte[] privateKeyBytes2 = Hex.decode(privateKeyStr);
		byte[] publicKeyBytes2 = Hex.decode(publicKeyStr);

		// 待签名字符串
		String msg = "1234567";
		// 使用私钥字节数组进行签名
		byte[] signBytes = EasyGmUtils.signSm3WithSm2(msg.getBytes(), privateKeyBytes2);
		// 使用privateKey对象进行签名
		// byte[] signBytes = EasyGmUtils.signSm3WithSm2(msg.getBytes(),
		// userId.getBytes(), kp.getPrivate());

		// 返回的是base64编码的签名数据
		String strbase64 = new String(Base64.encode(signBytes));
		System.out.println(strbase64);

		// 验签时,对签名串做base64解码.
		byte[] verifyBytes = Base64.decode(strbase64);
		// 使用公钥字节数组进行验签
		boolean verified = EasyGmUtils.verifySm3WithSm2(msg.getBytes(), verifyBytes,
				publicKeyBytes2);

		// 使用publicKey对象进行验签
		// boolean verified = EasyGmUtils.verifySm3WithSm2(msg.getBytes(),
		// userId.getBytes(), verifyBytes, kp.getPublic());
		System.out.println(verified);

	}

}
