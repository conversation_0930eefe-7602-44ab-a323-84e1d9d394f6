package cn.com.duiba.pcg.service.biz.service.abc;


import cn.com.duiba.pcg.service.biz.config.cgb.AbcFastETConfig;
import cn.com.duiba.pcg.service.biz.dto.abc.AbcClientConfigParams;
import cn.com.duiba.pcg.service.biz.dto.abc.AccessTokenBean;
import cn.com.duiba.pcg.service.biz.dto.abc.BaseResponse;
import cn.com.duiba.pcg.service.biz.dto.abc.UserInfoBean;
import cn.com.duiba.pcg.tool.HttpClientSSLUtil;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.SignatureException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeSet;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/5/22 09:58
 */
@Service
public class AbcAuthServiceImpl implements AbcAuthService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AbcAuthServiceImpl.class);

    @Autowired
    private HttpClientSSLUtil httpClientUtil;
    @Autowired
    private AbcFastETConfig abcFastETConfig;


    @Override
    public String getToken(String code, AbcClientConfigParams client) {
        Map<String, String> param = Maps.newHashMap();
        param.put("grant_type", "authorization_code");
        param.put("client_id", abcFastETConfig.getClientId());
        param.put("client_secret", abcFastETConfig.getClientSecret());
        param.put("code", code);
        param.put("redirect_uri", abcFastETConfig.getRedirectUrl());
        String result = httpClientUtil.sendPost(abcFastETConfig.getTokenUrl(), param, false);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        AccessTokenBean accessToken = JSON.parseObject(result, AccessTokenBean.class);
        String resultToken=accessToken == null ? null : accessToken.getAccessToken();
        LOGGER.info("农行ABC快E通免登Auth获取token={}",resultToken);
        return resultToken;
    }


    @Override
    public UserInfoBean getUserInfoNew(String token,AbcClientConfigParams params) {
        //头部
        BasicHeader[] headers = new BasicHeader[2];
        headers[0] = new BasicHeader("Authorization", "Bearer " + token);
        headers[1] = new BasicHeader("Content-Type", "application/json;charset=utf-8");
        JSONObject paraJson = new JSONObject();
        paraJson.put("appid",params.getClientId());
        paraJson.put("biz_data","");
        paraJson.put("sign_type","SHA256");
        paraJson.put("encrypt_data",null);
        paraJson.put("timestamp", DateUtils.getSecondStr(new Date()));
        paraJson.put("encrypt_type",null);
        paraJson.put("nonce", UUID.randomUUID().toString().replace("-", ""));
        String plainTxt=getSignPlainText(paraJson);
        //对拼装明文进行签名
        String sign = signData(plainTxt,params.getPrivateKey());
        if(StringUtils.isBlank(sign)){
            LOGGER.info("农行ABC快E通免登Auth new sign为空,params:{}",JSON.toJSONString(params));
            return null;
        }
        paraJson.put("sign",sign);
        LOGGER.info("农行ABC快E通免登Auth new请求参数={}",paraJson.toJSONString());
        String result = httpClientUtil.sendPostJsonWithHeaders(abcFastETConfig.getUserInfoNew(),paraJson.toJSONString(),headers, false);
        if (StringUtils.isBlank(result)) {
            return null;
        }
        JSONObject resJson = JSON.parseObject(result);
        LOGGER.info("农行ABC快E通免登Auth new第三方请求结果={}",resJson);
        if(!"0000".equals(resJson.getString("code"))){
            LOGGER.error("农行ABC快E通免登Auth new请求第三方接口异常params:{}",JSON.toJSONString(params));
            return null;
        }
        String user = resJson.getString("biz_content");
        if(StringUtils.isBlank(user)){
            return null;
        }
        UserInfoBean userInfo = JSON.parseObject(user, UserInfoBean.class);
        LOGGER.info("农行ABC快E通免登Auth new获取userInfo={}",JSON.toJSONString(userInfo));
        return baseCheck(userInfo) ? userInfo : null;
    }


    /**
     * getSignPlainText 拼接报文明文-用于请求报文
     * @param msg
     * @return String
     */
    private String getSignPlainText(JSONObject msg) {
        Map<String, String> map = new HashMap<String, String>();
        StringBuilder signPlainText = new StringBuilder();
        for (Map.Entry<String, Object> pi : msg.entrySet()) {
            if (pi != null && pi.getValue() != null && !pi.getValue().equals("") && !pi.getKey().trim().toLowerCase().equals("sign") &&
                    !pi.getKey().trim().toLowerCase().equals("code") && !pi.getKey().trim().toLowerCase().equals("msg")) {
                map.put(pi.getKey(), pi.getValue().toString());
            }
        }
        TreeSet<String> treeSet = new TreeSet<String>();
        for (String key : map.keySet()) {
            treeSet.add(key);
        }

        for (String key : treeSet) {
            signPlainText.append(map.get(key) + "@");
        }

        if (signPlainText.length() - 1 >= 0) {
            signPlainText.deleteCharAt(signPlainText.length() - 1);
        }
        return signPlainText.toString();
    }


    private boolean baseCheck(BaseResponse baseResponse) {
        return "0000".equals(baseResponse.getRetCode());
    }

    /**
     * 签名方法
     * @param plainTxt 明文
     * @param privateKey
     * <AUTHOR>
     */
    public String signData(String plainTxt, PrivateKey privateKey) {
        try {
            byte[] plainTxtBytes = plainTxt.getBytes("UTF-8");

            // 实例化Signature
            Signature signature = Signature.getInstance("SHA256withRsa");
            // 初始化Signature
            signature.initSign(privateKey);
            // 更新
            signature.update(plainTxtBytes);
            byte[] result = signature.sign();
            return Base64.encodeBase64String(result);
        } catch (UnsupportedEncodingException | SignatureException | NoSuchAlgorithmException | InvalidKeyException e) {
            LOGGER.error("农行ABC快E通免登Auth 签名加密错误",e);
            return null;
        }
    }
}
