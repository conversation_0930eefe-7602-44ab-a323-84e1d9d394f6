package cn.com.duiba.pcg.service.biz.config;

import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import com.google.common.collect.Sets;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

@Configuration
@ConfigurationProperties(prefix = "gaw.app.domain")
@Data
public class CustomPayDomainConfig {

    /**
     * 应用id集合
     */
    private Set<Long> appIdSet = Sets.newHashSet();


    private String pdoamin =  "https://activity-10.m.duiba.com.cn";

    /**
     *  支付专用跳转域名
     */
    private String payUrlDomain = "https://10-activity.dexfu.cn";



    /**
     *  支付专用跳转域名
     */
    private String newPayUrlDomain = "https://10-activity.dexfu.cn";


    /**
     * 定制支付宝跳转到订单结果页的appid
     */
    private Set<Long> payResultAppIds = Sets.newHashSet(89644L);


    //根据 appid 获取对应的支付域名
    public String getPayDomainByAppId(Long appId) {
        if(WhiteAccessUtil.matchWhiteList(appId,"DZZYLYM_liukai")){
            
            return pdoamin;
        }

        if(WhiteAccessUtil.matchWhiteList(appId,"ZFYMZXDBZTYM_liukai")){
            return newPayUrlDomain;
        }

        return payUrlDomain;
    }
}
