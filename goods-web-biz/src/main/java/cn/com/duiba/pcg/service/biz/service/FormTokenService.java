package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.pcg.constant.MemcachedSpace;
import cn.com.duiba.pcg.tool.FileIDGenerator;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 表单token操作服务
 * 
 * <AUTHOR>
 */
@Service
public class FormTokenService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FormTokenService.class);

    private static final String TEST_TKEN = "yrJRr7Cddp2YeQd";

    @Resource(name = "redisTemplate")
    private AdvancedCacheClient advancedCacheClient;

    /**
     * 验证token并进行失效操作
     * 
     * @param cid
     * @param token
     * @return result
     */
    public boolean checkAndInvalidConsumerToken(Long cid, String token) {

        if (token == null) {
            LOGGER.info("客户端token已失效, cid={}, token={}", cid, token);
            return false;
        }
        // 如果是测试的token，那么默认可用
        if (TEST_TKEN.equals(token)) {
            return true;
        }
        String memToken = (String) advancedCacheClient.get(getConsumerKey(cid));
        if (memToken == null) {
            LOGGER.info("缓存token已失效, cid={}, memToken={}", cid, memToken);
            return false;
        }
        advancedCacheClient.remove(getConsumerKey(cid));
        if (memToken.equals(token)) {
            return true;
        }
        LOGGER.info("token不一致已失效, cid={}, token={}, memToken={}", cid, token, memToken);
        return false;
    }

    private String getConsumerKey(Long cid) {
        return MemcachedSpace.MS_FORM_CONSUMER_TOKEN + "-" + cid;
    }

    /**
     * 获取商城用户的form token
     * 
     * @param cid
     * @return token
     */
    public String getConsumerToken(Long cid) {
        String token = (String) advancedCacheClient.get(getConsumerKey(cid));
        if (token == null) {
            token = generate();
            advancedCacheClient.set(getConsumerKey(cid), token, 600, TimeUnit.SECONDS);
        }
        return token;
    }

    private String generate() {
        int length = new Random().nextInt(5) + 5;
        return FileIDGenerator.getRandomString(length);
    }

}
