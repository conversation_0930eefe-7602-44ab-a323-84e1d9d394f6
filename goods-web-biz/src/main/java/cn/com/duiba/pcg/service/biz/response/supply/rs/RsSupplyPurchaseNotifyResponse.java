package cn.com.duiba.pcg.service.biz.response.supply.rs;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 荣数下单异步通知参数
 * <AUTHOR>
 * @date 2023/7/26 16:03
 */
@Data
public class RsSupplyPurchaseNotifyResponse implements Serializable {

    private static final long serialVersionUID = -5871514596185683343L;

    /**
     * 处理结果
     * 成功-SUCCESS、失败-FAIL
     */
    private String status;

    /**
     * 响应码
     * 00000-处理成功
     * 00001-处理中
     * 20001-微信立减金预算不足（处理失败）
     * 其他（商户侧自定义）-处理失败
     */
    private String code;

    /**
     * 处理失败原因
     * 处理失败原因，同订单失败原因，成功时为空
     */
    private String errMessage;

    /**
     * 商户订单号
     * 商户侧订单流水号
     */
    private String vendorOrderNo;

    /**
     * 券信息
     * 只有发券才有值
     */
    private List<RsVoucherInfo> voucherInfo;

}
