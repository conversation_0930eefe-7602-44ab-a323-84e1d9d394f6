package cn.com.duiba.pcg.service.biz.dto.lt.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class LtReverseOrderResponse extends LtBaseResponse{
    /**
     * 不支持作废
     */
    public final static String TASCNRO = "TASCNRO";
    /**
     * 订单不存在
     */
    public final static String NORECORD = "NORECORD";
    /**
     * 商家系统订单号，唯一、不可重复、不可空
     */
    @JsonProperty("order_card")
    private String orderCard;

    /**
     * 卡号（唯一，不可重复）(如无卡号，可空)
     */
    @JsonProperty("card_id")
    private String cardId;


}
