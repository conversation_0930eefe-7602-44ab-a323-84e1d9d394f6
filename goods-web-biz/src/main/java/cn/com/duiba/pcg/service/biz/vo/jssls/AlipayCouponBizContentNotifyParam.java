package cn.com.duiba.pcg.service.biz.vo.jssls;

/**
 * <AUTHOR>
 * @date 2023-06-13 10:58
 */
public class AlipayCouponBizContentNotifyParam {

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 优惠券ID
     */
    private String voucherId;

    /**
     * 领取用户ID
     */
    private String userId;

    /**
     * 核销时间（Unix时间戳，毫秒）
     * 1624932891990
     */
    private String useTime;

    /**
     * 核销金额（分）
     */
    private String useAmount;

    /**
     * 券消息类型，例如券核销（V_USE）
     */
    private String bizType;

    /**
     * 退款时间（Unix时间戳，毫秒）
     * 1624932891990
     */
    private String refundTime;

    /**
     * 退款金额（分）
     */
    private String refundAmount;

    /**
     * 券状态，可用（ENABLED）/不可用（DISABLED）
     */
    private String voucherStatus;

    /**
     * 幂等ID
     */
    private String orderId;

    /**
     * 支付宝交易号
     */
    private String tradeNo;

    /**
     * 券领取时间（Unix时间戳，毫秒）
     */
    private String gmtVoucherCreate;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 券删除时间（Unix时间戳，毫秒）
     */
    private String deleteTime;

    /**
     * 发放金额（分）
     */
    private String publishAmount;

    /**
     * 券过期时间（Unix时间戳，毫秒）
     */
    private String expireTime;

    public String getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(String deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getPublishAmount() {
        return publishAmount;
    }

    public void setPublishAmount(String publishAmount) {
        this.publishAmount = publishAmount;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getVoucherId() {
        return voucherId;
    }

    public void setVoucherId(String voucherId) {
        this.voucherId = voucherId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUseTime() {
        return useTime;
    }

    public void setUseTime(String useTime) {
        this.useTime = useTime;
    }

    public String getUseAmount() {
        return useAmount;
    }

    public void setUseAmount(String useAmount) {
        this.useAmount = useAmount;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(String refundTime) {
        this.refundTime = refundTime;
    }

    public String getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(String refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getVoucherStatus() {
        return voucherStatus;
    }

    public void setVoucherStatus(String voucherStatus) {
        this.voucherStatus = voucherStatus;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getGmtVoucherCreate() {
        return gmtVoucherCreate;
    }

    public void setGmtVoucherCreate(String gmtVoucherCreate) {
        this.gmtVoucherCreate = gmtVoucherCreate;
    }
}
