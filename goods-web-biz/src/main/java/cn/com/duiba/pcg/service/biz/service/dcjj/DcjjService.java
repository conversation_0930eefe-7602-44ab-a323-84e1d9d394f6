package cn.com.duiba.pcg.service.biz.service.dcjj;

import cn.com.duiba.api.enums.ItemTypeEnums;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.credits.sdk.SignTool;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.thirdpartyvnew.api.dcjj.RemoteDcjjService;
import cn.com.duiba.thirdpartyvnew.dto.dcjj.DcjjDrawCallBackParam;
import cn.com.duiba.wolf.utils.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: huakai
 * @createTime: 2024/06/17 21:40
 * @description: todo
 * @version: 1.0
 */
@Service
@Slf4j
public class DcjjService {
    @Resource
    private RemoteDcjjService remoteDcjjService;

    public void failCallback(AppSimpleDto app,String type, String recordId,String failMessage) {
        log.info("大成基金订单推送 失败推送 type={} recordId={} failMessage={}",type,recordId,failMessage);
        if (StringUtils.isBlank(recordId)) {
            log.warn("大成基金订单推送 recordId为空");
            return;
        }
        DcjjDrawCallBackParam callBackParam = bulidDcjjDrawCallBackParam(app,type,recordId,failMessage);
        remoteDcjjService.drawCallBack(callBackParam);
    }

    /**
     * 构建推送对象
     */
    private DcjjDrawCallBackParam bulidDcjjDrawCallBackParam(AppSimpleDto app,String type,String recordId,String failMessage){
        DcjjDrawCallBackParam callBackParam = new DcjjDrawCallBackParam();
        // 大成基金领奖记录id
        callBackParam.setRecordId(recordId);
        // 回调类型 区分实物、充值、其他
        callBackParam.setCallBackType(getCallBackType(type));
        // 领奖状态
        callBackParam.setDrawStatus(DcjjDrawCallBackParam.FAIL);
        // 失败信息
        callBackParam.setMessage(failMessage);
        // 通用参数
        callBackParam.setTimestamp(System.currentTimeMillis());
        callBackParam.setAppKey(app.getAppKey());
        // 签名
        Map<String, String> signParam = BeanUtils.transBeanToMap(callBackParam)
                .entrySet()
                .stream()
                .filter(x -> x.getValue() != null)
                .collect(Collectors.toMap(Map.Entry::getKey, y -> y.getValue().toString()));
        callBackParam.setSign(SignTool.sign(signParam));
        return callBackParam;
    }
    /**
     * 获取回调类型
     * 1、下单成功；2，卡券充值；3，实物填写收货信息
     */
    private String getCallBackType(String type) {
        // 话费、支付宝、虚拟商品=>充值类
        if (StringUtils.equals(type, ItemTypeEnums.TypePhonebill.getName()) || StringUtils.equals(type, ItemTypeEnums.TypeAlipay.getName()) || StringUtils.equals(type, ItemTypeEnums.TypeVirtual.getName())) {
            return "1";
        }
        // 实物=>实物
        if (StringUtils.equals(type, ItemTypeEnums.TypeObject.getName())) {
            return "3";
        }
        return "1";
    }
}
