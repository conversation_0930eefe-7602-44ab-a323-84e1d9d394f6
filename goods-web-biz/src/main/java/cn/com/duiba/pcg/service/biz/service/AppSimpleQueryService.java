package cn.com.duiba.pcg.service.biz.service;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import cn.com.duiba.developer.center.api.domain.dto.DeveloperDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteDeveloperService;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.duiba.developer.center.api.domain.dto.RemainingMoneyDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteRemainingMoneyService;
import cn.com.duiba.pcg.constant.MemcachedSpace;
import cn.com.duiba.wolf.cache.AdvancedCacheClient;

import com.alibaba.fastjson.JSONObject;

/**
 * ClassName: AppSimpleQueryService <br/>
 * date: 2017年4月12日 下午5:50:04 <br/>
 *
 * @version 
 * @since JDK 1.6
 */
@Service("appSimpleQueryService")
public class AppSimpleQueryService {

    private Logger log    = LoggerFactory.getLogger(AppSimpleQueryService.class);

    @Autowired
	private RemoteRemainingMoneyService remainingMoneyDAO;
	@Resource(name="redisTemplate")
    private AdvancedCacheClient advancedCacheClient;
	@Autowired
    private RemoteDeveloperService remoteDeveloperService;
	@Autowired
    private cn.com.duiba.developer.center.api.remoteservice.RemoteAppExtraService remoteAppExtraServiceNew;

	private String getRemainingMoneyKey(Long developerId){
		return MemcachedSpace.MS_APPCACHE+"-rm-"+developerId;
	}

    private Cache<Long, DeveloperDto> developerCache = CacheBuilder.newBuilder().maximumSize(600).expireAfterWrite(60, TimeUnit.SECONDS).build();
    private Cache<Long, String> loginProgramCache = CacheBuilder.newBuilder().maximumSize(600).expireAfterWrite(60, TimeUnit.SECONDS).build();


    /**
	 * @param developerId
	 * @return RemainingMoneyDto
	 */
	public RemainingMoneyDto findRemainingMoneyByDeveloperIdWithCache(Long developerId) {
		RemainingMoneyDto rm=null;
		String json=(String)advancedCacheClient.get(getRemainingMoneyKey(developerId));
		if(json==null){
			rm=remainingMoneyDAO.getRemainingMoneyByDeveloperId(developerId).getResult();
			advancedCacheClient.set(getRemainingMoneyKey(developerId), JSONObject.toJSONString(rm),30, TimeUnit.SECONDS);
		}else{
			rm= JSONObject.toJavaObject(JSONObject.parseObject(json), RemainingMoneyDto.class);
		}
		return rm;
	}

    /**
     * 查询developerDto Cache
     * @param developerId
     * @return
     */
	public DeveloperDto findByDeveloperIdCache(final Long developerId){
        try {
            return developerCache.get(developerId, new Callable<DeveloperDto>() {
                @Override
                public DeveloperDto call() throws Exception {
                    return remoteDeveloperService.getDeveloperById(developerId).getResult();
                }
            });
        } catch (Exception e) {
            log.warn("findByDeveloperIdCache", e);
            return remoteDeveloperService.getDeveloperById(developerId).getResult();
        }
    }

    /**
     *
     * @param appId
     * @return
     */
    public String findCallLoginProgramByAppIdByCache(final Long appId){
        try {
            return loginProgramCache.get(appId, new Callable<String>() {
                @Override
                public String call() throws Exception {
                    return remoteAppExtraServiceNew.findCallLoginProgramByAppId(appId).getResult();
                }
            });
        } catch (Exception e) {
            log.warn("findCallLoginProgramByAppIdByCache",e);
            return remoteAppExtraServiceNew.findCallLoginProgramByAppId(appId).getResult();
        }
    }

}
