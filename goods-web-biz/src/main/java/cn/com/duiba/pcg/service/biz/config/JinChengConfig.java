package cn.com.duiba.pcg.service.biz.config;

import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 金城银行-接口配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "jincheng")
@Data
@Slf4j
public class JinChengConfig {

    /**
     * appIds
     */
    @Value("#{'${jincheng.appIds}'.split(',')}")
    private Set<Long> appIds = Sets.newHashSet(96599L);

    /**
     * 加密aesKey的rsa非对称密钥公钥
     * 测试：MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUwWAzrI+GfuaL9bM+11/ig4pGPn31WyFlTS54AzcRrMaN/t0gYrcFCEmm22cGxqmvJGUfOL9pBI9rOQQmiBx9KQ4tt8mUkmf+BtboMrr5E4InqLQ6DmlsAiKlQC4JeCK38n+NxUZfynLF0+W+QAWPm2lGsPLSyfMa045qdASy8wIDAQAB
     */
    private String rsaPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUwWAzrI+GfuaL9bM+11/ig4pGPn31WyFlTS54AzcRrMaN/t0gYrcFCEmm22cGxqmvJGUfOL9pBI9rOQQmiBx9KQ4tt8mUkmf+BtboMrr5E4InqLQ6DmlsAiKlQC4JeCK38n+NxUZfynLF0+W+QAWPm2lGsPLSyfMa045qdASy8wIDAQAB";

    /**
     * 验证签名-解密私钥
     * 测试：MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJuc89m5SmPmS4byHoYZiaNWx4brg55Sk/S4qMMoPNY5DK29UnalA+Xlvf6wHv0d/oGTavtI4d1xkHI5kI0TcV/V4Dqk4gc9pmpYmOiBn+eTQdDITLlwAy0E4hTg+mbL74YIor2rUrJ0RM5ajxZXwgoFTzcgxfASgU4Zgk1lZk+PAgMBAAECgYACxCZ9j1KoNfbPvkwWnCubMLa5SkR7swIYTpQJbBSwxjDYgc4Od9563rui5GSYoUjd6s3zC5egRR2JOka8i0TvpRe+w7+lsYYNR4TH/3oIbFvIi6WUhoAmbCp1k4ibe6m49xwen45IXP938isQnSakf6FlrDWuuoRJs8GVRsDysQJBAMtV0bbhEWByQD2Vs91JJwTPzWdllj288pp/9zvbZPX3wgqAe+u5D6lz5jOvvT6AGfvq5N7TTM+WA9zPMEq5NqkCQQDD6uh92DX9yBguViee1qoOgoPVVrqUyk9xAGMVzwRpzzHmmkt9CfEtvLPu1fJudhf4Rl6mSsSeVA8d336Yig93AkB/auvb2fX1piKzdNgdVvXogf+JR5N6ZKjyQDZBgB4oNHWX9yOwd2tlGIes4/eG7udZgbyiFW8QtkT9Xag5aw7hAkAOkIAIGvKokx95El6PqFWsDnfOfEwSk5ekRusWy/UACK8k/YNvIDYVuFmH/8+t9omBo3mV0ykavBUDm4HFxT2xAkEAsYaJ3EAf0OveRciTFg0U4LUm09YBTeVMwlLlowDvPXegPv65U0niiwUVMdb9j9h4I3ydZloNTWvgrnz6uM0KSw==
     */
    private String signPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJuc89m5SmPmS4byHoYZiaNWx4brg55Sk/S4qMMoPNY5DK29UnalA+Xlvf6wHv0d/oGTavtI4d1xkHI5kI0TcV/V4Dqk4gc9pmpYmOiBn+eTQdDITLlwAy0E4hTg+mbL74YIor2rUrJ0RM5ajxZXwgoFTzcgxfASgU4Zgk1lZk+PAgMBAAECgYACxCZ9j1KoNfbPvkwWnCubMLa5SkR7swIYTpQJbBSwxjDYgc4Od9563rui5GSYoUjd6s3zC5egRR2JOka8i0TvpRe+w7+lsYYNR4TH/3oIbFvIi6WUhoAmbCp1k4ibe6m49xwen45IXP938isQnSakf6FlrDWuuoRJs8GVRsDysQJBAMtV0bbhEWByQD2Vs91JJwTPzWdllj288pp/9zvbZPX3wgqAe+u5D6lz5jOvvT6AGfvq5N7TTM+WA9zPMEq5NqkCQQDD6uh92DX9yBguViee1qoOgoPVVrqUyk9xAGMVzwRpzzHmmkt9CfEtvLPu1fJudhf4Rl6mSsSeVA8d336Yig93AkB/auvb2fX1piKzdNgdVvXogf+JR5N6ZKjyQDZBgB4oNHWX9yOwd2tlGIes4/eG7udZgbyiFW8QtkT9Xag5aw7hAkAOkIAIGvKokx95El6PqFWsDnfOfEwSk5ekRusWy/UACK8k/YNvIDYVuFmH/8+t9omBo3mV0ykavBUDm4HFxT2xAkEAsYaJ3EAf0OveRciTFg0U4LUm09YBTeVMwlLlowDvPXegPv65U0niiwUVMdb9j9h4I3ydZloNTWvgrnz6uM0KSw==";


    public static final  String DUIBA_FUNCTION_PURCHASE = "duiba.purchase.purchase";

    public static final  String DUIBA_FUNCTION_PURCHASE_QUERY = "duiba.purchase.query";

    public static final  String DUIBA_FUNCTION_PURCHASE_DESTROY = "duiba.purchase.destroy";

    public static final  String DUIBA_FUNCTION_GOODS_QUERY= "duiba.purchase.goods.query";

    public static final  String DUIBA_FUNCTION_CREDITS_CHANGE_STATUS = "duiba.purchase.credits.change.status";

    public static final  String DUIBA_FUNCTION_PURCHASE_CREDITS_DESTROY = "duiba.purchase.credits.destroy";

    /**
     * 积分描述
     */
    private String creditsDesc = "积分奖励";
    /**
     * 扣积分描述
     */
    private String creditBackDesc = "积分作废";
}
