package cn.com.duiba.pcg.service.biz.config;

import com.google.common.collect.Sets;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 南宁中信银行 定制
 */
@Configuration
@ConfigurationProperties(prefix = "citic.bank.guiyang")
public class CiticBankGuiYangConfig {

    /**
     * 应用id，加密的客户号做uid免登的应用
     */
    private Set<Long> custAppIds = Sets.newHashSet();

    public Set<Long> getCustAppIds() {
        return custAppIds;
    }

    public void setCustAppIds(Set<Long> custAppIds) {
        this.custAppIds = custAppIds;
    }

    public boolean isCustGuiYangCitic(Long appId) {
        return custAppIds.contains(appId);
    }
}
