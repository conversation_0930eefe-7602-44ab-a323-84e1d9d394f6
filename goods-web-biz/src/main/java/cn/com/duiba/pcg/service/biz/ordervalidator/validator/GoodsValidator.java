package cn.com.duiba.pcg.service.biz.ordervalidator.validator;

import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.dto.PriceCreditsDto;
import cn.com.duiba.pcg.service.biz.ordervalidator.OrderValidatorException;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.BaseParam;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStatusService;
import cn.com.duiba.pcg.service.biz.vo.cmb.CmbItemConfigVO;
import cn.com.duiba.wolf.utils.DateUtils;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * Created by dugq on 2018/11/21 0021.
 */
public abstract class GoodsValidator {
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final String NO_DATA_LIMIT_STR = "no";
    private static final char DATE_SEPARATOR_CHAR = ',';
    private static final char TIME_SEPARATOR_CHAR = '-';
    private static final String TIME_PATTERN = "HH:mm";

    protected GoodsValidator() {
    }

    public static void dateLimit(ItemKeyDto itemKey, CmbItemConfigVO cmbItemConfigVO) {
        String limitDate = getLimitDate(itemKey);
        if (StringUtils.isBlank(limitDate)) {
            return;
        }
        String[] dayBetween = StringUtils.split(limitDate, DATE_SEPARATOR_CHAR);
        if (dayBetween.length != 2) {
            return;
        }


        if(cmbItemConfigVO != null){
            Date start = DateUtils.getDayDate(dayBetween[0]);
            Date end = DateUtils.getDayDate(dayBetween[1]);
            String limitStartDate = dayBetween[0] + " " + cmbItemConfigVO.getMinSecs().get(0);
            String limitEndDate = dayBetween[1] + " " + cmbItemConfigVO.getMinSecs().get(1);
            start = DateUtils.getSecondDate(limitStartDate);
            end = DateUtils.getSecondDate(limitEndDate);
            if (System.currentTimeMillis() < start.getTime() || System.currentTimeMillis() > end.getTime()) {
                throw new OrderValidatorException(ErrorCode.E9999998, "限"+dayBetween[0]+"至"+dayBetween[1]+"日期内兑换");
            }
        }else{
            LocalDate start = LocalDate.parse(dayBetween[0], DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate end = LocalDate.parse(dayBetween[1], DateTimeFormatter.ofPattern(DATE_FORMAT));
            LocalDate now = LocalDate.now();
            //含尾
            if (now.isAfter(end)) {
                throw new OrderValidatorException(ErrorCode.E9999998, "限"+dayBetween[0]+"至"+dayBetween[1]+"日期内兑换");
            }
            //含头
            if (now.isBefore(start)) {
                throw new OrderValidatorException(ErrorCode.E9999998, "限"+dayBetween[0]+"至"+dayBetween[1]+"日期内兑换");
            }
        }
    }

    private static String getLimitDate(ItemKeyDto itemKey) {
        //优先取兑吧后台设置的日期限制
        if (Objects.nonNull(itemKey.getItem()) && StringUtils.isNotBlank(itemKey.getItem().getLimitDate()) && !StringUtils.equals(itemKey.getItem().getLimitDate(), NO_DATA_LIMIT_STR)) {
            return itemKey.getItem().getLimitDate();
        }
        //再取开发者后台设置的日期限制
        if (Objects.nonNull(itemKey.getAppItem()) && !StringUtils.equals(itemKey.getAppItem().getLimitDate(), NO_DATA_LIMIT_STR)) {
            return itemKey.getAppItem().getLimitDate();
        }
        return null;
    }


    public static void timeLimit(ItemKeyDto itemKey) {
        String limitTime = getLimitTime(itemKey);
        if (StringUtils.isBlank(limitTime)) {
            return;
        }
        String[] timeBetween = StringUtils.split(limitTime, TIME_SEPARATOR_CHAR);
        if (timeBetween.length != 2) {
            return;
        }
        LocalTime start = LocalTime.parse(timeBetween[0], DateTimeFormatter.ofPattern(TIME_PATTERN));
        LocalTime end = LocalTime.parse(timeBetween[1], DateTimeFormatter.ofPattern(TIME_PATTERN));
        LocalTime now = LocalTime.now();
        //不含尾
        if (end.isBefore(now)) {
            throw new GoodsWebException(GoodsItemStatusService.StatusTypeEnum.IS_END.getName());
        }
        //含头
        if (now.isBefore(start)) {
            throw new GoodsWebException(GoodsItemStatusService.StatusTypeEnum.IS_GOING_TO_START.getName());
        }
    }

    private static String getLimitTime(ItemKeyDto itemKey) {
        //兑吧直推商品
        if (itemKey.isItemMode() && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit)) {
            return itemKey.getItem().getLimitTimeBetween();
            //开发者购买兑吧商品  兑吧的限制优先
        } else if (itemKey.isDuibaAppItemMode()) {
            if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
                return itemKey.getItem().getLimitTimeBetween();
            }
            if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit)) {
                return itemKey.getAppItem().getLimitTimeBetween();
            }
            if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto())) {
                return itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime();
            }
            //开发者自有商品
        } else if (itemKey.isSelfAppItemMode()) {
            String limitTime = itemKey.getAppItem().getLimitTimeBetween();
            if (StringUtils.isBlank(limitTime) && Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto())) {
                limitTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime();
            }
            return limitTime;
        }
        return null;
    }

    public static void validatePriceAndCredits(BaseParam param, PriceCreditsDto priceCredits) {
        if (Objects.isNull(priceCredits)) {
            throw new OrderValidatorException(ErrorCode.E1000037);
        }
        if (param.getEquityFlag()) {
            return;
        }
        Long price = Objects.isNull(priceCredits.getSalePrice()) ? 0L : priceCredits.getSalePrice();
        if(Objects.isNull(param.getDeductionType())) {
            if (!Objects.equals(price, param.getFirstItemParam().getPrice())) {
                throw new OrderValidatorException(ErrorCode.E1000032);
            }
        }
    }

}
