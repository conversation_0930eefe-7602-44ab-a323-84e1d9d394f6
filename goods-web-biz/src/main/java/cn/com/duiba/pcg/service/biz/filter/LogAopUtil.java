package cn.com.duiba.pcg.service.biz.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/5/15 7:04 PM
 */
@Slf4j
public class LogAopUtil {
    
    private LogAopUtil() {}

    /**
     * 获取请求路径
     * @param request http请求
     * @return 路径
     */
    public static String getPath(HttpServletRequest request) {
        if (request == null) {
            return "";
        }
        return clearRequestUri(request.getRequestURI());
    }

    /**
     * 将requestURI中的//替换为/
     * @param requestUri 请求uri
     * @return 请求path
     */
    public static String clearRequestUri(String requestUri){
        if(StringUtils.isBlank(requestUri)){
            return requestUri;
        }
        return StringUtils.replace(requestUri, "//", "/");
    }
    
    public static Method getMethod(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        return signature.getMethod();
    }

    public static JSONObject getFieldsName(ProceedingJoinPoint joinPoint) {
        // 参数值
        Object[] args = joinPoint.getArgs();
        ParameterNameDiscoverer pnd = new DefaultParameterNameDiscoverer();
        Method method = getMethod(joinPoint);
        String[] parameterNames = pnd.getParameterNames(method);
        JSONObject paramObj = new JSONObject();
        if (parameterNames == null || parameterNames.length < 1) {
            return paramObj;
        }
        for (int i = 0; i < Objects.requireNonNull(parameterNames).length; i++) {
            Object ob = args[i];
            if (ob instanceof HttpServletRequest || ob instanceof HttpServletResponse || ob instanceof BindingResult || ob instanceof MultipartFile){
                continue;
            }
            if (Objects.isNull(Optional.ofNullable(ob).map(Object::getClass).map(Class::getClassLoader).orElse(null))){
                paramObj.put(parameterNames[i], ob);
            }else {
                try {
                    paramObj.put(parameterNames[i], JSON.toJSONString(ob));
                }catch (Exception e){
                    log.info("access log aop warn obj:{}", ob.getClass());
                    paramObj.put(parameterNames[i], ob);
                }
            }
        }
        return paramObj;
    }

    public static String getResponseBody(Object obj, int maxLength){
        JSONObject jsonObj = new JSONObject();
        if (Objects.isNull(obj)){
            return jsonObj.toJSONString();
        }
        if (obj instanceof String){
            jsonObj.put("result", obj);
            return jsonObj.toJSONString();
        }
        String json = JSON.toJSONString(obj);
        return subStringIfAbsent(json, maxLength);
    }


    public static String subStringIfAbsent(String str, int maxLength) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        if (str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }
}
