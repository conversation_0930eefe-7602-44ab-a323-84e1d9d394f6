package cn.com.duiba.pcg.service.biz.enums;

/**
 * 用户维度限制性兑换项当前兑换状态
 * Created by xiaoxuda on 2016/12/21.
 */
public enum LimitExtrangeStatusEnum {
    /** 可兑换 */
    CANUSED("canused","可兑换"),
    /** 今日已兑 */
    DAILAYUSED("everyday","今日已兑"),
    /** 永久已兑 */
    FOREVERUSED("forever","永久已兑"),
    /** 当月已兑 */
    MONTHUSED("month","当月已兑"),
    /** 当周已兑 */
    WEEKUSED("week","本周已兑"),
    /** 批次已兑 */
    BATCHUSED("batch","批次已兑"),
    /** 兑换周期内已兑*/
    PERIODUSED("DAY_","周期已兑");

    private String code;
    private String desc;

    LimitExtrangeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * @return desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * @param code
     * @return LimitExtrangeStatusEnum
     */
    public LimitExtrangeStatusEnum getByCode(String code) {
        for (LimitExtrangeStatusEnum limitExtrangeStatusEnum : LimitExtrangeStatusEnum.values()) {
            if (limitExtrangeStatusEnum.getCode().equals(code)) {
                return limitExtrangeStatusEnum;
            }
        }
        return null;
    }
}
