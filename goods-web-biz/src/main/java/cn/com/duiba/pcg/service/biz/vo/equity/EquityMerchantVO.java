package cn.com.duiba.pcg.service.biz.vo.equity;


import java.util.List;

public class EquityMerchantVO {

    //商品名称
    private String appItemName;

    //权益截止时间
    private String equityEndDate;

    //券码
    private String code;

    //0：未核销，1：已核销，2：已过期
    private int hasVerify;

    private Long equityId;

    private List<MerchantVO> merchants;

    public String getAppItemName() {
        return appItemName;
    }

    public void setAppItemName(String appItemName) {
        this.appItemName = appItemName;
    }

    public String getEquityEndDate() {
        return equityEndDate;
    }

    public void setEquityEndDate(String equityEndDate) {
        this.equityEndDate = equityEndDate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getHasVerify() {
        return hasVerify;
    }

    public void setHasVerify(int hasVerify) {
        this.hasVerify = hasVerify;
    }

    public Long getEquityId() {
        return equityId;
    }

    public void setEquityId(Long equityId) {
        this.equityId = equityId;
    }

    public List<MerchantVO> getMerchants() {
        return merchants;
    }

    public void setMerchants(List<MerchantVO> merchants) {
        this.merchants = merchants;
    }

    public static class MerchantVO{
        /**
         *
         */
        private Long id;

        /**
         * 商家名称
         */
        private String merchantName;

        /**
         * 商家编码
         */
        private String merchantCode;

        /**
         * 商家地址
         */
        private String merchantAddress;

        /**
         * 经度
         */
        private String longitude;
        /**
         * 纬度
         */
        private String latitude;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getMerchantName() {
            return merchantName;
        }

        public void setMerchantName(String merchantName) {
            this.merchantName = merchantName;
        }

        public String getMerchantCode() {
            return merchantCode;
        }

        public void setMerchantCode(String merchantCode) {
            this.merchantCode = merchantCode;
        }

        public String getMerchantAddress() {
            return merchantAddress;
        }

        public void setMerchantAddress(String merchantAddress) {
            this.merchantAddress = merchantAddress;
        }

        public String getLongitude() {
            return longitude;
        }

        public void setLongitude(String longitude) {
            this.longitude = longitude;
        }

        public String getLatitude() {
            return latitude;
        }

        public void setLatitude(String latitude) {
            this.latitude = latitude;
        }
    }
}
