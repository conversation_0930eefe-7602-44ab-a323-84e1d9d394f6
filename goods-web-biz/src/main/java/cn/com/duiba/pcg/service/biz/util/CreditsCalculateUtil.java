/**
 * Project Name:platform-coupon-biz
 * File Name:CreditsCalculateUtil.java
 * Package Name:cn.com.duiba.pcg.service.biz.util
 * Date:2017年2月7日上午10:07:32
 * Copyright (c) 2017, duiba.com.cn All Rights Reserved.
 *
*/

package cn.com.duiba.pcg.service.biz.util;

import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * ClassName:CreditsCalculateUtil <br/>
 * Date:     2017年2月7日 上午10:07:32 <br/>
 * @version  
 * @since    JDK 1.6
 * @see 	 
 */
public class CreditsCalculateUtil {

    /**
     * 积分单位转换，如果大于1W，转换为XXX万
     * 
     * @param credits
     * @param app 
     * @return 积分展示文案
     */
    public static String convertCreditsUnit4Consumer(Long credits,AppSimpleDto app) {
        String ret = "";
        if(credits==null){
            return ret;
        }
        if(app.isAppSwitch(AppSimpleDto.SwitchCreditsDecimalPoint)){
            if (credits >= 1000000 && credits % 100000 == 0) {
                ret = new BigDecimal(credits).divide(new BigDecimal(1000000)) + "万";
            } else {
                DecimalFormat df = new DecimalFormat("#0.00");  
                ret = df.format(Double.valueOf(credits)/100) + "";
            }
        }else{
            if (credits >= 10000 && credits % 1000 == 0) {
                ret = new BigDecimal(credits).divide(new BigDecimal(10000)) + "万";
            } else {
                ret = credits + "";
            }
        }
        return ret;
    }
    
    /**
     * @param money
     * @return 展示金额
     */
    public static String convertMoneyUnit4Consumer(Long money) {
        String ret = "";
        if (money == null) {
            return ret;
        }
        if (money >= 1000000 && money % 100000 == 0) {
            ret = new BigDecimal(money).divide(new BigDecimal(1000000)) + "万";
        } else {
            DecimalFormat df = new DecimalFormat("#0.00");
            ret = df.format(Double.valueOf(money) / 100) + "";
        }
        return ret;
    }
}

