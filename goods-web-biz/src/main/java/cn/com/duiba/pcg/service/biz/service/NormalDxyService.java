package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.activity.center.api.enums.label.ConsumerLabelType;
import cn.com.duiba.activity.center.api.remoteservice.label.RemoteConsumerLabelService;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParam;
import cn.com.duiba.activity.center.api.remoteservice.label.param.LabelPassGoodsFilterParamBuilder;
import cn.com.duiba.activity.center.api.remoteservice.label.result.LabelPassResult;
import cn.com.duiba.activity.custom.center.api.dto.identity.IdentityCustomerRelationDto;
import cn.com.duiba.activity.custom.center.api.enums.BocRelTypeEnum;
import cn.com.duiba.activity.custom.center.api.paramquery.identity.IdentityCustomerRelationConditionParam;
import cn.com.duiba.activity.custom.center.api.remoteservice.identity.RemoteIdentityCustomerRelationSerivce;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.developer.center.api.domain.dto.visualeditor.VisualEditorAppSkinDto;
import cn.com.duiba.developer.center.api.domain.dto.visualeditor.VisualEditorAppSkinUnitDto;
import cn.com.duiba.developer.center.api.domain.param.VisualEditorAppSkinQueryParam;
import cn.com.duiba.developer.center.api.remoteservice.visualeditor.RemoteVisualEditorAppSkinService;
import cn.com.duiba.developer.center.api.remoteservice.visualeditor.RemoteVisualEditorAppSkinUnitService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.kvtable.service.api.dto.HbApiKStrVDto;
import cn.com.duiba.kvtable.service.api.remoteservice.hbase.RemoteHbApiKvNoHotspotService;
import cn.com.duiba.order.center.api.constant.OrderStatusEnum;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.BocConfig;
import cn.com.duiba.pcg.service.biz.handler.bocCycle.BocCycleHandler;
import cn.com.duiba.pcg.service.biz.constants.WhiteCodeUtils;
import cn.com.duiba.pcg.service.biz.vo.NormalDxyBaseInfo;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-01
 */
@Service
public class NormalDxyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(NormalDxyService.class);

    @Autowired
    private BocConfig bocConfig;
    @Autowired
    private RemoteHbApiKvNoHotspotService remoteHbApiKvNoHotspotService;
    @Autowired
    private List<BocCycleHandler> bocCycleHandlers;
    @Autowired
    private RemoteConsumerLabelService remoteConsumerLabelService;
    @Autowired
    private RemoteIdentityCustomerRelationSerivce remoteIdentityCustomerRelationSerivce;
    @Autowired
    private RemoteVisualEditorAppSkinService remoteVisualEditorAppSkinService;
    @Autowired
    private RemoteVisualEditorAppSkinUnitService remoteVisualEditorAppSkinUnitService;


    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;

    public BocRelTypeEnum getBocRelTypeEnum(Long appId) {
        String cycleKey = BocConfig.getdxyKey(appId, bocConfig.getNormalDxyUnitKey());
        String cycleValue = remoteHbApiKvNoHotspotService.getStringByKey(cycleKey);
        //没查到限制周期 肯定有问题，组件默认也有null
        if (StringUtils.isBlank(cycleValue)) {
            throw new GoodsWebException("未找到商品多选一组件限制周期");
        }
        String[] s = cycleValue.split("_");
        BocRelTypeEnum bocRelTypeEnum = EnumUtils.getEnum(BocRelTypeEnum.class, s[1]);
        if (bocRelTypeEnum == null) {
            throw new GoodsWebException("");
        }
        return bocRelTypeEnum;
    }

    private BocCycleHandler getBocCycleHandler(BocRelTypeEnum type) {
        for (BocCycleHandler bocCycleHandler : bocCycleHandlers) {
            Boolean flag = bocCycleHandler.getType(type);
            if (flag) {
                return bocCycleHandler;
            }
        }
        return null;
    }

    /**
     * 此商品是否多选一商品
     *
     * @param appItemId
     * @return
     */
    public Boolean isDxyAppItem(Long appId, Long appItemId) {
        List<Long> dxyAppItemIdList = getDxyAppItemIdList(appId);
        if (dxyAppItemIdList.contains(appItemId)) {
            return true;
        }
        return false;
    }

    private List<Long> getDxyAppItemIdList(Long appId) {
        //找出所有页面皮肤-组件关联关系
        VisualEditorAppSkinQueryParam param = new VisualEditorAppSkinQueryParam();
        param.setAppId(appId);
        List<VisualEditorAppSkinDto> skinDtoList = remoteVisualEditorAppSkinService.selectSkins(param);
        if (CollectionUtil.isEmpty(skinDtoList)) {
            LOGGER.info("商品多选一组件 获取多选一商品 页面列表为空 appId={}", appId);
            return Lists.newArrayList();
        }
        List<Long> skinIds = skinDtoList.stream().map(VisualEditorAppSkinDto::getId).collect(Collectors.toList());

        //过滤这些页面所有的商品多选一组件
        List<String> unitKeyList = remoteVisualEditorAppSkinUnitService.finListBySkinIds(skinIds)
                .stream()
                .filter(x -> bocConfig.getNormalDxyUnitKey().equals(x.getUnitKey()))
                .map(VisualEditorAppSkinUnitDto::getId)
                .map(VisualEditorAppSkinUnitDto::buildAppSkinUnitHbaseKey)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unitKeyList)) {
            LOGGER.info("商品多选一组件 获取多选一商品 未配置商品多选一组件 appId={}", appId);
            return Lists.newArrayList();
        }

        List<HbApiKStrVDto> hbApiKStrVDtos = remoteHbApiKvNoHotspotService.batchFindStrVBykeys(unitKeyList);
        if (CollectionUtils.isEmpty(hbApiKStrVDtos)) {
            LOGGER.warn("商品多选一组件 获取多选一商品 未查询到配置信息 appId={}");
            return Lists.newArrayList();
        }
        //商品多选一组件，约定只有一个
        HbApiKStrVDto hbApiKStrVDto = hbApiKStrVDtos.get(0);
        LOGGER.info("商品多选一组件 获取多选一商品 组件配置信息 appId={} dto={}", appId, JSON.toJSONString(hbApiKStrVDto));
        JSONArray itemIds = JSON.parseObject(hbApiKStrVDto.getValue()).getJSONArray("itemIds");
        List<Long> appItemIdList = itemIds.toJavaList(Long.class);
        LOGGER.info("商品多选一组件 获取多选一商品 商品id集合 appId={} appItemIdList={}", appId, JSON.toJSONString(appItemIdList));
        return appItemIdList;
    }

    /**
     * 用户兑换状态
     *
     * @return
     */
    public NormalDxyBaseInfo getInfo() {
        boolean hasExchangeFlag = false;
        boolean canExchangeFlag = false;

        String uid = RequestLocal.getPartnerUserId();
        Long appId = RequestLocal.getAppId();
        Long cid = RequestLocal.getCid();

        //白名单内应用可调用
        if (!WhiteAccessUtil.matchWhiteList(appId, WhiteCodeUtils.NORMAL_DXY_CODE)) {
            throw new GoodsWebException("暂无权限");
        }
        //是否可兑换
        LabelPassGoodsFilterParam goodsFilterParam = new LabelPassGoodsFilterParamBuilder()
                .setAppId(appId)
                .setUserId(uid)
                .setStrategyType(ConsumerLabelType.GOODS_FILTER.getType())
                .setAppItemIds(Lists.newArrayList())
                .setStrategyContents(bocConfig.getNormalDxyUnitKey())
                .setCid(cid)
                .builder();
        LabelPassResult result = remoteConsumerLabelService.isPass(goodsFilterParam);
        LOGGER.info("商品多选一组件 是否可兑换判断 goodsFilterParam={} result={}", JSON.toJSONString(goodsFilterParam), JSON.toJSONString(result));
        if (CollectionUtils.isNotEmpty(result.getPassTargets()) && result.getPassTargets().contains(bocConfig.getNormalDxyUnitKey())) {
            canExchangeFlag = true;
        }

        //是否已兑换
        BocRelTypeEnum bocRelTypeEnum = this.getBocRelTypeEnum(appId);
        //查询该用户兑换记录
        //获取限制周期类型的开始-结束时间
        BocCycleHandler handler = this.getBocCycleHandler(bocRelTypeEnum);
        if (handler == null) {
            LOGGER.error("商品多选一组件 未找到对应限制周期处理器 appId={} uid={} bocRelTypeEnum={}", appId, uid, bocRelTypeEnum);
            throw new GoodsWebException("未找到对应限制周期处理器");
        }
        Map<String, Date> cycleStartAndEndTime = handler.getCycleStartAndEndTime(new Date());

        //查询该用户兑换记录
        IdentityCustomerRelationConditionParam param = new IdentityCustomerRelationConditionParam();
        param.setAppId(appId);
        param.setCid(cid);
        param.setPartnerid(uid);
        param.setRelType(bocRelTypeEnum.getCode());
        param.setRelContent(bocConfig.getNormalDxyUnitKey());
        param.setStartTime(cycleStartAndEndTime.get("start"));
        param.setEndTime(cycleStartAndEndTime.get("end"));
        List<IdentityCustomerRelationDto> identityCustomerRelationDtos = remoteIdentityCustomerRelationSerivce.listByConditions(param);
        LOGGER.info("商品多选一组件 是否已兑换判断 param={} identityCustomerRelationDtos={}", JSON.toJSONString(param), JSON.toJSONString(identityCustomerRelationDtos));
        //有兑换记录
        if (CollectionUtils.isNotEmpty(identityCustomerRelationDtos)) {
            //库存不足 会影响兑换资格 需要先查询订单 将失败的订单逻辑删除
            Map<Long, IdentityCustomerRelationDto> orderMap = identityCustomerRelationDtos.stream()
                    .filter(i -> StringUtils.isNotBlank(i.getExtra())).collect(Collectors.toMap(
                            i -> JSONObject.parseObject(i.getExtra()).getLong("orderId"), Function.identity(), (x1, x2) -> x2));
            //获取订单号
            Set<Long> orderIds = orderMap.keySet();
            Map<String, List<OrdersDto>> orderStatusMap = Optional.ofNullable(remoteConsumerOrderSimpleService.findByIds(new ArrayList<>(orderIds), cid).getResult()).orElse(new ArrayList<>())
                    .stream().collect(Collectors.groupingBy(OrdersDto::getStatus));
            //如果订单成功
            if (CollectionUtils.isNotEmpty(orderStatusMap.get(OrderStatusEnum.StatusSuccess.getCode())) || CollectionUtils.isNotEmpty(orderStatusMap.get(OrderStatusEnum.StatusAfterSend.getCode()))
                    || CollectionUtils.isNotEmpty(orderStatusMap.get(OrderStatusEnum.StatusConsumeSuccess.getCode()))){
                hasExchangeFlag = true;
                orderStatusMap.remove(OrderStatusEnum.StatusSuccess.getCode());
            }
            // 失败订单 过滤恢复资格  删除记录
            if (MapUtils.isNotEmpty(orderStatusMap)){
                List<Long> ids = orderStatusMap.values().stream().flatMap(i ->
                        i.stream().filter(t  -> StringUtils.isNotBlank(t.getFlowworkStage()) &&
                                t.getFlowworkStage().toLowerCase().contains("fail")).map(OrdersDto::getId))
                        .collect(Collectors.toList());
                //获取主键id
                if (CollectionUtils.isNotEmpty(ids)){
                    List<Long> relationIds = orderMap.entrySet().stream().filter(i -> ids.contains(i.getKey())).map(t -> t.getValue().getId()).collect(Collectors.toList());
                    remoteIdentityCustomerRelationSerivce.batchUpdateIsDeleteByIds(relationIds);
                }
            }
        }

        NormalDxyBaseInfo baseInfo = new NormalDxyBaseInfo();
        baseInfo.setHasExchangeFlag(hasExchangeFlag);
        baseInfo.setCanExchangeFlag(canExchangeFlag);
        return baseInfo;
    }


}
