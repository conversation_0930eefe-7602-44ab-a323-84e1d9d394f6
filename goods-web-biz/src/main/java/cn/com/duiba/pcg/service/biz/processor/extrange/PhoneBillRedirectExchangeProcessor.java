package cn.com.duiba.pcg.service.biz.processor.extrange;

import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.developer.center.api.domain.dto.AppBudgetDto;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.geo.api.dto.PhoneAreaDto;
import cn.com.duiba.geo.api.remoteservice.RemotePhoneAreaService;
import cn.com.duiba.goods.center.api.remoteservice.dto.SupplierProductDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.dto.RequestParams;
import cn.com.duiba.order.center.api.dto.UniqueOrderCheckDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteExchangeService;
import cn.com.duiba.order.center.api.remoteservice.RemoteUniqueOrderCheckService;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.BaseParam;
import cn.com.duiba.pcg.service.biz.service.AppExtrangeLimitService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemActualPriceCalculateService;
import cn.com.duiba.pcg.service.biz.service.SupplierProductService;
import cn.com.duiba.pcg.service.biz.util.OrderTool;
import cn.com.duiba.pcg.service.biz.vo.ConsumerBindingInfoVO;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.perf.timeprofile.RequestTool;
import cn.com.duiba.wolf.utils.NumberUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

import static cn.com.duiba.pcg.service.biz.processor.extrange.AlipayRedirectExchangeProcessor.QUANTITY;

/**
 * Created by yansen on 17/5/24.
 */
@Component("phoneBillRedirectExchangeProcessor")
public class PhoneBillRedirectExchangeProcessor extends ExtrangeBaseProcessor{

    Logger log = LoggerFactory.getLogger(AlipayRedirectExchangeProcessor.class);

    public static final String TYPE_PHONEBILL_RIDETECT="redirectPhonebill";

    private static final String PARAM_BIZID_KEY = "bizId";
    private static final String PARAM_PHONE_KEY = "phone";

    @Autowired
    private RemoteUniqueOrderCheckService remoteUniqueOrderCheckService;

    @Autowired
    private RemoteAppService remoteAppServiceNew;

    @Autowired
    private GoodsItemActualPriceCalculateService goodsItemActualPriceCalculateService;
    @Autowired
    private SupplierProductService supplierProductService;
    @Autowired
    private AppExtrangeLimitService appExtrangeLimitService;
    @Autowired
    private RemoteExchangeService remoteExchangeService;
    @Autowired
    private RemotePhoneAreaService remotePhoneAreaService;

    /**
     * 处理器要处理的商品类型，{@link ItemDto}
     *
     * @return
     */
    @Override
    public String[] getProcessorType() {
        return new String[]{TYPE_PHONEBILL_RIDETECT};

    }

    /**
     * 参数验证
     * 不通过直接抛出GoodsWebException类型业务异常，可从e.getMessage中得到验证失败的原因
     *
     * @param request
     * @param itemKey
     * @param app
     * @param consumer
     * @throws GoodsWebException 业务异常
     */
    @Override
    public void paramValid(HttpServletRequest request, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) {
        String phone = request.getParameter(PARAM_PHONE_KEY);
        if(StringUtils.isBlank(phone) || phone.length()!=11){
            throw new GoodsWebException("请输入正确的手机号码");
        }
        int quantity = NumberUtils.parseInt(request.getParameter("quantity"),0);
        String bizId = request.getParameter(PARAM_BIZID_KEY);

        if(StringUtils.isBlank(bizId) || bizId.trim().length()==0){
            throw new GoodsWebException("bizId不能为空");

        }
        String mobile="";
        String province="";
        try {
            PhoneAreaDto attr = remotePhoneAreaService.findPhoneArea(phone);
            mobile = attr.getIsp();
            province = attr.getAddress();
            if(StringUtils.isBlank(mobile) || mobile.length()==0
                    || StringUtils.isBlank(province) || province.length()==0){
                throw new GoodsWebException("无法识别此手机号");
            }
        } catch (Exception e) {
            //号码归属地查询失败
            throw new GoodsWebException("手机号归属地查询失败");
        }
        //四川联通5元起充，湖北联通10元起充，陕西联通5元起充
        if(province.contains("四川") && mobile.contains("联通") && quantity<5){
            throw new GoodsWebException("四川联通暂时不支持此面值充值");
        }
        if(province.contains("湖北") && mobile.contains("联通") && quantity<10){
            throw new GoodsWebException("湖北联通暂时不支持此面值充值");
        }
        if(province.contains("陕西") && mobile.contains("联通") && quantity<5){
            throw new GoodsWebException("陕西联通暂时不支持此面值充值");
        }

    }

    /**
     * 是否可兑换验证,业务相关的校验
     * 不通过直接抛出GoodsWebException类型业务异常，可从e.getMessage中得到验证失败的原因
     *
     * @param baseParam
     * @param itemKey
     * @param app
     * @param consumer
     * @throws GoodsWebException 业务异常
     */
    @Override
    public void canExtrangeValid(BaseParam baseParam, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) {
        throw new GoodsWebException(ERRMSG_NOT_IMPL);
    }

    /**
     * 是否可兑换验证,业务相关的校验
     * 不通过直接抛出GoodsWebException类型业务异常，可从e.getMessage中得到验证失败的原因
     *
     * @param request
     * @param itemKey
     * @param app
     * @param consumer
     * @throws GoodsWebException 业务异常
     */
    @Override
    public void canExtrangeValid(HttpServletRequest request, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) {
        String bizId = request.getParameter(PARAM_BIZID_KEY);
        UniqueOrderCheckDto uoc=new UniqueOrderCheckDto(true);
        uoc.setAppId(app.getId());
        uoc.setDeveloperBizId(bizId);
        uoc.setSource(UniqueOrderCheckDto.SOURCE_API);
        try{
            //判断接口是否调用成功
            DubboResult<UniqueOrderCheckDto> dubboResult = remoteUniqueOrderCheckService.insert(uoc);
            if (!dubboResult.isSuccess()) {
                throw new GoodsWebException("Failed to remoteUniqueOrderCheckService.insert msg=" + dubboResult.getMsg());
            }
        }catch(Exception e){
            log.error("API接口创建话费订单失败，APP="+app.getId()+e.getMessage());
            throw new GoodsWebException("创建订单失败,订单号不能重复");
        }
    }

    /**
     * 创建订单
     *
     * @param baseParam
     * @param itemKey
     * @param app
     * @return OrdersDto
     * @throws GoodsWebException
     */
    @Override
    public OrdersDto buildOrder(BaseParam baseParam, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) {
        throw new GoodsWebException(ERRMSG_NOT_IMPL);
    }

    /**
     * 创建订单
     *
     * @param request
     * @param itemKey
     * @param app
     * @param consumer
     * @return OrdersDto
     * @throws GoodsWebException
     */
    @Override
    public OrdersDto buildOrder(HttpServletRequest request, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer) {

        PhoneAreaDto attr = remotePhoneAreaService.findPhoneArea(request.getParameter(PARAM_PHONE_KEY));
        String mobile = attr.getIsp();
        String province = attr.getAddress();
        request.setAttribute(ConsumerBindingInfoVO.PHONE_CATNAME,mobile);
        request.setAttribute(ConsumerBindingInfoVO.PHONE_ROVINCE,province);
        int quantity = Integer.parseInt(request.getParameter(QUANTITY));
        String type= ItemDto.TypePhonebillDingzhi.equals(request.getParameter("type"))? ItemDto.TypePhonebillDingzhi:ItemDto.TypePhonebill;
        if (ItemDto.TypePhonebillDingzhi.equals(type)) {
            quantity = itemKey.getItem().getFacePrice() / 100;
        }
        String phone;
        if (app.isAppSwitch(AppSimpleDto.BoundPhone)) {
            phone = consumer.getPhone();
        } else {
            phone = request.getParameter(ConsumerBindingInfoVO.MOBILE);
        }

        OrdersDto order = new OrdersDto(true);
        order.setChargeMode(OrdersDto.ChargeModeApi);
        order.setConsumerId(consumer.getId());
        order.setAppId(consumer.getAppId());
        order.setDeveloperId(app.getDeveloperId());
        order.setFacePrice(quantity * 100);
        order.setBrief("手机号:" + phone + "充值" + quantity + "元");
        order.setBizParams(phone + ":" + mobile + ":" + province);

        Integer tempActualPrice = goodsItemActualPriceCalculateService.calculatePhonebillActualPrice(mobile, province, order.getFacePrice(), app.getId());
        if (tempActualPrice == null) {
            /**logger.error(phone + "," + province + "," + tempActualPrice + " 该地区的价格未设置，无法进行充值兑换");*/
            logger.error("{} , {}, {} , 该地区的价格未设置，无法进行充值兑换", phone, province, tempActualPrice);
            throw new GoodsWebException("该地区不支持充值");
        }
        order.setActualPrice(tempActualPrice);

        if (order.getActualPrice() != null && order.getActualPrice() > 0) {
            order.setPayStatus(OrdersDto.PayStatusWaitPay);
        } else {
            order.setPayStatus(OrdersDto.PayStatusNone);
        }

        order.setQuantity(quantity);
        order.setStatus(OrdersDto.StatusCreate);
        order.setType(ItemDto.TypePhonebill);

        order.setCredits(0l);

        if (itemKey.getItem() != null) {
            order.setItemId(itemKey.getItem().getId());
        }
        if (itemKey.getAppItem() != null) {
            order.setAppItemId(itemKey.getAppItem().getId());
        }

        order.setDuibaPayStatus(OrdersDto.DuibaPayStatusNone);
        order.setConsumerPayStatus(OrdersDto.ConsumerPayStatusNone);

        order.setIp(RequestTool.getIpAddr(request));

        AppBudgetDto budgetDto = remoteAppServiceNew.getAppBudget(app.getId()).getResult();
        if (OrderTool.isNeedAudit(order, itemKey, budgetDto)) {
            order.setAuditStatus(OrdersDto.AuditStatusWait);
        }

        return order;
    }

    /**
     * 执行兑换,因为执行兑换操作不会再次验证参数和商品可兑性，不建议提供给外部调用
     * 兑换成功返回订单号
     * 不通过直接抛出GoodsWebException业务异常，可从e.getMessage中得到验证失败的原因
     *
     * @param baseParam
     * @param itemKey
     * @param app
     * @param order    @return 订单号
     * @throws GoodsWebException 业务异常
     */
    @Override
    protected Long excuteExtrange(BaseParam baseParam, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer, OrdersDto order) {
        throw new GoodsWebException(ERRMSG_NOT_IMPL);
    }

    /**
     * 执行兑换,因为执行兑换操作不会再次验证参数和商品可兑性，不建议提供给外部调用
     * 兑换成功返回订单号
     * 不通过直接抛出GoodsWebException业务异常，可从e.getMessage中得到验证失败的原因
     *
     * @param request
     * @param itemKey
     * @param app
     * @param consumer
     * @param order    @return 订单号
     * @throws GoodsWebException 业务异常
     */
    @Override
    protected Long excuteExtrange(HttpServletRequest request, ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer, OrdersDto order) {
        DubboResult<OrdersDto> dubboResult=null;
        try {
            appExtrangeLimitService.balanceValidate(app, order.getActualPrice());

            SupplierProductDto supplierProductDto = supplierProductService.queryProduct((String)request.getAttribute(ConsumerBindingInfoVO.PHONE_CATNAME), (String)request.getAttribute(ConsumerBindingInfoVO.PHONE_ROVINCE), order.getFacePrice(), app.getId());

            //开发者余额校验

            dubboResult = remoteExchangeService
                    .createPhoneBillOrder(order, supplierProductDto.getId(), getRemoteRequestParams());
            if (!dubboResult.isSuccess()) {
                throw new GoodsWebException(dubboResult.getMsg());
            }
        }catch (Exception e){
            remoteUniqueOrderCheckService.deleteByAppAndBizId(app.getId(), request.getParameter(PARAM_BIZID_KEY));
            throw new GoodsWebException(e.getMessage());
        }
        return dubboResult.getResult().getId();
    }

    @Override
    public OrdersDto secondKillExtrange(ItemKeyDto itemKey, AppSimpleDto app, ConsumerDto consumer, RequestParams requestParams, Long relationId, Integer relationType) {
        throw new GoodsWebException("该方法不支持");
    }

    @Override
    public String getCaptchaId(ConsumerDto consumer, AppSimpleDto app, ItemKeyDto itemKey) {
        return null;
    }
}
