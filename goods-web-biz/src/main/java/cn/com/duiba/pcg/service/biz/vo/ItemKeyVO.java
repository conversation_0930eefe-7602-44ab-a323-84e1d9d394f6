package cn.com.duiba.pcg.service.biz.vo;

import cn.com.duiba.activity.center.api.dto.activity.OperatingActivityDto;
import cn.com.duiba.dcommons.enums.AppItemSourceTypeEnum;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * Created by yansen on 17/1/3.
 */
public class ItemKeyVO implements Serializable {

    private static final long   serialVersionUID = 5465809480099328803L;

    /**
     * 已兑
     */
    public static final Integer STATUS_USED      = 1;
    /**
     * 兑完
     */
    public static final Integer STATUS_NOREMAING = 2;
    /**
     * 抽奖
     */
    public static final Integer STATUS_LOTTERY   = 3;

    /** 普通 */
    public static final Integer MARK_NORMAL      = 0;
    /** 秒杀 */
    public static final Integer MARK_TIME        = 1;
    /** 限量 */
    public static final Integer MARK_QUANTITY    = 2;
    /** 抽奖 */
    public static final Integer MARK_LOTTERY     = 3;
    /** 活动(专题) */
    public static final Integer MARK_ACTIVITY    = 4;
    /** NEW(新商品) */
    public static final Integer MARK_NEW         = 5;
    /** 地域限制 */
    public static final Integer MARK_ADDRLIMIT   = 6;
    /** 游戏 */
    public static final Integer MARK_GAME        = 7;
    /** 答题 */
    public static final Integer MARK_QUESTION    = 8;
    /** 测试题 */
    public static final Integer MARK_QUIZZ       = 9;
    /** 游戏 */
    public static final Integer MARK_NGAME       = 7;
    /** 竞猜 */
    public static final Integer MARK_GUESS       = 10;
    /** 积分游戏 */
    public static final Integer MARK_CREDIT_GAME = 11;

    public static final String DBNEWOPEN = "&dbnewopen";
    private ItemKeyDto          itemKey;

    private Integer             useStatus        = 0;
    private Integer             markStatus       = MARK_NORMAL;
    private Integer             payload;
    private Integer             activityType;
    private String              activityId;
    private String              duibaActivityId;
    private Long                sAppItemId;
    private Long                sItemId;

    //定向库存
    private Integer itemAppSpecifyRemaining;
    //预分配库存
    private Long ItemAppPreStock;

    /**
     * Creates a new instance of ItemKeyVO.
     */
    public ItemKeyVO() {
    }

    /**
     * Creates a new instance of ItemKeyVO.
     *
     * @param itemKey
     */
    public ItemKeyVO(ItemKeyDto itemKey) {
        this.itemKey = itemKey;
    }

    /**
     * @return ItemId
     */
    public Long getItemId() {
        if (itemKey.isItemMode() || itemKey.isDuibaAppItemMode()) {
            return itemKey.getItem().getId();
        }
        if (itemKey.isSelfAppItemMode()) {
            return null;
        }
        return null;
    }

    /**
     * @return AppItemId
     */
    public Long getAppItemId() {
        if (itemKey.isItemMode()) {
            return null;
        }
        if (itemKey.isSelfAppItemMode() || itemKey.isDuibaAppItemMode()) {
            return itemKey.getAppItem().getId();
        }
        return null;
    }

    /**
     * @return logo
     */
    public String getLogo() {
        if (itemKey.isItemMode()) {
            return itemKey.getItem().getLogo();
        }
        if (itemKey.isSelfAppItemMode()) {
            return itemKey.getAppItem().getLogo();
        }
        if (itemKey.isDuibaAppItemMode()) {
            if (StringUtils.isNotBlank(itemKey.getAppItem().getLogo())) {
                return itemKey.getAppItem().getLogo();
            } else {
                return itemKey.getItem().getLogo();
            }
        }
        return null;
    }

    /**
     * @return smallImage
     */
    public String getSmallImage() {
        if (itemKey.isItemMode()) {
            return itemKey.getItem().getSmallImage();
        }
        if (itemKey.isSelfAppItemMode()) {
            return itemKey.getAppItem().getSmallImage();
        }
        if (itemKey.isDuibaAppItemMode()) {
            if (StringUtils.isNotBlank(itemKey.getAppItem().getSmallImage())) {
                return itemKey.getAppItem().getSmallImage();
            } else {
                return itemKey.getItem().getSmallImage();
            }
        }
        return null;
    }

    /**
     * @return title
     */
    public String getTitle() {
        if (itemKey.getAppItem() != null) {
            if (itemKey.getAppItem().getTitle() == null) {
                if (itemKey.getItem() != null) {
                    return itemKey.getItem().getName();
                }
                return null;
            } else {
                return itemKey.getAppItem().getTitle();
            }
        } else {
            return itemKey.getItem().getName();
        }
    }

    /**
     * @return id
     */
    public Long getId() {
        if (itemKey.getAppItem() != null) {
            if (itemKey.getAppItem().getId() == null) {
                if (itemKey.getItem() != null) {
                    return itemKey.getItem().getId();
                }
                return null;
            } else {
                return itemKey.getAppItem().getId();
            }
        } else {
            return itemKey.getItem().getId();
        }
    }

    /**
     * @return type
     */
    public String getType() {
        if (itemKey.getAppItem() != null) {
            if (itemKey.getAppItem().getType() == null) {
                if (itemKey.getItem() != null) {
                    return itemKey.getItem().getType();
                }
                return null;
            } else {
                return itemKey.getAppItem().getType();
            }
        } else {
            return itemKey.getItem().getType();
        }
    }

    /**
     * @return subtitle
     */
    public String getSubtitle() {
        if (itemKey.isItemMode()) {
            return itemKey.getItem().getSubtitle();
        } else if (itemKey.isDuibaAppItemMode()) {
            if (StringUtils.isBlank(itemKey.getAppItem().getSubtitle())) {
                return itemKey.getItem().getSubtitle();
            } else {
                return itemKey.getAppItem().getSubtitle();
            }
        } else if (itemKey.isSelfAppItemMode()) {
            return itemKey.getAppItem().getSubtitle();
        } else {
            return null;
        }
    }

    /**
     * @return itemKey
     */
    public ItemKeyDto getItemKey() {
        return itemKey;
    }

    /**
     * useStatus.
     *
     * @return the useStatus
     */
    public Integer getUseStatus() {
        return useStatus;
    }

    /**
     * useStatus.
     *
     * @param useStatus the useStatus to set
     */
    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    /**
     * markStatus.
     *
     * @return the markStatus
     */
    public Integer getMarkStatus() {
        return markStatus;
    }

    /**
     * markStatus.
     *
     * @param markStatus the markStatus to set
     */
    public void setMarkStatus(Integer markStatus) {
        this.markStatus = markStatus;
    }

    /**
     * payload.
     *
     * @return the payload
     */
    public Integer getPayload() {
        return payload;
    }

    /**
     * payload.
     *
     * @param payload the payload to set
     */
    public void setPayload(Integer payload) {
        this.payload = payload;
    }

    /**
     * @return link
     */
    public String getLink(DomainConfigDto domainConfigDto) {
        String link ;
        String activityDomain=domainConfigDto.getActivityDomain();
        String skillDomain=domainConfigDto.getSeckillDomain();
        if (getItemKey().getAppItem() != null) {
            link = getAppItemLink(activityDomain,skillDomain);
        } else {
            link = "/mobile/detail?itemId=" + getItemId() + DBNEWOPEN;
        }
        return link;
    }

    private String getAppItemLink(String activityDomain,String skillDomain){
        AppItemDto ai = getItemKey().getAppItem();
         if(ai.getSourceType()==AppItemDto.SourceTypeSingleLottery){
            return activityDomain + "/singleLottery/index?id="+ai.getSourceRelationId()+DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeManualLottery) {
            return activityDomain +  "/consumerAppManualLottery/index?id=" + ai.getSourceRelationId() + "?dbnewopen";
        } else if (ai.getSourceType() == AppItemDto.SourceTypeDuibaActivitySingleLottery) {
            return activityDomain + "/singleLottery/index?id=" + ai.getSourceRelationId() + "&inlet=1"+DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeDuibaActivityItem) {
            return "/mobile/detail?itemId=" + ai.getSourceRelationId() + DBNEWOPEN;
            // 新圆形大转盘迁移
        } else if (isNewtools(ai)) {
            return activityDomain + "/hdtool/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeDuibaQuestionAnswer) {
            return activityDomain + "/question/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == OperatingActivityDto.TypeActivityAccessQuestionAnswer) {
            return activityDomain + "/question/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeDuibaQuizz) {
            return activityDomain + "/quizz/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeSecondsKill) {
            return skillDomain + "/SecondsKillActivity/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeDuibaSeckill) {
            return skillDomain + "/seckill/index/" + ai.getSourceRelationId() + "?dbnewopen";
        } else if (ai.getSourceType() == AppItemDto.SourceTypeNgame) {
            return activityDomain + "/ngame/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemDto.SourceTypeGuess) {
            return activityDomain + "/guess/index?id=" + ai.getSourceRelationId() + DBNEWOPEN;
        } else if (ai.getSourceType() == AppItemSourceTypeEnum.SourceTypeCreditGame.getCode()) {
            return activityDomain + "/creditgames/" + ai.getSourceRelationId()+ "?dbnewopen";
        }
        if (ai.getItemId() != null) {
            return "/mobile/detail?itemId=" + ai.getItemId() + DBNEWOPEN;
        } else {
            return "/mobile/appItemDetail?appItemId=" + ai.getId() + DBNEWOPEN;
        }
    }

    private boolean isNewtools(AppItemDto ai){
        return ai.getSourceType() == AppItemDto.SourceTypeAppScratchCardLottery
                || ai.getSourceType() == AppItemDto.SourceTypeHdtoolTiger
                || ai.getSourceType() == AppItemDto.SourceTypeHdtoolFlop
                || ai.getSourceType() == AppItemDto.SourceTypeHdtoolSmashg
                || ai.getSourceType() == AppItemDto.SourceTypeHdtoolTurntable
                || ai.getSourceType() == OperatingActivityDto.TypeActivityAccessHdtool;
    }
    /**
     * @param itemKey
     */
    public void setItemKey(ItemKeyDto itemKey) {
        this.itemKey = itemKey;
    }

    /**
     * activityType.
     *
     * @return the activityType
     */
    public Integer getActivityType() {
        return activityType;
    }

    /**
     * activityType.
     *
     * @param activityType the activityType to set
     */
    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    /**
     * activityId.
     *
     * @return the activityId
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     * activityId.
     *
     * @param activityId the activityId to set
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * duibaActivityId.
     *
     * @return the duibaActivityId
     */
    public String getDuibaActivityId() {
        return duibaActivityId;
    }

    /**
     * duibaActivityId.
     *
     * @param duibaActivityId the duibaActivityId to set
     */
    public void setDuibaActivityId(String duibaActivityId) {
        this.duibaActivityId = duibaActivityId;
    }

    /**
     * sAppItemId.
     *
     * @return the sAppItemId
     */
    public Long getsAppItemId() {
        return sAppItemId;
    }

    /**
     * sAppItemId.
     *
     * @param sAppItemId the sAppItemId to set
     */
    public void setsAppItemId(Long sAppItemId) {
        this.sAppItemId = sAppItemId;
    }

    /**
     * sItemId.
     *
     * @return the sItemId
     */
    public Long getsItemId() {
        return sItemId;
    }

    /**
     * sItemId.
     *
     * @param sItemId the sItemId to set
     */
    public void setsItemId(Long sItemId) {
        this.sItemId = sItemId;
    }

    public Integer getItemAppSpecifyRemaining() {
        return itemAppSpecifyRemaining;
    }

    public void setItemAppSpecifyRemaining(Integer itemAppSpecifyRemaining) {
        itemAppSpecifyRemaining = itemAppSpecifyRemaining;
    }

    public Long getItemAppPreStock() {
        return ItemAppPreStock;
    }

    public void setItemAppPreStock(Long itemAppPreStock) {
        ItemAppPreStock = itemAppPreStock;
    }
}
