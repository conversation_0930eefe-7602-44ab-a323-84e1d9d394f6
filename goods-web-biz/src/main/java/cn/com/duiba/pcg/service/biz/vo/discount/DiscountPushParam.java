package cn.com.duiba.pcg.service.biz.vo.discount;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: <PERSON><PERSON>ui
 * @date: 2023/11/28 10:55
 * @description:
 */
public class DiscountPushParam {

    @NotNull(message = "appKey不能为空")
    private String appKey;

    @NotNull(message = "优惠券id不能为空")
    private Long discountId;

    @NotNull(message = "签名不能为空")
    private String sign;


    @NotNull(message = "请求唯一编号")
    private String uniqueId;
    /**
     * 回调通知地址
     */
    @NotNull(message = "回调通知地址不能为空")
    private String notifyUrl;


    private List<DiscountPushConsumerParam> list;


    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public List<DiscountPushConsumerParam> getList() {
        return list;
    }

    public void setList(List<DiscountPushConsumerParam> list) {
        this.list = list;
    }

    public static class DiscountPushConsumerParam{
        /**
         * 用户id
         */
        private String uid;


        /**
         * 领取次数
         */
        private Long receiveTimes;

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public Long getReceiveTimes() {
            return receiveTimes;
        }

        public void setReceiveTimes(Long receiveTimes) {
            this.receiveTimes = receiveTimes;
        }
    }
}
