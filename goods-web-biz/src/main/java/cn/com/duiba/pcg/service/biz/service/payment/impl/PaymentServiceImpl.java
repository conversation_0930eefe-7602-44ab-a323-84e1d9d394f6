package cn.com.duiba.pcg.service.biz.service.payment.impl;

import cn.com.duiba.activity.center.api.domain.dto.singleAward.SingleAwardFastRecordDto;
import cn.com.duiba.activity.center.api.domain.dto.singleAward.SingleAwardRecordDto;
import cn.com.duiba.activity.center.api.enums.singleAward.ActivityRecordStatusEnum;
import cn.com.duiba.activity.center.api.remoteservice.singleAward.RemoteSingleAwardFastRecordService;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnOrderStatusEnum;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnOrderTypeEnum;
import cn.com.duiba.activity.custom.center.api.dto.pinganshengqian.PingAnShengQianDeductionDto;
import cn.com.duiba.activity.custom.center.api.params.pinganshengqian.PingAnOrderParam;
import cn.com.duiba.activity.custom.center.api.remoteservice.pinganshengqian.RemotePingAnOrderService;
import cn.com.duiba.api.bo.reqresult.Result;
import cn.com.duiba.api.bo.reqresult.ResultBuilder;
import cn.com.duiba.api.enums.SubjectTypeEnum;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.boot.utils.NetUtils;
import cn.com.duiba.boot.utils.SpringEnvironmentUtils;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.domain.dto.DomainConfigDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.developer.center.api.remoteservice.RemoteSubjectRecordService;
import cn.com.duiba.developer.center.api.utils.WhiteAccessUtil;
import cn.com.duiba.order.center.api.dto.AmbOrderFastDto;
import cn.com.duiba.order.center.api.dto.AmbPaychannelOrdersDto;
import cn.com.duiba.order.center.api.dto.OrderFlowworkOperationDataDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteAmbOrderFastService;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.paycenter.dto.payment.charge.ChargeOrderDto;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.abc.AbcWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.alipay.AlipayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.bankofsuzhou.BankOfSuZhouWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.boc.BocWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.ccb.CcbWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.cib.CibPayWxChargeResponseDTO;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.citic.CiticWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayRequest;
import cn.com.duiba.paycenter.dto.payment.charge.cmb.CmbOneNetPayResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.bf.DuibaLiveBFChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.installment.DuibaLiveInstallmentChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.duibaLive.mp.DuibaLiveMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.hello.HelloPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbc.credits.IcbcCreditsChargeResp;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4AppChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.icbcelife.IcbcElife4WxChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.lshm.LshmChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.mock.MockWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeRequestDto;
import cn.com.duiba.paycenter.dto.payment.charge.ningbobank.charge.NbcbChargeResponseDto;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.shouxin.ShouxinPayChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.unionpay.UnionPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.weibo.WeiboChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wjrcb.WjrcbPayWxPubChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.BuildAuthorizationUrlRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayLiteChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayMpChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeRequest;
import cn.com.duiba.paycenter.dto.payment.charge.wxpay.WxPayWapChargeResponse;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeRequestDTO;
import cn.com.duiba.paycenter.dto.payment.charge.xib.charge.XibChargeResponseDTO;
import cn.com.duiba.paycenter.enums.BizTypeEnum;
import cn.com.duiba.paycenter.enums.ChannelEnum;
import cn.com.duiba.paycenter.enums.ChannelModeEnum;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteAlipayConfigService;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteChargeService;
import cn.com.duiba.paycenter.remoteservice.payment.RemoteWxPayConfigService;
import cn.com.duiba.pcg.constant.TopicConstant;
import cn.com.duiba.pcg.service.biz.IcbcElifeConfig;
import cn.com.duiba.pcg.service.biz.config.AbcConfig;
import cn.com.duiba.pcg.service.biz.config.CustomPayDomainConfig;
import cn.com.duiba.pcg.service.biz.config.LshmConfig;
import cn.com.duiba.pcg.service.biz.config.LuZhouRiBaoSheConfig;
import cn.com.duiba.pcg.service.biz.config.WxPayConfig;
import cn.com.duiba.pcg.service.biz.enums.OrderUriConstant;
import cn.com.duiba.pcg.service.biz.service.RocketMQMessageService;
import cn.com.duiba.pcg.service.biz.service.payment.PaymentService;
import cn.com.duiba.pcg.service.biz.util.DomainCrossUtils;
import cn.com.duiba.pcg.service.biz.vo.payment.BankOfSuZhouResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.CmbOneNetPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.DuibaLiveInstallmentPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.DuibaLiveMpPayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.HellopayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.ShouxinpayResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.WxPayLiteResponseVO;
import cn.com.duiba.pcg.service.biz.vo.payment.WxPayMpResponseVO;
import cn.com.duiba.wolf.dubbo.DubboResult;
import cn.com.duiba.wolf.utils.BeanUtils;
import cn.com.duiba.wolf.utils.DateUtils;
import cn.com.duibabiz.component.domain.DomainService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/11/20
 */
@Service
public class PaymentServiceImpl implements PaymentService {
    private static final Logger LOG = LoggerFactory.getLogger(PaymentServiceImpl.class);
    private static final int MAX_BODY_LENGTH = 128;
    private static final String DEFAULT_BODY = "商品兑换";
    private static final String ACTIVITY_DEFAULT_BODY = "活动工具支付";
    private static final String RETURN_URL = "/crecord/recordDetailNew?orderId=";


    private static final String CALLBACK_URL_SUFFIX = "https://www.abchina.com/abcpay";

    private static final String SPILT = "|";

    private static final String CALLBACK_URL_MID = "*";

    public static final String  EX_SPE_CHAT = "&1111&new";

    @Autowired
    private WxPayConfig wxPayConfig;
    @Autowired
    private RemoteChargeService remoteChargeService;
    @Resource
    private RemoteWxPayConfigService remoteWxPayConfigService;
    @Resource
    private RemoteAlipayConfigService remoteAlipayConfigService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private RemoteSubjectRecordService remoteSubjectRecordService;
    @Autowired
    private RemoteAppService remoteAppService;
    @Autowired
    private RemoteConsumerOrderSimpleService remoteConsumerOrderSimpleService;
    @Autowired
    private RocketMQMessageService rocketMQMessageService;
    @Autowired
    private TopicConstant topicConstant;

    @Autowired
    private  AbcConfig abcConfig;


    @Resource
    private LshmConfig lshmConfig;

    @Autowired
    private CustomPayDomainConfig customPayDomainConfig;

    @Autowired
    private LuZhouRiBaoSheConfig luZhouRiBaoSheConfig;
    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;
    @Autowired
    private IcbcElifeConfig icbcElifeConfig;
    @Autowired
    private RemoteAmbOrderFastService remoteAmbOrderFastService;
    @Autowired
    private RemoteSingleAwardFastRecordService remoteSingleAwardFastRecordService;

    @Resource
    private RemotePingAnOrderService remotePingAnOrderService;




    @Override
    public boolean supportWxPay(Long appId) {
        if (appId == null || appId == 0) {
            return false;
        }
        return wxPayConfig.getAppIds().contains(appId);
    }

    @Override
    public boolean supportLitePay(Long appId) {
        return wxPayConfig.canLitePay(appId);
    }

    @Override
    public Result<WxPayMpResponseVO> wxPayMpCharge(WxPayMpChargeRequest chargeRequest, OrdersDto ordersDto) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(DEFAULT_BODY);
        }
        chargeRequest.setChannelType(ChannelEnum.WX_PUB.getChannelType());
        chargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());
            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (java.util.Objects.nonNull(fasterDto)) {
                chargeRequest.setTimeExpire(fasterDto.getScanTime());
            }

            chargeRequest.setSubjectType(subjectType);
            WxPayMpChargeResponse chargeResponse = remoteChargeService.createWxPayMpCharge(chargeRequest);

            if (chargeResponse.isSuccess()) {
                return ResultBuilder.success(convertResponseVO(chargeResponse));
            }
            return ResultBuilder.fail(chargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<WxPayMpResponseVO> activityWxPayMpCharge(WxPayMpChargeRequest chargeRequest, SingleAwardRecordDto recordDto) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(ACTIVITY_DEFAULT_BODY);
        }
        chargeRequest.setChannelType(ChannelEnum.WX_PUB.getChannelType());
        chargeRequest.setBizType(BizTypeEnum.ACTIVITY.getCode());
        chargeRequest.setSubjectType(SubjectTypeEnum.FUJIAN_GUANWEN.getType());
        try {
            // 支付过期时间
            SingleAwardFastRecordDto fastDto = remoteSingleAwardFastRecordService.getByActivityOrderNumAndStatus(recordDto.getActivityOrderNum(), ActivityRecordStatusEnum.WAIT_PAY.getCode());
            if (java.util.Objects.nonNull(fastDto)) {
                chargeRequest.setTimeExpire(fastDto.getScanTime());
            }
            WxPayMpChargeResponse chargeResponse = remoteChargeService.createWxPayMpCharge(chargeRequest);

            if (chargeResponse.isSuccess()) {
                return ResultBuilder.success(convertResponseVO(chargeResponse));
            }
            return ResultBuilder.fail(chargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<WxPayLiteResponseVO> wxPayLiteChargeForPingan(WxPayLiteChargeRequest chargeRequest,OrdersDto ordersDto,String orderChannel) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(DEFAULT_BODY);
        }

        try {
            // 支付过期时间
            Date now = new Date();
            if (PingAnOrderTypeEnum.OWN.getType().equals(orderChannel)){
                chargeRequest.setBizType(BizTypeEnum.ORD.getCode());
                AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
                if (java.util.Objects.nonNull(fasterDto)) {
                    chargeRequest.setTimeExpire(fasterDto.getScanTime());
                }
            }else {
                chargeRequest.setBizType(BizTypeEnum.PINGAN.getCode());
                chargeRequest.setTimeExpire(DateUtil.offsetSecond(now,ordersDto.getFailType()));

            }
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            chargeRequest.setSubjectType(subjectType);
            WxPayLiteChargeResponse chargeResponse = remoteChargeService.createWxPayLiteCharge(chargeRequest);

            if (chargeResponse.isSuccess()) {
                PingAnShengQianDeductionDto qianDeductionDto = remotePingAnOrderService.queryByPayOrderNum(chargeRequest.getBizOrderNo());
                if (qianDeductionDto!= null) {
                    PingAnOrderParam pingAnOrderParam = new PingAnOrderParam();
                    pingAnOrderParam.setId(qianDeductionDto.getId());
                    String updateStatus = chargeResponse.isSuccess()?PingAnOrderStatusEnum.PAYMENT_RECEIVED_SUCCESS.getCode() + "":PingAnOrderStatusEnum.PAYMENT_RECEIVED_FAILED.getCode() + "";
                    pingAnOrderParam.setOrderStatus(updateStatus);
                    remotePingAnOrderService.updateRecord(pingAnOrderParam);
                }
                return ResultBuilder.success(convertResponseVO(chargeResponse));
            }
            return ResultBuilder.fail(chargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<WxPayLiteResponseVO> wxPayLiteCharge(WxPayLiteChargeRequest chargeRequest, OrdersDto ordersDto) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(DEFAULT_BODY);
        }
        chargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());
            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (java.util.Objects.nonNull(fasterDto)) {
                chargeRequest.setTimeExpire(fasterDto.getScanTime());
            }

            chargeRequest.setSubjectType(subjectType);
            ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(RequestLocal.getCid()).getResult();
            if (consumerExtraDto != null) {
                String consumerExtra = consumerExtraDto.getJson();
                JSONObject jsonObject = JSON.parseObject(consumerExtra);
                if (jsonObject != null) {
                    chargeRequest.setOpenId(jsonObject.getString("openId"));
                }
            }
            WxPayLiteChargeResponse chargeResponse = remoteChargeService.createWxPayLiteCharge(chargeRequest);

            if (chargeResponse.isSuccess()) {
                return ResultBuilder.success(convertResponseVO(chargeResponse));
            }
            return ResultBuilder.fail(chargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> wxPayWapCharge(WxPayWapChargeRequest chargeRequest, OrdersDto ordersDto) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(chargeRequest.getBody().substring(0, 55) + "...");
        }
        chargeRequest.setChannelType(ChannelEnum.WX_WAP.getChannelType());
        chargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());
            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (java.util.Objects.nonNull(fasterDto)) {
                chargeRequest.setTimeExpire(fasterDto.getScanTime());
            }

            chargeRequest.setSubjectType(subjectType);
            WxPayWapChargeResponse wapChargeResponse = remoteChargeService.createWxPayWapCharge(chargeRequest);
            if (wapChargeResponse.isSuccess()) {
                DomainConfigDto domainConfigDto = DomainCrossUtils.getSystemDomain(chargeRequest.getAppId(), domainService);
                String redirectUrl = "https:" + domainConfigDto.getTradeDomain() + RETURN_URL + ordersDto.getId() + "&after=1";
                try {
                    return ResultBuilder.success(wapChargeResponse.getMwebUrl()
                            + "&redirect_url=" + URLEncoder.encode(redirectUrl, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    return ResultBuilder.fail("编码不支持");
                }
            }
            return ResultBuilder.fail(wapChargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> wxPayH5Charge(WxPayWapChargeRequest chargeRequest, OrdersDto ordersDto,String redirectUrl) {
        if (StringUtils.isBlank(chargeRequest.getBody()) || chargeRequest.getBody().length() > MAX_BODY_LENGTH) {
            chargeRequest.setBody(chargeRequest.getBody().substring(0, 55) + "...");
        }
        chargeRequest.setChannelType(ChannelEnum.WX_WAP.getChannelType());
        chargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());
            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (java.util.Objects.nonNull(fasterDto)) {
                chargeRequest.setTimeExpire(fasterDto.getScanTime());
            }

            chargeRequest.setSubjectType(subjectType);
            WxPayWapChargeResponse wapChargeResponse = remoteChargeService.createWxPayWapCharge(chargeRequest);
            if (wapChargeResponse.isSuccess()) {
                if(StringUtils.isBlank(redirectUrl)){
                    DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(chargeRequest.getAppId(), domainService);
                    redirectUrl = getPayDomian(domainConfig,ordersDto.getAppId(),ordersDto) + OrderUriConstant.ORDER_PAY_RESULT_URL + ordersDto.getId();
                }
                try {
                    return ResultBuilder.success(wapChargeResponse.getMwebUrl()
                            + "&redirect_url=" + URLEncoder.encode(redirectUrl, "utf-8"));
                } catch (UnsupportedEncodingException e) {
                    return ResultBuilder.fail("编码不支持");
                }
            }
            return ResultBuilder.fail(wapChargeResponse.getMessage());
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    private String getPayDomian(DomainConfigDto domainConfig,Long appId,OrdersDto ordersDto) {
        if (luZhouRiBaoSheConfig.getAppIds().contains(appId) && Objects.equal(ordersDto.getAppItemId(),199293172594254L)){
            return customPayDomainConfig.getPayUrlDomain().replace("//activity","//"+appId+".activity");
        }

        if(SpringEnvironmentUtils.isTestEnv() || SpringEnvironmentUtils.isDevEnv() || customPayDomainConfig.getAppIdSet().contains(appId)){
            return "https:" + domainConfig.getTradeDomain();
        }

        return customPayDomainConfig.getPayDomainByAppId(appId);

    }


    //已经过期不用调用了，使用buildCodeRequestUrlNew
    @Override
    @Deprecated
    public Result<String> buildCodeRequestUrl(Long appId, OrdersDto ordersDto) {
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());


            String redirectUri = customPayDomainConfig.getPayDomainByAppId(appId) + "/ambPay/ambPay?orderId=" + ordersDto.getId();
            BuildAuthorizationUrlRequest request = new BuildAuthorizationUrlRequest();
            request.setAppId(appId);
            request.setRedirectURI(redirectUri);
            request.setSubjectType(subjectType);
            return ResultBuilder.success(remoteChargeService.oauth2buildAuthorizationUrlBySubject(request));
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> buildCodeRequestUrlNew(Long appId, HttpServletRequest request, OrdersDto ordersDto) {
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());

            StringBuilder redirectUri = new StringBuilder();

            //redirectUri.append(RequestTool.getServerPath(request));
            redirectUri.append(customPayDomainConfig.getPayDomainByAppId(appId));
            redirectUri.append(OrderUriConstant.AMB_CASHIER_URL);

            redirectUri.append(request.getParameter("orderId"));

            BuildAuthorizationUrlRequest authorizationUrlRequest = new BuildAuthorizationUrlRequest();
            authorizationUrlRequest.setAppId(appId);
            authorizationUrlRequest.setRedirectURI(redirectUri.toString());
            authorizationUrlRequest.setSubjectType(subjectType);
            return ResultBuilder.success(remoteChargeService
                    .oauth2buildAuthorizationUrlBySubject(authorizationUrlRequest));
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> buildCodeRequestUrlForXst(Long appId, Long developerId,String redirectUrl) {
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(developerId, new Date());
            BuildAuthorizationUrlRequest authorizationUrlRequest = new BuildAuthorizationUrlRequest();
            authorizationUrlRequest.setAppId(appId);
            authorizationUrlRequest.setRedirectURI(redirectUrl);
            authorizationUrlRequest.setSubjectType(subjectType);
            return ResultBuilder.success(remoteChargeService
                    .oauth2buildAuthorizationUrlBySubject(authorizationUrlRequest));
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> buildCodeRequestUrlAjax(Long appId,Long developerId,String url) {
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(developerId, new Date());
            StringBuilder redirectUri = new StringBuilder();
            redirectUri.append(customPayDomainConfig.getPayDomainByAppId(appId));
            redirectUri.append(url);
            BuildAuthorizationUrlRequest authorizationUrlRequest = new BuildAuthorizationUrlRequest();
            authorizationUrlRequest.setAppId(appId);
            authorizationUrlRequest.setRedirectURI(redirectUri.toString());
            authorizationUrlRequest.setSubjectType(subjectType);
            return ResultBuilder.success(remoteChargeService
                    .oauth2buildAuthorizationUrlBySubject(authorizationUrlRequest));
        } catch (BizException e) {
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public String getAmbPayChannelType(Long appId, String paymentChannelType) {
        if (ChannelModeEnum.DUIBA.getCode().equals(remoteWxPayConfigService.getChannelMode(appId, paymentChannelType))) {
            return AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeWeixin;
        }
        return AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf;
    }

    @Override
    public String getAliPayChannelType(Long appId, String paymentChannelType) {
        if (ChannelModeEnum.DUIBA.getCode().equals(remoteAlipayConfigService.getChannelMode(appId, paymentChannelType))) {
            return AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeAlipay;
        }
        return AmbPaychannelOrdersDto.PayChannelOrdersPayChannelTypeSelf;
    }

    @Override
    public Result<String> alipayWapCharge(AlipayWapChargeRequest request, OrdersDto ordersDto) {
        try {
            // 获取订单业务主体信息
            String subjectType = remoteSubjectRecordService.getSubjectByDevelopIdAndData(ordersDto.getDeveloperId(), ordersDto.getGmtCreate());
            // 检查订单主体是否发生变更
            checkOrderSubject(subjectType, ordersDto.getId());
            AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
            if (java.util.Objects.nonNull(fasterDto)) {
                request.setTimeExpire(fasterDto.getScanTime());
            }

            request.setSubjectType(subjectType);
            AlipayWapChargeResponse response = remoteChargeService.createAlipayWapCharge(request);
            return ResultBuilder.success(response.getHtml());
        } catch (BizException e) {
            LOG.error("alipayWapCharge", e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public ChargeOrderDto findByOutTradeNo(String orderNo) {
        try {
            return remoteChargeService.findByOrderNo(orderNo);
        } catch (Exception e) {
            LOG.warn("获取订单详情出错", e);
            return null;
        }
    }

    @Override
    public Result<BankOfSuZhouResponseVO> szPay(BankOfSuZhouWapChargeRequest request) {
        try {
            BankOfSuZhouWapChargeResponse response = remoteChargeService.createSuZhouCharge(request);
            BankOfSuZhouResponseVO responseVO = new BankOfSuZhouResponseVO();
            responseVO.setGetwayUrl(response.getGetWayUrl());
            responseVO.setParams(response.getParams());
            return ResultBuilder.success(responseVO);
        } catch (BizException e) {
            LOG.error("苏州银行支付加密失败", e);
            return ResultBuilder.fail(e.getMessage());
        }
    }

    @Override
    public Result<String> wjrcbWxPubPay(WjrcbPayWxPubChargeRequest request) {
        if (StringUtils.isBlank(request.getDesc()) || request.getDesc().length() > MAX_BODY_LENGTH) {
            request.setDesc(DEFAULT_BODY);
        }
        request.setBizType(BizTypeEnum.ORD.getCode());
        try {
            WjrcbPayWxPubChargeResponse response = remoteChargeService.createWjrcbWxPubCharge(request);
            if (response.isSuccess()) {
                return ResultBuilder.success(response.getPrePay());
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("苏州农商行下单失败:{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<String> ccbPay(CcbWapChargeRequest request) {
        request.setBizType(BizTypeEnum.ORD.getCode());
        if (StringUtils.isBlank(request.getGoodsDetail()) || request.getGoodsDetail().length() > MAX_BODY_LENGTH) {
            request.setGoodsDetail(DEFAULT_BODY);
        }
        try {
            CcbWapChargeResponse response = remoteChargeService.createCcbCharge(request);
            if (response.isSuccess()) {
                return ResultBuilder.success(response.getPayUrl());
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("建行下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<String> unionPay(UnionPayWapChargeRequest request) {
        request.setBizType(BizTypeEnum.ORD.getCode());
        if (StringUtils.isBlank(request.getGoodsDetail()) || request.getGoodsDetail().length() > MAX_BODY_LENGTH) {
            request.setGoodsDetail(DEFAULT_BODY);
        }
        try {
            UnionPayWapChargeResponse response = remoteChargeService.createUnionPayCharge(request);
            if (response.isSuccess()) {
                return ResultBuilder.success(response.getTn());
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("银联云闪付下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<String> abcPay(AbcWapChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            AbcWapChargeResponse response = remoteChargeService.createAbcCharge(request);
            if (response.isSuccess()) {
                //zhe边还要特殊处理下
                String[] payUrlNums =  response.getPayUrl().split("=");
                //构造掌银可以打开的url
                OrdersDto ordersDto = remoteConsumerOrderSimpleService.findByOrderNum(response.getOrderNo()).getResult();
                //获取域名
                DomainConfigDto domainConfigDto = domainService.getSystemDomain(ordersDto.getAppId());
                //拼接农行  免登
                String  transforUrl =  abcConfig.getGuangxiAppId().equals(ordersDto.getAppId()) ? AbcConfig.ORDER_PAY_RESULT_URL : AbcConfig.ABC_AUTHURL;
                String  abcAutoURL = RequestLocal.getRequest().getScheme() + ":" + domainConfigDto.getTradeDomain()  +  transforUrl + ordersDto.getId();
                String url =  SPILT+payUrlNums[1]+EX_SPE_CHAT+CALLBACK_URL_MID+abcAutoURL;
                LOG.info("abcPay callbackUrl = {}  response= {}",url,response);

                String finalUrl = CALLBACK_URL_SUFFIX+ url;
                return ResultBuilder.success(finalUrl);
            } else {
                LOG.info("abcPay fail response= {}",response);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("农行下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<HellopayResponseVO> helloPay(HelloPayChargeRequest request) {
        if (StringUtils.isBlank(request.getDesc()) || request.getDesc().length() > MAX_BODY_LENGTH) {
            request.setDesc(DEFAULT_BODY);
        }
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            request.setChannelType(ChannelEnum.HELLO_PAY.getChannelType());
            HelloPayChargeResponse response = remoteChargeService.createHelloPayCharge(request);
            if (response.isSuccess()) {
                HellopayResponseVO hellopayResponseVO = new HellopayResponseVO();
                hellopayResponseVO.setHelloOrderId(response.getHelloOrderId());
                hellopayResponseVO.setAmount(request.getAmount());
                return ResultBuilder.success(hellopayResponseVO);
            } else {
                LOG.info("helloPay fail response= {}",response);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("哈啰零钱下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<CmbOneNetPayResponseVO> cmbOneNetPay(CmbOneNetPayRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            if (StringUtils.isBlank(request.getGoodsDetail()) || request.getGoodsDetail().length() > MAX_BODY_LENGTH) {
                request.setGoodsDetail(DEFAULT_BODY);
            }
            CmbOneNetPayResponse response = remoteChargeService.createCmbOneNetCharge(request);
            if(response.isSuccess()){
                CmbOneNetPayResponseVO vo = new CmbOneNetPayResponseVO();
                vo.setGetwayUrl(response.getOneNetPayUrl());
                vo.setParams(response.getParams());
                return ResultBuilder.success(vo);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("招行一网通下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<ShouxinpayResponseVO> shouxinPay(ShouxinPayChargeRequest request) {
        if (StringUtils.isBlank(request.getDesc()) || request.getDesc().length() > MAX_BODY_LENGTH) {
            request.setDesc(DEFAULT_BODY);
        }
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            request.setChannelType(ChannelEnum.SHOUXIN_PAY.getChannelType());
            ShouxinPayChargeResponse response = remoteChargeService.createShouxinPayCharge(request);
            if (response.isSuccess()) {
                ShouxinpayResponseVO shouxinpayResponseVO = new ShouxinpayResponseVO();
                shouxinpayResponseVO.setPayGateWayUrl(response.getPayGateWayUrl());
                return ResultBuilder.success(shouxinpayResponseVO);
            }
            LOG.info("shouxinPay fail response= {}",JSONObject.toJSONString(response));
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("首信支付下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<CiticWapChargeResponse> citicPay(CiticWapChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            CiticWapChargeResponse response = remoteChargeService.createCiticCharge(request);
            if (response.isSuccess()) {
                LOG.info("citicPay response= {}", JSONObject.toJSONString(response));
                return ResultBuilder.success(response);
            } else {
                LOG.info("citicPay fail response= {}",JSONObject.toJSONString(response));
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("中信银行下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<BocWapChargeResponse> bocPay(BocWapChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            BocWapChargeResponse response = remoteChargeService.createBocCharge(request);
            if (response.isSuccess()) {
                LOG.info("bocPay  response= {}", JSONObject.toJSONString(response));
                return ResultBuilder.success(response);
            } else {
                LOG.info("bocPay fail response= {}",JSONObject.toJSONString(response));
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("中国银行下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    private WxPayMpResponseVO convertResponseVO(WxPayMpChargeResponse chargeResponse) {
        WxPayMpResponseVO vo = BeanUtils.copy(chargeResponse, WxPayMpResponseVO.class);
        vo.setPrepayId("prepay_id=" + vo.getPrepayId());
        return vo;
    }

    private WxPayLiteResponseVO convertResponseVO(WxPayLiteChargeResponse chargeResponse) {
        WxPayLiteResponseVO vo = BeanUtils.copy(chargeResponse, WxPayLiteResponseVO.class);
        vo.setPrepayId("prepay_id=" + vo.getPrepayId());
        return vo;
    }

    // 检查订单支付时业务主体是否发生变更
    private void checkOrderSubject(String orderSubjectType, Long orderId) throws BizException {
        DubboResult<AppSimpleDto> appSimpleDtoDubboResult = remoteAppService.getSimpleApp(RequestLocal.getAppId());
        AppSimpleDto appSimpleDto = appSimpleDtoDubboResult.getResult();
        if (!Objects.equal(orderSubjectType, appSimpleDto.getSubject())) {
            //发送MQ信息，将订单处理成失败
            OrderFlowworkOperationDataDto dto = new OrderFlowworkOperationDataDto();
            dto.setOrderId(orderId);
            dto.setType(OrderFlowworkOperationDataDto.TYPE_SZBANK_FORCE_ORDER_FAIL);
            dto.setError4consumer("订单超时，请重新下单。");
            dto.setError4developer("业务类型变更，用户需重新下单。");
            dto.setError4admin("业务类型变更，用户需重新下单。");
            rocketMQMessageService.sendMsg(topicConstant.orderflowworkoperation, "", null, JSONObject.toJSONString(dto));
            throw new BizException("订单超时，请重新下单。");
        }
    }

    /**
     * 注mock 支付
     * @param request
     * @return
     */
    @Override
    public Result<MockWapChargeResponse> mockPay(MockWapChargeRequest request) throws BizException{
        if(SpringEnvironmentUtils.isPreEnv() || SpringEnvironmentUtils.isProdEnv()){
            throw new BizException("线上环境不能mock");
        }

        //避免SpringEnvironmentUtils失效，再加一层判断
        if(!"true".equals(System.getProperty("mockLuckBag"))){
            throw new BizException("此环境不能mock");
        }

        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            MockWapChargeResponse response = remoteChargeService.createMockCharge(request);
            if (response.isSuccess()) {
                LOG.info("mockPay response= {}", JSONObject.toJSONString(response));
                return ResultBuilder.success(response);
            } else {
                LOG.info("mockPay fail response= {}",JSONObject.toJSONString(response));
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("mock下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<DuibaLiveMpPayResponseVO> duibaLiveMpPay(DuibaLiveMpChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            request.setChannelType(ChannelEnum.DUIBA_LIVE_MP_PAY.getChannelType());
            DuibaLiveMpChargeResponse response = remoteChargeService.createDuibaLiveMpCharge(request);
            if (response.isSuccess()) {
                DuibaLiveMpPayResponseVO vo = new DuibaLiveMpPayResponseVO();
                BeanUtils.copy(response,vo);
                return ResultBuilder.success(vo);
            } else {
                LOG.info("duibaLiveMpPay fail response= {}",response);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("duibaLiveMpPay下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<DuibaLiveInstallmentPayResponseVO> duibaLiveInstallmentPay(DuibaLiveInstallmentChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            request.setChannelType(ChannelEnum.DUIBA_LIVE_INSTALLMENT_PAY.getChannelType());
            //添加支持银行限制
            String fqzfzcyhpz_str = WhiteAccessUtil.selectWhiteListJsonConfig("FQZFZCYHPZ_guoyunlong");
            String appId = String.valueOf(RequestLocal.getAppId());
            if (StringUtils.isNotBlank(fqzfzcyhpz_str)){
                Map<String,String> fqzfzcyhpz_map = JSONObject.parseObject(fqzfzcyhpz_str, HashMap.class);
                for (Map.Entry<String, String> fqzfzcyhpz : fqzfzcyhpz_map.entrySet()) {
                    if(fqzfzcyhpz.getKey().equals(appId)){
                        LOG.info("payType:{},payName:{},appId:{},subbBankName:{}",ChannelEnum.DUIBA_LIVE_INSTALLMENT_PAY.getChannelType(),ChannelEnum.DUIBA_LIVE_INSTALLMENT_PAY.getChannelName(),fqzfzcyhpz.getKey(),fqzfzcyhpz.getValue());
                        request.setSuppBankName(fqzfzcyhpz.getValue());
                        break;
                    }
                }
            }
            DuibaLiveInstallmentChargeResponse response = remoteChargeService.createDuibaLiveInstallmentCharge(request);
            if (response.isSuccess()) {
                DuibaLiveInstallmentPayResponseVO vo = new DuibaLiveInstallmentPayResponseVO();
                BeanUtils.copy(response,vo);
                return ResultBuilder.success(vo);
            } else {
                LOG.info("duibaLiveInstallmentPay fail response= {}",response);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("duibaLiveInstallmentPay下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public Result<DuibaLiveMpPayResponseVO> duibaLiveBFPay(DuibaLiveBFChargeRequest request) {
        try {
            request.setBizType(BizTypeEnum.ORD.getCode());
            request.setChannelType(ChannelEnum.DUIBA_LIVE_BF_PAY.getChannelType());
            DuibaLiveBFChargeResponse response = remoteChargeService.createDuibaLiveBFCharge(request);
            if (response.isSuccess()) {
                DuibaLiveMpPayResponseVO vo = new DuibaLiveMpPayResponseVO();
                BeanUtils.copy(response,vo);
                return ResultBuilder.success(vo);
            } else {
                LOG.info("duibaLiveBFPay fail response= {}",response);
            }
            return ResultBuilder.fail(response.getMessage());
        } catch (Exception e) {
            LOG.warn("duibaLiveBFPay下单失败：{}", request, e);
            return ResultBuilder.fail("下单失败");
        }
    }

    @Override
    public NbcbChargeResponseDto ningboCreateCharge(NbcbChargeRequestDto chargeRequestDto) throws BizException {
        if (StringUtils.isBlank(chargeRequestDto.getOrderDesc()) || chargeRequestDto.getOrderDesc().length() > MAX_BODY_LENGTH) {
            chargeRequestDto.setOrderDesc(DEFAULT_BODY);
        }
        chargeRequestDto.setBizType(BizTypeEnum.ORD.getCode());
        chargeRequestDto.setChannelType(ChannelEnum.NINGBO_BANK_PAY.getChannelType());
        return remoteChargeService.createNingboBankPayCharge(chargeRequestDto);
    }


    @Override
    public IcbcCreditsChargeResp icbcCreditsCreateCharge(IcbcCreditsChargeRequest icbcCreditsChargeRequest) throws BizException {
        if (StringUtils.isBlank(icbcCreditsChargeRequest.getBizOrderBrief()) || icbcCreditsChargeRequest.getBizOrderBrief().length() > MAX_BODY_LENGTH) {
            icbcCreditsChargeRequest.setBizOrderBrief(DEFAULT_BODY);
        }
        icbcCreditsChargeRequest.setBizType(BizTypeEnum.ORD.getCode());
        icbcCreditsChargeRequest.setChannelType(ChannelEnum.BANK_OF_ICBC_PAY_CREDITS.getChannelType());
        return remoteChargeService.createIcbcCreditsPayCharge(icbcCreditsChargeRequest);
    }

    @Override
    public IcbcElife4AppChargeResponse icbcElife4AppCharge(OrdersDto ordersDto) throws BizException {
        IcbcElife4AppChargeRequest request = new IcbcElife4AppChargeRequest();
        //基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.ICBC_ELIFE_PAY_APP.getChannelType());
        request.setAppId(ordersDto.getAppId());
        //业务参数
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dayDtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dayStr = dayDtf.format(now);
        DateTimeFormatter timeDtf = DateTimeFormatter.ofPattern("HHmmss");
        String timeStr = timeDtf.format(now);

        String custId = this.getIcbcElifeCustId(ordersDto.getConsumerId());
        if (StringUtils.isBlank(custId)) {
            throw new BizException("custId为空");
        }
        request.setCustId(custId);
        request.setTradeDate(dayStr);
        request.setTradeTime(timeStr);
        //半小时有效期，通积分商城待支付过期时间
        request.setPayExpire(1800 + "");
        request.setNotifyFlag("1");
        request.setNotifyUrl(icbcElifeConfig.getNotifyUrl4App());
        request.setAutoSubmitFlag("1");
        request.setAllPointsFlag("1");
        request.setGoodPoints(getIcbcElifeGoodsPoints(request.getAmount()) + "");
        IcbcElife4AppChargeResponse response = remoteChargeService.createIcbcElifeCharge4App(request);
        return response;
    }

    @Override
    public IcbcElife4WxChargeResponse icbcElife4WxCharge(OrdersDto ordersDto) throws BizException {
        IcbcElife4WxChargeRequest request = new IcbcElife4WxChargeRequest();
        //基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.ICBC_ELIFE_PAY_WX.getChannelType());
        request.setAppId(ordersDto.getAppId());
        //业务参数
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter dayDtf = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dayStr = dayDtf.format(now);
        DateTimeFormatter timeDtf = DateTimeFormatter.ofPattern("HHmmss");
        String timeStr = timeDtf.format(now);

        request.setTradeDate(dayStr);
        request.setTradeTime(timeStr);
        request.setPayExpire(1800 + "");
        request.setNotifyFlag("0");
        request.setTporderCreateIp(NetUtils.getLocalIp());
        IcbcElife4WxChargeResponse response = remoteChargeService.createIcbcElifeCharge4Wx(request);
        return response;
    }

    @Override
    public CibPayWxChargeResponseDTO cibCreateCharge(OrdersDto ordersDto) throws BizException {
        String openId = "";
        ConsumerExtraDto result = remoteConsumerExtraService.findByConsumerId(ordersDto.getConsumerId()).getResult();
        if (StringUtils.isNotBlank(result.getJson())) {
            JSONObject jsonObject = JSONObject.parseObject(result.getJson());
            openId = jsonObject.get("openId") == null ? "" : jsonObject.get("openId").toString();
        }
        if (StringUtils.isBlank(openId)) {
            throw new BizException("openId不存在");
        }
        CibPayWxChargeRequestDTO request = new CibPayWxChargeRequestDTO();
        //基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.CIB_WX_PAY.getChannelType());
        request.setAppId(ordersDto.getAppId());
        //业务参数
        request.setOrderDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setOpenId(openId);
        request.setIp(NetUtils.getLocalIp());
        request.setMerDate(ordersDto.getGmtCreate());
        return remoteChargeService.createCibWxPayCharge(request);
    }

    @Override
    public String weiboCharge(OrdersDto ordersDto, String itemName) throws BizException {
        WeiboChargeRequest request = new WeiboChargeRequest();
        // 基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.WEIBO_PAY.getChannelType());
        request.setAppId(ordersDto.getAppId());
        // 业务参数
        request.setSubject(itemName);
        ConsumerDto consumerDto = RequestLocal.getConsumerDO();
        DomainConfigDto domainConfig = DomainCrossUtils.getSystemDomain(consumerDto.getAppId(), domainService);
        request.setReturnUrl("https:" + domainConfig.getTradeDomain() + OrderUriConstant.ORDER_PAY_RESULT_URL + ordersDto.getId());
        // 支付超时时间，对吧订单是30分钟，这里是30分钟-5秒
        request.setExpire((int)TimeUnit.MINUTES.toSeconds(30) - 5);

        WeiboChargeResponse weiboChargeResponse = remoteChargeService.createWeiboCharge(request);
        if (weiboChargeResponse.isSuccess()) {
            return weiboChargeResponse.getPayInfo()
                    .entrySet()
                    .stream()
                    .filter(java.util.Objects::nonNull)
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining("&"));
        }
       throw new BizException(weiboChargeResponse.getMessage());
    }

    @Override
    public String lshmCharge(OrdersDto ordersDto) throws BizException {
        LshmChargeRequest request = new LshmChargeRequest();
        // 基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.LSHM_PAY.getChannelType());
        request.setAppId(ordersDto.getAppId());
        request.setTitle(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));

        // 门店编码
        String storeCode = Optional.ofNullable(ordersDto.getExtraInfo())
                .map(JSON::parseObject)
                .map(x -> x.getString(LshmConfig.STORE_CODE))
                .orElse(lshmConfig.getStoreCode(ordersDto.getAppId()));
        // 回调地址
        request.setNotifyUrl(lshmConfig.getDuibaNotifyUrl());
        request.setStoreCode(storeCode);
        // 用户openId
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(RequestLocal.getCid()).getResult();
        String openId = Optional.ofNullable(consumerExtraDto)
                .map(ConsumerExtraDto::getJson)
                .map(JSON::parseObject)
                .map(x -> x.getString(LshmConfig.OPEN_ID))
                .orElse(null);
        request.setWxOpenId(openId);
        // 测试环境Mock
        if (SpringEnvironmentUtils.isTestEnv()){
            if (StringUtils.isBlank(openId)){
                request.setWxOpenId(lshmConfig.getTestOpenId());
            }
            if (StringUtils.isBlank(storeCode)){
                request.setStoreCode(lshmConfig.getTestStoreCode());
            }
        }
        if  (StringUtils.isBlank(request.getWxOpenId())) {
            throw new BizException("openId不存在");
        }
        if  (StringUtils.isBlank(request.getStoreCode())) {
            throw new BizException("storeCode不存在");
        }
        // 支付超时时间，对吧订单是30分钟，这里是30分钟-5秒
        request.setCloseOrderTime((DateUtils.secondsAddOrSub(new Date(),1795)).getTime());
       LshmChargeResponse lshmChargeResponse = remoteChargeService.createLshmCharge(request);
        if (lshmChargeResponse.isSuccess()) {
            return lshmChargeResponse.getPayParams();
        }
        throw new BizException(lshmChargeResponse.getMessage());
    }

    @Override
    public XibChargeResponseDTO xibCharge(OrdersDto ordersDto) throws BizException {

        AppSimpleDto app = remoteAppService.getSimpleApp(ordersDto.getAppId()).getResult();

        XibChargeRequestDTO request = new XibChargeRequestDTO();
        //基本参数
        request.setAmount(ordersDto.getConsumerPayPrice().intValue());
        request.setBizOrderNo(ordersDto.getOrderNum());
        request.setBizType(BizTypeEnum.ORD.getCode());
        request.setChannelType(ChannelEnum.XIB_PAY.getChannelType());
        request.setAppId(ordersDto.getAppId());
        AmbOrderFastDto fasterDto = remoteAmbOrderFastService.findByOrderIdAndTypeNew(ordersDto.getId(), AmbOrderFastDto.OrderFastTypeWaitPay, null);
        if (fasterDto != null) {
            request.setScanTime(fasterDto.getScanTime());
        }
        // 业务参数
        request.setOrderDesc(new String(ordersDto.getBrief().getBytes(), StandardCharsets.UTF_8));
        request.setTxnAmt(ordersDto.getConsumerPayPrice());
        request.setOrderNo(ordersDto.getOrderNum());
        request.setIp(ordersDto.getIp());
        request.setAppKey(app.getAppKey());
        request.setAppSecret(app.getAppSecret());
        request.setUid(RequestLocal.getPartnerUserId());
        return remoteChargeService.createXibPayCharge(request);
    }

    /**
     * order_amt（金额，元）x2000/3来换算 good_points
     * order_amt（金额，分）x20/3来换算 good_points
     * 本次活动涉及到的值是固定的，order_amt传15分(0.15元），good_points 传 100积分
     *
     * @param amount 单位：分
     * @return
     */
    private int getIcbcElifeGoodsPoints(int amount) {
        return amount * 20 / 3;
    }

    private String getIcbcElifeCustId(Long consumerId) {
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(consumerId).getResult();
        if (consumerExtraDto != null) {
            String json = consumerExtraDto.getJson();
            if (StringUtils.isNotBlank(json)) {
                JSONObject object = JSONObject.parseObject(json);
                if (null != object) {
                    String custId = object.getString("customerId");
                    return custId;
                }
            }
        }
        return null;
    }

}
