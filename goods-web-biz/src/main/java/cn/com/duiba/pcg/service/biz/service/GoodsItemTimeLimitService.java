package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsLimitConfig;
import cn.com.duiba.goods.center.api.remoteservice.dto.marketing.MarketingItemCreditsLimitExchangeType;
import cn.com.duiba.goods.center.api.remoteservice.tool.ExtraInfoUtils;
import cn.com.duiba.pcg.exception.GoodsWebException;
import cn.com.duiba.pcg.service.biz.config.CommonConfig;
import cn.com.duiba.pcg.service.biz.vo.cmb.CmbItemConfigVO;
import cn.com.duiba.pcg.service.common.DateUtil;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 限时商品Service
 *
 * <AUTHOR> modified by fxt (12-08 修改 开发者也可以配置自有商品的限时) (12-18 修改
 *         开发者也可以配置兑吧商品的限时)
 */
@Service("goodsItemTimeLimitService")
public class GoodsItemTimeLimitService {
    private static final Logger LOG = LoggerFactory.getLogger(GoodsItemTimeLimitService.class);
	private static final char TIME_SEPARATOR_CHAR = '-';
	private static final String TIME_PATTERN = "HH:mm";
	private static final String TIME_SECOND_PATTERN = "yyyy-MM-dd HH:mm:ss";
	private static final String TIME_DAY_PATTERN = "yyyy-MM-dd";
	private static final String TIME_NOT_DATE_SECOND_PATTERN= "HH:mm:ss";



	@Autowired
	private CommonConfig commonConfig;
	/**
	 * 日期限制兑换验证
	 *
	 * @param itemKey
	 * @return 是否可兑
	 */
	public boolean limitDayCanTakeOrder(ItemKeyDto itemKey) {//NOSONAR
		String limitDate = null;
		limitDate = getLimitDate(itemKey,limitDate);
		if (StringUtils.isNotBlank(limitDate) && !StringUtils.equals(limitDate, "no")) {
		    String[] dayBetween = StringUtils.split(limitDate, ',');
		    if(dayBetween.length==2){
		        String startDay = dayBetween[0];
	            String endDay = dayBetween[1];
				CmbItemConfigVO cmbItemConfigVO = commonConfig.isCmbLimitItem(itemKey.getAppId(), itemKey.getAppItem().getId());
				if(cmbItemConfigVO != null){
					String limitStartDate = startDay.trim() + " " + cmbItemConfigVO.getMinSecs().get(0);
					String limitEndDate = endDay.trim() + " " + cmbItemConfigVO.getMinSecs().get(1);
					Date start = DateUtils.getSecondDate(limitStartDate);
					Date end = DateUtils.getSecondDate(limitEndDate);
					if (System.currentTimeMillis() < start.getTime() || System.currentTimeMillis() > end.getTime()) {
						return false;
					}
				}else{
					Date start = DateUtil.getDayDate(startDay);
					Calendar cal = Calendar.getInstance();
					cal.setTime(DateUtil.getDayDate(endDay));
					cal.add(Calendar.DATE, 1);
					Date end = cal.getTime();
					if (System.currentTimeMillis() < start.getTime() || System.currentTimeMillis() > end.getTime()) {
						return false;
					}
				}

		    }
		}
		return true;
	}

	/**
	 * 时间限制兑换验证，精确到分
	 *
	 * @param itemKey
	 * @return
	 */
	public boolean limitTimeCanTakeOrder(ItemKeyDto itemKey) {
		if (Objects.nonNull(itemKey.getAppItem()) && Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
			String[] timeBetween = StringUtils.split(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime(), TIME_SEPARATOR_CHAR);
			if (timeBetween.length != 2) {
				return true;
			}
			LocalTime start = LocalTime.parse(timeBetween[0], DateTimeFormatter.ofPattern(TIME_PATTERN));
			LocalTime end = LocalTime.parse(timeBetween[1], DateTimeFormatter.ofPattern(TIME_PATTERN));
			LocalTime now = LocalTime.now();
			// 含头不含尾
			if (end.isBefore(now) || now.isBefore(start)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 限制兑换开始时间
	 * @param itemKey
	 * @return trur 未命中限制
	 */
	public boolean limitStartTimeCanTakeOrder(ItemKeyDto itemKey) {
		if (Objects.nonNull(itemKey.getAppItem()) && Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto())
				&& StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getLimitStartTime())) {
			String limitStartTime = itemKey.getAppItem().getMarketingItemCreditsDto().getLimitStartTime();
			if (StringUtils.isBlank(limitStartTime)){
				return true;
			}
			LocalDateTime startTime = LocalDateTime.parse(limitStartTime, DateTimeFormatter.ofPattern(TIME_SECOND_PATTERN));
			LocalDateTime now = LocalDateTime.now();
			return !now.isBefore(startTime);
		}
		return true;
	}

	/**
	 * 限制兑换开始时间
	 * @param itemKey
	 * @return trur 未命中限制
	 */
	public boolean limitCircleTimeCanTakeOrder(ItemKeyDto itemKey) {
		if (Objects.nonNull(itemKey.getAppItem()) && Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto())
				&& StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getLimitConfig())) {
			String limitConfig = itemKey.getAppItem().getMarketingItemCreditsDto().getLimitConfig();
			if (StringUtils.isBlank(limitConfig)){
				return true;
			}
			Integer limitExchangeType = Optional.ofNullable(itemKey.getAppItem().getMarketingItemCreditsDto())
					.map(MarketingItemCreditsDto::getExtraInfo)
					.map(JSON::parseObject)
					.map(x -> x.getInteger(ExtraInfoUtils.LIMIT_EXCHANGE_TYPE))
					.orElse(null);
			if (!MarketingItemCreditsLimitExchangeType.CIRCLE_TIME.getCode().equals(limitExchangeType)){
				return true;
			}
			MarketingItemCreditsLimitConfig marketingItemCreditsLimitConfig = JSON.parseObject(limitConfig, MarketingItemCreditsLimitConfig.class);
			LocalDateTime now = LocalDateTime.now();
			String limitStartDate = marketingItemCreditsLimitConfig.getLimitStartDate();
			String limitEndDate = marketingItemCreditsLimitConfig.getLimitEndDate();
			LocalDate startDate = LocalDate.parse(limitStartDate, DateTimeFormatter.ofPattern(TIME_DAY_PATTERN));
			LocalDate endDate = LocalDate.parse(limitEndDate, DateTimeFormatter.ofPattern(TIME_DAY_PATTERN));
			// 不在限制日期内
			if (now.toLocalDate().isBefore(startDate) || now.toLocalDate().isAfter(endDate)) {
				return false;
			}
			// 限制时间内
			String limitStartTime = marketingItemCreditsLimitConfig.getLimitStartTime();
			String limitEndTime = marketingItemCreditsLimitConfig.getLimitEndTime();
			LocalTime startTime = LocalTime.parse(limitStartTime, DateTimeFormatter.ofPattern(TIME_NOT_DATE_SECOND_PATTERN));
			LocalTime endTime = LocalTime.parse(limitEndTime, DateTimeFormatter.ofPattern(TIME_NOT_DATE_SECOND_PATTERN));
            return now.toLocalTime().isAfter(startTime) && now.toLocalTime().isBefore(endTime);
        }
		return true;
	}

	public String getLimitCircleTimeTip(ItemKeyDto itemKey) {
		String limitConfig = itemKey.getAppItem().getMarketingItemCreditsDto().getLimitConfig();
		MarketingItemCreditsLimitConfig marketingItemCreditsLimitConfig = JSON.parseObject(limitConfig, MarketingItemCreditsLimitConfig.class);
		LocalDateTime now = LocalDateTime.now();
		String limitEndDate = marketingItemCreditsLimitConfig.getLimitEndDate();
		LocalDate endDate = LocalDate.parse(limitEndDate, DateTimeFormatter.ofPattern(TIME_DAY_PATTERN));
		String limitEndTime = marketingItemCreditsLimitConfig.getLimitEndTime();
		LocalTime endTime = LocalTime.parse(limitEndTime, DateTimeFormatter.ofPattern(TIME_NOT_DATE_SECOND_PATTERN));
		String limitStartTime = marketingItemCreditsLimitConfig.getLimitStartTime();
		String limitStartDate = marketingItemCreditsLimitConfig.getLimitStartDate();
		LocalTime startTime = LocalTime.parse(limitStartTime, DateTimeFormatter.ofPattern(TIME_NOT_DATE_SECOND_PATTERN));
		LocalDate startDate = LocalDate.parse(limitStartDate, DateTimeFormatter.ofPattern(TIME_DAY_PATTERN));
		// 判断是否所有场次都已经结束
		LocalDateTime finalEndTime = LocalDateTime.of(endDate, endTime);
		if (now.isAfter(finalEndTime)) {
			return "不在兑换时间内";
		}
		// 获取下一场开始时间（当天或未来某天）
		LocalDateTime nextStartTime;
		// 如果今天还没到开始日期，则第一场是开始日期当天的时间
		if (now.toLocalDate().isBefore(startDate)) {
			nextStartTime = LocalDateTime.of(startDate, startTime);
		}
		// 今天还没到开始时间
		else if (now.toLocalTime().isBefore(startTime)){
			nextStartTime = LocalDateTime.of(now.toLocalDate(), startTime);
		}else {
			// 否则就是明天的开始时间
			nextStartTime = LocalDateTime.of(now.toLocalDate().plusDays(1), startTime);
		}
		return nextStartTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + " - " + limitEndTime + " 可兑换";
	}


	/**
	 * 返回限制日期
	 *
	 * @param itemKey
	 */
	public String[] getLimitDay(ItemKeyDto itemKey) {
		String limitDate = null;
		limitDate = getLimitDate(itemKey,limitDate);
		if (StringUtils.isNotBlank(limitDate) && !StringUtils.equals(limitDate, "no")) {
			String[] dayBetween = StringUtils.split(limitDate, ',');
			if(dayBetween.length==2){
				return dayBetween;
			}
		}
		return null;
	}

	/**
	 * 日期限制是否开始
	 * @param itemKey 
	 * @return boolean
	 */
	public boolean limitDayIsStart(ItemKeyDto itemKey) {
		String limitDate = null;
		limitDate = getLimitDate(itemKey,limitDate);
		if (StringUtils.isNotBlank(limitDate) && !StringUtils.equals(limitDate, "no")) {
		    String[] dayBetween = StringUtils.split(limitDate, ',');
			String startDay = dayBetween[0];
			Date start = DateUtil.getDayDate(startDay);
			if (System.currentTimeMillis() < start.getTime()) {
				return true;
			}
		}
		return false;
	}

	private String getLimitDate(ItemKeyDto itemKey,String limitDate){
		if (itemKey.isItemMode()) {
			limitDate = itemKey.getItem().getLimitDate();
		}else if (itemKey.isDuibaAppItemMode()) {
			if (itemKey.getItem().getLimitDate() != null && !StringUtils.equals(itemKey.getItem().getLimitDate(), "no")) {
				limitDate = itemKey.getItem().getLimitDate();
			} else if (itemKey.getAppItem().getLimitDate() != null) {
				limitDate = itemKey.getAppItem().getLimitDate();
			}
		} else if (itemKey.isSelfAppItemMode()) {
			limitDate = itemKey.getAppItem().getLimitDate();
		}
		return limitDate;
	}

	/**
	 * 日期限制是否结束
	 * @param itemKey 
	 * @return boolean
	 */
	public boolean limitDayIsOver(ItemKeyDto itemKey) {
		String limitDate = null;
		limitDate = getLimitDate(itemKey,limitDate);
		if (StringUtils.isNotBlank(limitDate) && !StringUtils.equals(limitDate, "no")) {
		    String[] dayBetween = StringUtils.split(limitDate, ',');
		    if(dayBetween.length==2){
		        String endDay = dayBetween[1];
	            Calendar cal = Calendar.getInstance();
	            cal.setTime(DateUtil.getDayDate(endDay));
	            cal.add(Calendar.DATE, 1);
	            Date end = cal.getTime();
	            if (System.currentTimeMillis() > end.getTime()) {
	                return true;
	            }
		    }
		}
		return false;
	}

	/**
	 * 每日时间限制，是否能兑换
	 * 
	 * @param itemKey
	 * @return Boolean
	 */
	public Boolean canTakeOrder(ItemKeyDto itemKey) {
		boolean canTake = true;
		Date dateNow = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
		return canTake(itemKey,dateNow,sdf,canTake);
	}

	private boolean canTake(ItemKeyDto itemKey,Date dateNow,SimpleDateFormat sdf,boolean canTake){
		if (itemKey.isDuibaAppItemMode()) { // 添加到开发者库中的兑吧商品
			// 先判断兑吧是否配置了限时。
			if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getItem().getLimitTimeBetween().split("-")[0];
				String endTime = itemKey.getItem().getLimitTimeBetween().split("-")[1];
				return !isOutTime(sdf,dateNow,startTime,endTime);
				// 断开发者是否配置了限时。
			} else if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[0];
				String endTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[1];
				return  !isOutTime(sdf,dateNow,startTime,endTime);
			}
			// 商品兑换时间限制，精确到分
			if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
				String startTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[0];
				String endTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[1];
				return !isOutTime(sdf,dateNow,startTime,endTime);
			}
		} else if (itemKey.isItemMode()) { // 自动推荐商品
			// 直接判断兑吧是否限时
			if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getItem().getLimitTimeBetween().split("-")[0];
				String endTime = itemKey.getItem().getLimitTimeBetween().split("-")[1];
				return  !isOutTime(sdf,dateNow,startTime,endTime);
			}
		} else if (itemKey.isSelfAppItemMode()) { // 开发者自有商品
			// 直接判断开发者是否限时
			if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[0];
				String endTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[1];
				return  !isOutTime(sdf,dateNow,startTime,endTime);
			}
			// 商品兑换时间限制，精确到分
			if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
				String startTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[0];
				String endTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[1];
				return !isOutTime(sdf,dateNow,startTime,endTime);
			}
		}
		return canTake;
	}

	/**
	 * 是否不在兑换时间内
	 * @return
	 */
	private boolean isOutTime(SimpleDateFormat sdf,Date dateNow,String startTime,String endTime){
		try {
			Date now = sdf.parse(sdf.format(dateNow));
			Date startDate = sdf.parse(startTime);
			Date endDate = sdf.parse(endTime);
			if (now.before(startDate) || now.after(endDate)) {
				return true;
			}
		} catch (ParseException e) {
			throw new GoodsWebException("日期格式化出错", e);
		}
		return false;
	}
	
	/**
	 * 判断时间是否兑换项时间范围内
	 * @param itemKey
	 * @param dateNow
	 * @return Boolean
	 */
	public Boolean canDateTakeOrder(ItemKeyDto itemKey, Date dateNow) {
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
		boolean canTake = true;
		return canTake(itemKey,dateNow,sdf,canTake);
	}


	/**
	 * 是否即将开始的判断
	 * @param itemKey
	 * @return Boolean
	 */
	public Boolean isGoingToStart(ItemKeyDto itemKey) {
		Date dateNow = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
		boolean gTs = false;
		if (itemKey.isDuibaAppItemMode()) { // 添加到开发者库中的兑吧商品
			// 先判断兑吧是否配置了限时。
			if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getItem().getLimitTimeBetween().split("-")[0];
				gTs = isGoingToStart(sdf,dateNow,startTime);
				// 判断开发者是否配置了限时
			} else if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[0];
				gTs = isGoingToStart(sdf,dateNow,startTime);
			}
			// 商品兑换时间限制，精确到分
			if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
				String startTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[0];
				return isGoingToStart(sdf,dateNow,startTime);
			}
		} else if (itemKey.isItemMode()) { // 自动推荐商品
			// 直接判断兑吧是否限时
			if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getItem().getLimitTimeBetween().split("-")[0];
				gTs = isGoingToStart(sdf,dateNow,startTime);
			}
		} else if (itemKey.isSelfAppItemMode()) { // 开发者自有商品
			// 直接判断开发者是否限时
			if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
				String startTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[0];
				gTs = isGoingToStart(sdf,dateNow,startTime);
			}
			// 商品兑换时间限制，精确到分
			if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
				String startTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[0];
				return isGoingToStart(sdf,dateNow,startTime);
			}
		}
		return gTs;
	}

	/**
	 * 是否即将开始的判断
	 * @return
	 */
	private boolean isGoingToStart(SimpleDateFormat sdf,Date dateNow,String startTime){
		try {
			Date now = sdf.parse(sdf.format(dateNow));
			Date startDate = sdf.parse(startTime);
			return now.before(startDate);
		} catch (ParseException e) {
			LOG.error("日期格式化错误", e);
			return true;
		}
	}

	/**
	 * 是否今日已结束的判断(限时)
	 * 
	 * <AUTHOR> 2014年12月18日
	 * @param itemKey
	 * @return boolean
	 */
	public Boolean isTodayOver(ItemKeyDto itemKey){
		Date dateNow = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
		if (itemKey.isDuibaAppItemMode()) { // 添加到开发者库中的兑吧商品
			return duibaAppItemModeIsTodayOver(itemKey,sdf,dateNow);
		} else if (itemKey.isItemMode()) { // 自动推荐商品
			// 直接判断兑吧是否限时
			if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
				String endTime = itemKey.getItem().getLimitTimeBetween().split("-")[1];
				return isTodayOver(sdf,dateNow,endTime);
			}
		} else if (itemKey.isSelfAppItemMode()) { // 开发者自有商品
			// 直接判断开发者是否限时
			if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
				String endTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[1];
				return isTodayOver(sdf,dateNow,endTime);
			}
			// 商品兑换时间限制，精确到分
			if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
				String endTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[1];
				return isTodayOver(sdf,dateNow,endTime);
			}
		}
		return false;
	}

	private Boolean duibaAppItemModeIsTodayOver(ItemKeyDto itemKey,SimpleDateFormat sdf,Date dateNow){
		// 先判断兑吧是否配置了限时。
		if (itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit) && itemKey.getItem().getLimitTimeBetween() != null) {
			String[] sp = itemKey.getItem().getLimitTimeBetween().split("-");
			if (sp.length == 2) {
				String endTime = sp[1];
				return isTodayOver(sdf,dateNow,endTime);
			}
			// 判断开发者配置了限时。
		} else if (itemKey.getAppItem().isOpTypeAppItem(ItemDto.OpTypeTimeLimit) && itemKey.getAppItem().getLimitTimeBetween() != null) {
			String endTime = itemKey.getAppItem().getLimitTimeBetween().split("-")[1];
			return isTodayOver(sdf,dateNow,endTime);
		}
		// 商品兑换时间限制，精确到分
		if (Objects.nonNull(itemKey.getAppItem().getMarketingItemCreditsDto()) && StringUtils.isNotBlank(itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime())) {
			String endTime = itemKey.getAppItem().getMarketingItemCreditsDto().getEveryDayLimitTime().split("-")[1];
			return isTodayOver(sdf,dateNow,endTime);
		}
		return false;
	}

	/**
	 * 是否今日已结束的判断(限时)
	 * @return
	 */
	private boolean isTodayOver(SimpleDateFormat sdf,Date dateNow,String endTime){
		try {
			Date now = sdf.parse(sdf.format(dateNow));
			Date endDate = sdf.parse(endTime);
			if (now.after(endDate) || now.equals(endDate)) {
				return true;
			}
		} catch (ParseException e) {
			LOG.error("日期格式化错误", e);
			return true;
		}
		return false;
	}

	/**
	 * 判断是否已抢光开关(优惠券，实物，定制话费)
	 * @param itemKey 
	 * @return boolean
	 */
	public boolean timeLimitSwitch(ItemKeyDto itemKey) {
		if (ItemDto.TypeCoupon.equals(itemKey.getItemDtoType()) || ItemDto.TypeObject.equals(itemKey.getItemDtoType()) || ItemDto.TypePhonebillDingzhi.equals(itemKey.getItemDtoType())) {
			if (itemKey.isDuibaAppItemMode()) {
				return itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimitSwitch) && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit);
			}
			if (itemKey.isItemMode()) {
				return itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimitSwitch) && itemKey.getItem().isOpTypeItem(ItemDto.OpTypeTimeLimit);
			}
		}
		return false;
	}
	
	/**
	 * @param item
	 * @return boolean
	 */
	public boolean isLimitItem(ItemDto item){
		if (item.isOpTypeItem(ItemDto.OpTypeTimeLimit)) {
			return true;
		}
		if (item.isOpTypeItem(ItemDto.OpTypeQuantityLimit)) {
			return true;
		}
		if (item.getLimitCount() != null && item.getLimitCount() > 0) {
			return true;
		}
		if (item.getLimitDate() != null && !item.getLimitDate().equals("no")) {
			return true;
		}
		if (StringUtils.isNotBlank(item.getLimitTimeBetween())){
			return true;
		}
		return false;
	}
}
