package cn.com.duiba.pcg.service.biz.vo.comment;

import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.comment.ConsumerCommentResultDto;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>hangyongjie on 2021/10/8 4:02 下午
 */
public class ConsumerCommentListVO extends ConsumerCommentResultDto implements Serializable {

    /**
     * 用户额外信息
     */
    private ConsumerExtraDto consumerExtraDto;

    /**
     * 评价时间字符串
     */
    @JsonFormat(pattern = "MM-dd HH:mm",timezone = "GMT+8")
    private Date commentTime;


    public Date getCommentTime() {
        return commentTime;
    }

    public void setCommentTime(Date commentTime) {
        this.commentTime = commentTime;
    }

    public ConsumerExtraDto getConsumerExtraDto() {
        return consumerExtraDto;
    }

    public void setConsumerExtraDto(ConsumerExtraDto consumerExtraDto) {
        this.consumerExtraDto = consumerExtraDto;
    }
}