package cn.com.duiba.pcg.service.biz.config;

import cn.com.duiba.order.center.api.dto.OrdersDto;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/5/8 20:16
 */
@Configuration
@ConfigurationProperties(prefix = "equity.refund")
public class EquityRefundConfig {

    private Set<Long> appIds = Sets.newHashSet(61687L,19485L);

    /**
     * 是否是权益可退款订单
     * @param ordersDto
     * @return
     */
    public Boolean isEquityRefundOrder(OrdersDto ordersDto) {
        return ordersDto != null && getAppIds().contains(ordersDto.getAppId())
                && ordersDto.getType().equals(OrdersDto.type_coupon)
                && ordersDto.getItemId() == null
                && ordersDto.getFlowworkStage().equals("AfterSend-started")
                && StringUtils.isNotBlank(ordersDto.getExtraInfo())
                && "true".equals(JSON.parseObject(ordersDto.getExtraInfo()).getString("equityCanReturnTag"))
                && ordersDto.getConsumerPayPrice() != 0;
    }

    public Set<Long> getAppIds() {
        return appIds;
    }

    public void setAppIds(Set<Long> appIds) {
        this.appIds = appIds;
    }
}
