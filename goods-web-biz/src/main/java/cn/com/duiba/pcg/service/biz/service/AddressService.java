package cn.com.duiba.pcg.service.biz.service;

import cn.com.duiba.activity.custom.center.api.dto.identity.IdentityCustomerRelationDto;
import cn.com.duiba.consumer.center.api.dto.ReceiveAddressDto;
import cn.com.duiba.pcg.service.biz.vo.AdministrativeDivisionVo;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: lufeng
 * @Description:通用用户地址信息接口
 * @Date: Created in 2018/11/20
 */
public interface AddressService {
    //获取省列表
    List<AdministrativeDivisionVo> getProvince();

    //通过父code获取子列表
    List<AdministrativeDivisionVo> getChildrenByParentCode(String parentCode);

    //通过code获取地址
    List<AdministrativeDivisionVo> getDivisionByCode(String code);

    //查询用户默认地址信息
    ReceiveAddressDto getDefaultAddress(Long consumerId);

    /**
     * 根据id 查询地址
     * @param id
     * @return
     */
    ReceiveAddressDto getAddressById(Long id);

    /**
     * 删除地址
     * @param id
     * @return
     */
    void delAddressById(Long id);

    //保存用户地址信息,返回地址主键id
    long saveAddr(String addrId, Long cId, String addrName, String addrCode, String addrDetail, String addrPhone, Integer isDefaultAddr,
                  String addrProvince,
                  String addrCity,
                  String addrArea,
                  String addrStreet);

    /**
     * 判断是否还是用6位code，即三级地址
     * @param addrCode
     * @return
     */
    boolean only6WeiAddrCode(String addrCode);

    /**
     * 判断是否存在下一级
     * @param addrCode
     * @return
     */
    boolean haveChildrenAddress(String addrCode);

    /**
     * 地址列表管理   用户更新或者插入 地址
     * @param addrId
     * @param cId
     * @param addrName
     * @param addrCode
     * @param addrDetail
     * @param addrPhone
     * @param isDefaultAddr
     * @return
     */
    long saveAddrCommon(String addrId, Long cId, String addrName, String addrCode, String addrDetail, String addrPhone, String isDefaultAddr,
                        String addrProvince,
                        String addrCity,
                        String addrArea,
                        String addrStreet);

    /**
     * 用户地址列表
     * @param request
     * @return
     */
    ModelAndView userAddressList(HttpServletRequest request);

    /**
     * 地址列表添加页vm
     * @param request
     * @return
     */
    ModelAndView addressAdd(HttpServletRequest request);

    /**
     * 查询用户地址列表
     * @param consumerId
     * @return
     */
    List<ReceiveAddressDto> getUserAddressList(Long consumerId);

    /**
     * 验证地址条数上限
     * @param consumerId
     * @return
     */
    Boolean verifyAddressNum(Long consumerId,String addrId);

    /**
     * 把addressId放入localThreadCache，后续运费/下单地址用
     * @param addressId
     * @param request
     */
    void requestCacheAddressId(HttpServletRequest request, Long addressId);

    /**
     * 获取该用户的隐私勾选状态
     * @return
     */
    IdentityCustomerRelationDto getPrivacy();
}
