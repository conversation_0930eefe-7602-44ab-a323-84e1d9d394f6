package cn.com.duiba.pcg.service.biz.vo.cmb;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xutao on 2020/4/30.
 */
public class CmbItemConfigVO {
    //appid
    private Long appId;
    //商品
    private List<Long> itemIds;
    //时间
    private List<String> minSecs;

    public Long getAppId() {
        return appId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public List<Long> getItemIds() {
        return itemIds;
    }

    public void setItemIds(List<Long> itemIds) {
        this.itemIds = itemIds;
    }

    public List<String> getMinSecs() {
        return minSecs;
    }

    public void setMinSecs(List<String> minSecs) {
        this.minSecs = minSecs;
    }

    public static void main(String[] args) throws Exception{
        CmbItemConfigVO cmbItemConfigVO = new CmbItemConfigVO();
        cmbItemConfigVO.setAppId(62445L);
        List<Long> itemIds = new ArrayList<>();
        itemIds.add(128270292720489L);
        itemIds.add(128270491962024L);
        cmbItemConfigVO.setItemIds(itemIds);

        List<String> minSecs = new ArrayList<>();
        minSecs.add("10:00:00");
        minSecs.add("23:59:59");
        cmbItemConfigVO.setMinSecs(minSecs);

        List<CmbItemConfigVO> list = new ArrayList<>();
        list.add(cmbItemConfigVO);
        System.out.println(JSONObject.toJSONString(list));
    }
}
