package cn.com.duiba.pcg.service.biz.vo;

import java.util.Map;

import com.google.common.collect.Maps;

import cn.com.duiba.developer.center.api.domain.dto.AerosolLinkDto;
import cn.com.duiba.developer.center.api.domain.dto.statistics.AerosolConsumerDto;
import cn.com.duiba.developer.center.api.domain.enums.PageEnum;

/** 
 * ClassName:AerosolVO.java <br/>
 * <AUTHOR> 
 * @date 创建时间：2016年12月15日 下午3:01:37 
 * @version 1.0 
 * @parameter  
 * @since   JDK 1.6
 */
public class AerosolVO {
    private Map<PageEnum,AerosolLinkDto> pageMap = Maps.newHashMap();

    private AerosolConsumerDto aerosolConsumerDto;

    /**
     * 设置浮标的信息
     * @param aerosolInfo
     */
    public void setAerosolInfo(AerosolConsumerDto aerosolInfo){
        aerosolConsumerDto = aerosolInfo;
        for(AerosolLinkDto link:aerosolInfo.getLinkList()){
            pageMap.put(link.getSourcePage(),link);
        }
    }

    /**
     * 获取目标页的地址
     * @param page
     * @return AerosolLinkDto
     */
    public AerosolLinkDto getDestinationPage(PageEnum page){
        if(pageMap.containsKey(page)){
            return pageMap.get(page);
        }else if(pageMap.containsKey(PageEnum.ALL)){
            return pageMap.get(PageEnum.ALL);
        }else{
            return null;
        }
    }

    /**
     * @param page
     * @return Boolean
     */
    public Boolean show(PageEnum page){
        if(aerosolConsumerDto == null){
            return false;
        }
        if(!aerosolConsumerDto.getShow()){
            return false;
        }
        return pageMap.containsKey(page) || pageMap.containsKey(PageEnum.ALL);
    }

    /**
     * @return aerosolConsumerDto
     */
    public AerosolConsumerDto getAerosolConsumerDto(){
        return aerosolConsumerDto;
    }
}
