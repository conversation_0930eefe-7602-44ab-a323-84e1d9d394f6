package cn.com.duiba.pcg.service.biz.service.front.impl.strategy.impl;

import cn.com.duiba.pcg.service.biz.config.LzljConfig;
import cn.com.duiba.pcg.service.biz.request.supply.SupplyPurchaseRequest;
import cn.com.duiba.pcg.service.biz.response.supply.QuerySupplyOrderResponse;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategy;
import cn.com.duiba.pcg.service.biz.service.front.impl.strategy.ExtraStrategyRegistry;
import cn.com.duiba.supplier.center.api.dto.DuiBaSupplyOrdersDto;
import cn.com.duiba.supplier.center.api.request.order.DuiBaSupplyOrderPurchaseRequest;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Optional;

@Service
public class LzljStrategyImpl extends ExtraStrategy {

    @Resource
    private LzljConfig lzljConfig;

    @PostConstruct
    private void init() {
        ExtraStrategyRegistry.register(Lists.newArrayList(lzljConfig.getAppIds()), this);
    }
    @Override
    public Object extraFild(QuerySupplyOrderResponse response, DuiBaSupplyOrdersDto order) {
        // 是否已核销
        boolean used = Optional.ofNullable(order)
                .map(x -> order.getExtraInfo())
                .map(JSON::parseObject)
                .map(x -> x.getBooleanValue(LzljConfig.USED))
                .orElse(false);
        if (used){
            response.setOrderStatus(QuerySupplyOrderResponse.STATUS_USED);
        }
        return response;
    }

    @Override
    public void customPurchaseHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request) {

    }

    @Override
    public void customPurchaseAfterHandle(DuiBaSupplyOrderPurchaseRequest r, SupplyPurchaseRequest request, Long supplyOrderNum) {

    }
}
