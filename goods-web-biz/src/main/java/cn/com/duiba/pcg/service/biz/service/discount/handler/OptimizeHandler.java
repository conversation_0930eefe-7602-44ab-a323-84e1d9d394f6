package cn.com.duiba.pcg.service.biz.service.discount.handler;

import cn.com.duiba.mall.center.api.domain.dto.discount.DiscountCombination;
import cn.com.duiba.mall.center.api.domain.dto.discount.DiscountTicketDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OptimizeHandler {

    /**
     * 查找最优优惠券
     * @param salePrice 商品售价
     * @param tickets 优惠券
     * @return 优惠券
     */
    List<DiscountCombination> optimize(Long salePrice, List<DiscountTicketDto> tickets);
}
