package cn.com.duiba.pcg.service.biz.config;

import com.google.common.collect.Sets;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;


/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "url.serial.access")
public class UrlSerialAccessConstants {
    public static final Set<String> RECORD_URL_SET = Sets.newHashSet("/mobile/appItemDetail", "/mobile/detail");
    /**
     * 全局开关
     */
    private Boolean record = true;
    /**
     * 全局开关
     */
    private Boolean intercept = true;
    /**
     * 排除读写的app
     */
    private Set<Long> excludeAppIds = Sets.newHashSet(0L);

    /**
     * 记录的保存天数,自然日
     * 1-当日过期
     * 2-明天过期
     */
    private Integer recordKeepDays = 2;

    public Boolean getRecord() {
        return record;
    }

    public void setRecord(Boolean record) {
        this.record = record;
    }

    public Boolean getIntercept() {
        return intercept;
    }

    public void setIntercept(Boolean intercept) {
        this.intercept = intercept;
    }

    public Set<Long> getExcludeAppIds() {
        return excludeAppIds;
    }

    public void setExcludeAppIds(Set<Long> excludeAppIds) {
        this.excludeAppIds = excludeAppIds;
    }

    public Integer getRecordKeepDays() {
        return recordKeepDays;
    }

    public void setRecordKeepDays(Integer recordKeepDays) {
        this.recordKeepDays = recordKeepDays;
    }
}
