package cn.com.duiba.pcg.service.biz.ordervalidator.enums;

/**
 * Created by dugq on 2018/11/20 0020.
 */
public enum  TypeEnum {
    OBJECT("object"),
    COUPON("coupon"),
    VIRTUAL("virtual"),
    DIRECT("direct"),
    QB("qb"),
    PHONE_BILL("phonebill"),
    PHONE_BILL_DINGZHI("phonebillDingzhi"),
    PHONE_FLOW("phone_flow"),
    AL_IPAY("alipay"),
    ;
    private String type;

    TypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
}
