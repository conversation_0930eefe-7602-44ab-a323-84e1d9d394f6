package cn.com.duiba.pcg.service.biz.service.equity.impl;

import cn.com.duiba.activity.center.api.dto.equity.EquityBatchConfigDto;
import cn.com.duiba.activity.center.api.dto.equity.EquityConfigDto;
import cn.com.duiba.activity.center.api.enums.equity.EquityTypeEnum;
import cn.com.duiba.activity.center.api.enums.equity.VerificationTypeEnum;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityBatchConfigService;
import cn.com.duiba.activity.center.api.remoteservice.equity.RemoteEquityConfigService;
import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.biz.tool.duiba.reqresult.Result;
import cn.com.duiba.biz.tool.duiba.reqresult.ResultBuilder;
import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ConsumerExtraDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteConsumerExtraService;
import cn.com.duiba.goods.center.api.remoteservice.dto.equity.EquityGoodsRecordDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.equity.RemoteEquityGoodsRecordService;
import cn.com.duiba.goods.center.api.request.equity.GoodsRecordFindPageConditions;
import cn.com.duiba.order.center.api.remoteservice.RemoteConsumerOrderSimpleService;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.service.biz.ordervalidator.params.ItemParam;
import cn.com.duiba.pcg.service.biz.request.equity.EquityPrizeExchangeReq;
import cn.com.duiba.pcg.service.biz.service.equity.EquityChinaSmokeService;
import cn.com.duiba.pcg.service.biz.service.equity.EquityPrizeService;
import cn.com.duiba.pcg.service.biz.service.order.OrderCreateService;
import cn.com.duiba.pcg.service.biz.vo.equity.EquityOrderCreatedVO;
import cn.com.duiba.pcg.service.biz.vo.equity.chinasmoke.CouponListVo;
import cn.com.duiba.pcg.service.biz.vo.equity.chinasmoke.EquityConponInfoVo;
import cn.com.duiba.pcg.service.biz.vo.equity.chinasmoke.UserCouponInfoVo;
import cn.com.duiba.wolf.utils.DateUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liukai on 2020/10/04.
 */
@Service
public class EquityChinaSmokeServiceImpl implements EquityChinaSmokeService {

    private static Logger logger = LoggerFactory.getLogger(EquityChinaSmokeServiceImpl.class);

    @Autowired
    private RemoteEquityGoodsRecordService remoteEquityGoodsRecordService;

    @Autowired
    private RemoteConsumerExtraService remoteConsumerExtraService;

    @Autowired
    private RemoteEquityConfigService remoteEquityConfigService;

    @Autowired
    private EquityPrizeService equityPrizeService;

    @Autowired
    protected OrderCreateService orderCreateService;

    @Autowired
    private RemoteConsumerOrderSimpleService remoteOrdersSimpleService;

    @Autowired
    private RemoteEquityBatchConfigService remoteEquityBatchConfigService;


    @Override
    public ModelAndView couponList(HttpServletRequest request) {
        try {
            ModelAndView model = new ModelAndView("consumer/equity/chinasmoke/index");
            //组装前端展示数据
            setBasicInfo(model);

            List<CouponListVo> couponListVos = Lists.newArrayList();

            List<EquityConfigDto> equityConfigDtos = remoteEquityConfigService.selectByEquityTypes(RequestLocal.getAppId(), VerificationTypeEnum.ONLINE.getCode(), Lists.newArrayList(EquityTypeEnum.CONFIG_EXCHANGE.getCode()));

            if (CollectionUtils.isEmpty(equityConfigDtos)) {
                return model;
            }
            List<Long> equityIds = equityConfigDtos.stream().map(EquityConfigDto::getId).collect(Collectors.toList());
            //根据 appid 权益类型

            Map<Long, EquityConfigDto> equityConfigDtoMap = equityConfigDtos.stream().collect(Collectors.toMap(EquityConfigDto::getId, Function.identity(), (a, b) -> a));
            GoodsRecordFindPageConditions goodsRecordFindPageConditions = new GoodsRecordFindPageConditions();
            goodsRecordFindPageConditions.setCid(RequestLocal.getCid());
            goodsRecordFindPageConditions.setEquityIdList(equityIds);
            List<EquityGoodsRecordDto> equityGoodsRecordDtos = remoteEquityGoodsRecordService.listByConditionParam(goodsRecordFindPageConditions);
            if (CollectionUtils.isNotEmpty(equityGoodsRecordDtos)) {
                equityGoodsRecordDtos.sort(Comparator.comparing(EquityGoodsRecordDto::getGmtCreate).reversed());
                for (EquityGoodsRecordDto equityGoodsRecordDto : equityGoodsRecordDtos) {
                    EquityConfigDto equityConfigDto = equityConfigDtoMap.get(equityGoodsRecordDto.getEquityId());
                    CouponListVo couponListVo = new CouponListVo();
                    couponListVo.setCode(equityGoodsRecordDto.getCode());
                    couponListVo.setEndDate(DateUtils.getSecondStr(equityConfigDto.getEndTime()));
                    couponListVo.setStartDate(DateUtils.getSecondStr(equityConfigDto.getStartTime()));
                    couponListVo.setEquityId(equityGoodsRecordDto.getEquityId());
                    couponListVo.setHasUsed(equityGoodsRecordDto.getVerificationTime() != null ? 1 : 0);
                    couponListVos.add(couponListVo);
                }
            }
            model.addObject("list", JSON.toJSONString(couponListVos));
            return model;
        } catch (Exception e) {
            logger.error("couponList duiba error ", e);
            return new ModelAndView("/error");
        }
    }

    @Override
    public UserCouponInfoVo userInfo(HttpServletRequest request) {
        ConsumerExtraDto consumerExtraDto = remoteConsumerExtraService.findByConsumerId(RequestLocal.getCid()).getResult();
        UserCouponInfoVo userCouponInfoVo = new UserCouponInfoVo();
        if (consumerExtraDto != null) {
            userCouponInfoVo.setNickName(consumerExtraDto.getNickname());
            userCouponInfoVo.setHeadPicUrl(consumerExtraDto.getAvatar());
        }
        userCouponInfoVo.setCanUseCouponSize(0);

        List<EquityConfigDto> equityConfigDtos = remoteEquityConfigService.selectByEquityTypes(RequestLocal.getAppId(), VerificationTypeEnum.ONLINE.getCode(), Lists.newArrayList(EquityTypeEnum.CONFIG_EXCHANGE.getCode()));

        if (CollectionUtils.isEmpty(equityConfigDtos)) {
            return userCouponInfoVo;
        }

        List<Long> equityIds = equityConfigDtos.stream().map(EquityConfigDto::getId).collect(Collectors.toList());

        GoodsRecordFindPageConditions goodsRecordFindPageConditions = new GoodsRecordFindPageConditions();
        goodsRecordFindPageConditions.setCid(RequestLocal.getCid());
        goodsRecordFindPageConditions.setEquityIdList(equityIds);
        List<EquityGoodsRecordDto> equityGoodsRecordDtos = remoteEquityGoodsRecordService.listByConditionParam(goodsRecordFindPageConditions);
        if (CollectionUtils.isNotEmpty(equityGoodsRecordDtos)) {
            Long count = equityGoodsRecordDtos.stream().filter(a -> a.getVerificationTime() == null).count();
            userCouponInfoVo.setCanUseCouponSize(count != null ? count.intValue() : 0);
        }

        return userCouponInfoVo;
    }

    @Override
    public List<EquityConponInfoVo> couponInfo(Long equityId) {

        List<EquityConponInfoVo> equityConponInfoVos = Lists.newArrayList();
        List<EquityConfigDto> equityConfigDtos = Lists.newArrayList();

        if (equityId != null) {
            EquityConfigDto equityConfigDto = remoteEquityConfigService.selectById(equityId);
            equityConfigDtos.add(equityConfigDto);
        } else {
            equityConfigDtos = remoteEquityConfigService.selectByEquityTypes(RequestLocal.getAppId(), VerificationTypeEnum.ONLINE.getCode(), Lists.newArrayList(EquityTypeEnum.CONFIG_EXCHANGE.getCode()));

        }
        if (CollectionUtils.isEmpty(equityConfigDtos)) {
            return equityConponInfoVos;
        }
        List<EquityGoodsRecordDto> equityGoodsRecordDtos = getEquityRecords(equityConfigDtos);

        if (CollectionUtils.isEmpty(equityGoodsRecordDtos)) {
            return equityConponInfoVos;
        }
        List<EquityGoodsRecordDto> data = equityGoodsRecordDtos.stream().filter(a -> a.getVerificationTime() == null).collect(Collectors.toList());

        Map<Long, List<EquityGoodsRecordDto>> equityRecordMap = data.stream().collect(Collectors.groupingBy(EquityGoodsRecordDto::getEquityId));
        if (MapUtils.isEmpty(equityRecordMap)) {
            return equityConponInfoVos;
        }

        equityRecordMap.forEach((key, value) -> {
            List<String> codes = value.stream().map(EquityGoodsRecordDto::getCode).collect(Collectors.toList());
            EquityConponInfoVo equityConponInfoVo = new EquityConponInfoVo();
            equityConponInfoVo.setCouponCode(codes);
            equityConponInfoVo.setEquityId(key);
            equityConponInfoVos.add(equityConponInfoVo);
        });
        return equityConponInfoVos;
    }

    @Override
    public Result<EquityOrderCreatedVO> exchange(EquityPrizeExchangeReq param, ConsumerDto consumerDto, HttpServletRequest request, HttpServletResponse response) throws BizException {
        // 1、券码基础校验
        EquityGoodsRecordDto equityGoodsRecordDto = remoteEquityGoodsRecordService.selectByCode(consumerDto.getAppId(), param.getExcCode());
        if (equityGoodsRecordDto == null || !Objects.equal(equityGoodsRecordDto.getAppId(), consumerDto.getAppId()) || !Objects.equal(equityGoodsRecordDto.getEquityId(), param.getEquityId())) {
            throw new BizException(ErrorCode.E1001000.getDesc()).withCode(ErrorCode.E1001000.getCode());
        } else if (equityGoodsRecordDto.getVerificationTime() != null) {
            throw new BizException(ErrorCode.E1001001.getDesc()).withCode(ErrorCode.E1001001.getCode());
        }

        // 2、权益信息校验
        Date now = new Date();
        EquityConfigDto equityConfigDto = remoteEquityConfigService.selectById(equityGoodsRecordDto.getEquityId());
        if (equityConfigDto == null || equityConfigDto.getDeleted() == 1 || !Objects.equal(EquityTypeEnum.CONFIG_EXCHANGE.getCode(), equityConfigDto.getType())) {
            throw new BizException(ErrorCode.E1001000.getDesc()).withCode(ErrorCode.E1001000.getCode());
        }
        equityPrizeService.checkEquity(param.getAppItemId(), now, equityConfigDto);
        // 3、权益批次信息校验
        EquityBatchConfigDto equityBatchConfigDto = remoteEquityBatchConfigService.selectById(equityGoodsRecordDto.getBatchId());
        if (equityBatchConfigDto == null || equityBatchConfigDto.getDeleted() == 1) {
            throw new BizException(ErrorCode.E1001000.getDesc()).withCode(ErrorCode.E1001000.getCode());
        }
        if (now.before(equityBatchConfigDto.getStartTime())) {
            throw new BizException(ErrorCode.E1001000.getDesc()).withCode(ErrorCode.E1001000.getCode());
        } else if (now.after(equityBatchConfigDto.getEndTime())) {
            throw new BizException(ErrorCode.E1001003.getDesc()).withCode(ErrorCode.E1001003.getCode());
        }
        // 4、商品可用性校验
        ItemKeyDto itemKeyDto = equityPrizeService.checkAppItemAvailable(param.getAppItemId(), param.getAppSkuId());
        ItemParam itemParam = new ItemParam();
        itemParam.setAppItemId(param.getAppItemId());
        itemParam.setQuantity(1);
        itemParam.setSkuId(param.getAppSkuId());
        List<ItemParam> itemParams = Collections.singletonList(itemParam);

        cn.com.duiba.api.bo.reqresult.Result<EquityOrderCreatedVO> equityOrderCreatedVOResult = equityPrizeService.executeExchange(param, equityGoodsRecordDto, consumerDto, itemKeyDto, itemParams, request, response);

        return ResultBuilder.success(equityOrderCreatedVOResult.getData());


    }


    private List<EquityGoodsRecordDto> getEquityRecords(List<EquityConfigDto> equityConfigDtos) {
        List<Long> equityIds = equityConfigDtos.stream().map(EquityConfigDto::getId).collect(Collectors.toList());

        GoodsRecordFindPageConditions goodsRecordFindPageConditions = new GoodsRecordFindPageConditions();
        goodsRecordFindPageConditions.setCid(RequestLocal.getCid());
        goodsRecordFindPageConditions.setEquityIdList(equityIds);
        return remoteEquityGoodsRecordService.listByConditionParam(goodsRecordFindPageConditions);
    }

    private void setBasicInfo(ModelAndView model) {
        model.addObject("hasLogin", true);
        if (RequestLocal.getConsumerDO() != null && RequestLocal.getConsumerDO().isNotLoginUser()) {
            model.addObject("hasLogin", false);
        }
        model.addObject("appId", RequestLocal.getAppId());
    }


}
