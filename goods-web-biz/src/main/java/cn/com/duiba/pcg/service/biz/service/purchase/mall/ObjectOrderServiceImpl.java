package cn.com.duiba.pcg.service.biz.service.purchase.mall;

import cn.com.duiba.boot.exception.BizException;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import cn.com.duiba.consumer.center.api.dto.ReceiveAddressDto;
import cn.com.duiba.consumer.center.api.remoteservice.RemoteReceiveAddressService;
import cn.com.duiba.dcommons.enums.GoodsTypeEnum;
import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.developer.center.api.remoteservice.RemoteAppService;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.AppItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemExtraDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.item.ItemKeyDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.AppItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.dto.sku.ItemSkuDto;
import cn.com.duiba.goods.center.api.remoteservice.item.RemoteItemExtraService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteAppItemSkuService;
import cn.com.duiba.goods.center.api.remoteservice.sku.RemoteItemSkuService;
import cn.com.duiba.order.center.api.constant.OrderPromoTypeEnum;
import cn.com.duiba.order.center.api.dto.AmbSubOrdersDto;
import cn.com.duiba.order.center.api.dto.OrderItemDto;
import cn.com.duiba.order.center.api.dto.OrdersDto;
import cn.com.duiba.order.center.api.dto.RequestParams;
import cn.com.duiba.order.center.api.dto.ShoppingTrolleryOrderDto;
import cn.com.duiba.order.center.api.remoteservice.RemoteExchangeService;
import cn.com.duiba.pcg.enums.ErrorCode;
import cn.com.duiba.pcg.service.biz.dto.purchase.MallPurchaseParam;
import cn.com.duiba.pcg.service.biz.service.AddressService;
import cn.com.duiba.pcg.service.biz.service.AppExtrangeLimitService;
import cn.com.duiba.pcg.service.biz.service.AppItemService;
import cn.com.duiba.pcg.service.biz.service.GoodsItemStatusService;
import cn.com.duiba.supplier.center.api.dto.supplier.SupplierInfoDto;
import cn.com.duiba.supplier.center.api.dto.supplier.SupplierSimpleInfoDto;
import cn.com.duiba.supplier.center.api.enums.SupplyTypeEnumUtil;
import cn.com.duiba.supplier.center.api.remoteservice.supplier.RemoteSupplierItemService;
import cn.com.duiba.thirdparty.enums.virtual.VirtualItemChannelEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018/6/29 0029 上午 11:08
 */
@Service
public class ObjectOrderServiceImpl implements ObjectOrderService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private AddressService addressService;
    @Autowired
    private RemoteAppService remoteAppServiceNew;
    @Autowired
    private RemoteExchangeService remoteExchangeService;
    @Autowired
    private AppExtrangeLimitService appExtrangeLimitService;
    @Autowired
    private GoodsPriceCalculateService goodsPriceCalculateService;
    @Autowired
    private AppItemService appItemService;
    @Autowired
    private RemoteReceiveAddressService remoteReceiveAddressService;

    @Autowired
    private GoodsItemStatusService goodsItemStatusService;
    @Autowired
    protected RemoteItemSkuService remoteItemSkuService;

    @Resource
    private RemoteAppItemSkuService remoteAppItemSkuService;

    @Resource
    private RemoteItemExtraService remoteItemExtraService;

    @Autowired
    private RemoteSupplierItemService remoteSupplierItemService;


    @Override
    public ShoppingTrolleryOrderDto extrange(HttpServletRequest request,
                                             MallPurchaseParam param,
                                             List<ItemKeyDto> itemKeyDtos,
                                             List<AppItemBO> appItemBOList,
                                             AppSimpleDto appSimpleDto,
                                             ConsumerDto consumerDto,
                                             ReceiveAddressDto addressDto) throws BizException {

        ShoppingTrolleryOrderDto order = buildOrder(itemKeyDtos, appSimpleDto, consumerDto,addressDto);

        List<GoodsNumBO> skuList = Lists.newArrayList();
        appItemBOList.forEach(bo -> skuList.add(new GoodsNumBO(bo.getAppItemId(), bo.getQuantity(), bo.getSkuId())));
        // 商品校验
        itemValid(itemKeyDtos);
        // 库存校验
        stockValid(appItemBOList);
        //计算价格,传入的数据应该是具有sku级别的
        GoodsPriceBO goodsPriceVO = goodsPriceCalculateService.goodsPriceDetailListAndExPrice(skuList, consumerDto, itemKeyDtos);
        order = setOrderItem(order, itemKeyDtos, appItemBOList, goodsPriceVO, appSimpleDto.getId());
        // 三方订单号放到开发者订单号中
        order.setDeveloperBizId(param.getThirdOrderNum());
        OrdersDto dto = excuteExtrange(appSimpleDto, consumerDto, order, itemKeyDtos);
        order.setId(dto.getId());
        return order;
    }

    /**
     * 库存校验（sku维度）
     */
    protected void stockValid(List<AppItemBO> appItemBOList) throws BizException {
        List<Long> appSkuIds = appItemBOList.stream().map(AppItemBO::getAppSkuId).collect(Collectors.toList());
        Map<Long, Integer> appItemQuantity = appItemBOList.stream().collect(Collectors.toMap(AppItemBO::getAppSkuId, AppItemBO::getQuantity));
        List<AppItemSkuDto> skuDtos = remoteAppItemSkuService.findSkuByIdListWithoutStock(appSkuIds);
        Map<Long, Long> appItemSkuStock = goodsItemStatusService.getAppItemSkuStock(skuDtos);
        for (AppItemSkuDto skuDto : skuDtos) {
            Long stockId = skuDto.getStockId();
            Long stockNum = appItemSkuStock.get(stockId);
            if (stockNum == null || stockNum < appItemQuantity.get(skuDto.getId())){
                throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
            }

        }
    }

    /**
     * 商品校验（sku维度）
     */
    protected void itemValid(List<ItemKeyDto> itemKeyDtos) throws BizException {
        for (ItemKeyDto itemKey : itemKeyDtos) {
            //  已下架 或 已删除
            if (goodsItemStatusService.isOffShelves(itemKey) || goodsItemStatusService.isDeleted(itemKey)) {
                throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
            }
            // 自动下架
            if (goodsItemStatusService.isAutoOffShelves(itemKey)) {
                throw new BizException(ErrorCode.********.getDesc()).withCode(ErrorCode.********.getCode());
            }
        }
    }




    /**
     * 执行兑换
     * 1.这里会插库
     * 2.这里只有order.id是有效字段，其他字段不全
     */
    private OrdersDto excuteExtrange(AppSimpleDto app, ConsumerDto consumer, ShoppingTrolleryOrderDto order, List<ItemKeyDto> itemKeyDtos) {
        // app余额校验
        appExtrangeLimitService.balanceValidate(app, order.getActualPrice());

        AmbSubOrdersDto ambSubOrdersDto = buildAmbSubOrder(consumer, order, itemKeyDtos);

        RequestParams requestParams = new RequestParams();
        requestParams.setConsumerId(consumer.getId());
        return remoteExchangeService.createMallObjectOrder(order, ambSubOrdersDto,requestParams);
    }

    /**
     * 这里是取一个有效的加钱购商品 插入ambSubOrder
     */
    private AmbSubOrdersDto buildAmbSubOrder(ConsumerDto consumer, ShoppingTrolleryOrderDto order, List<ItemKeyDto> itemKeyDtos) {
        return null;
    }

    private ItemKeyDto getItemKeyDto(List<ItemKeyDto> itemKeyDtos) {
        for (ItemKeyDto each : itemKeyDtos) {
            if (each.isItemMode()) {
                // 说明是兑吧 自有商品 兑吧 自个做加钱购的活动，和开发者无关
                if (each.getItem().isOpTypeItem(ItemDto.OpTypeIsAmb)) {
                    return each;
                }
            } else if (each.isSelfAppItemMode() && each.getAppItem().isOpTypeAppItem(ItemDto.OpTypeIsAmb)) {
                return each;
            }
        }
        return null;
    }

    /**
     * 设置子订单，包括一些统计参数，如价格等
     *
     * @param orderDto
     * @param itemKeyDtos
     * @return
     */

    private ShoppingTrolleryOrderDto setOrderItem(ShoppingTrolleryOrderDto orderDto, List<ItemKeyDto> itemKeyDtos, List<AppItemBO> appItemBOList, GoodsPriceBO goodsPriceVO, Long appId) {
        //价格计算
        List<OrderItemDto> orderItemDtos = new ArrayList<>(appItemBOList.size());
        // 市面价值
        Integer facePrice = 0;
        Map<Long, ItemKeyDto> itemKeyDtoMap = Maps.newHashMap();
        itemKeyDtos.stream().forEach(itemKey -> {
            itemKeyDtoMap.put(itemKey.getAppItem().getId(), itemKey);
        });
        List<GoodsPriceDetailBO> list = goodsPriceVO.getGoodsPriceDetailBOList();
        Map<String, GoodsPriceDetailBO> goodsPriceDetailVoMap = Maps.newHashMap();
        list.stream().forEach(b -> {
            goodsPriceDetailVoMap.put(buildKey(b.getAppItemId(), b.getAppItemSkuId()), b);
        });
        //要循环appItemBOList才合理,不能循环商品列表
        Integer quantity = 0;
        for (AppItemBO bo : appItemBOList) {
            OrderItemDto dto = new OrderItemDto();
            //购物车的商品,现在默认开发者应该是全部上架,不允许出现自动推荐类似的商品
            ItemKeyDto each = itemKeyDtoMap.get(bo.getAppItemId());
            AppItemDto appItemDto = each.getAppItem();
            ItemDto itemDto = each.getItem();
            ItemBriefBO itemBrief = new ItemBriefBO();
            //添加标题信息
            if (appItemDto != null) {
                facePrice = facePrice + ComUtils.defaultObj(appItemDto.getFacePrice()) * bo.getQuantity();
                itemBrief.setName(getName(each));
                dto.setAppItemId(appItemDto.getId());
                dto.setItemId(appItemDto.getItemId());
            } else {
                logger.warn("appItemDto no exits app {}", JsonUtils.objectToString(each));
                if (itemDto != null) {
                    logger.warn("bug itemId={}  ", itemDto.getId());
                    itemBrief.setName(itemDto.getName());
                    facePrice = facePrice + ComUtils.defaultObj(itemDto.getFacePrice()) * bo.getQuantity();
                }
            }

            if (each.isSelfAppItemMode()) {
                dto.setAppSkuId(bo.getSkuId());
            } else if (each.isDuibaAppItemMode()) {
                dto.setSkuId(bo.getSkuId());
                dto.setAppSkuId(bo.getAppSkuId());
            }
            dto.setQuantity(bo.getQuantity());
            dto.setOrderPromoTypeEnum(OrderPromoTypeEnum.PROMO_TYPE_ACTIVITY);
            JSONObject extraInfo = new JSONObject();
            dto.setExtraInfo(extraInfo.toJSONString());
            //skuinfo信息,要重新设置
            itemBrief.setSkuinfo(bo.getSkuInfo());
            itemBrief.setThirdSubOrderId(bo.getThirdSubOrderId());
            dto.setItemBrief(JsonUtils.objectToString(itemBrief));
            Integer tempQuantity = bo.getQuantity() == null ? 0 : bo.getQuantity();
            quantity = quantity + tempQuantity;

            GoodsPriceDetailBO goodsPriceDetailBO = goodsPriceDetailVoMap.get(buildKey(bo.getAppItemId(), bo.getSkuId()));
            if (goodsPriceDetailBO != null) {
                setOrderItemPrice(dto, goodsPriceDetailBO);
            }
            // 记录订单下单时（快照）的兑吧商品的成本价，售价信息
            try {
                // 添加
                HashMap<String, Object> params = new HashMap<>();
                ItemSkuDto itemSkuDto = remoteItemSkuService.findSkuById(bo.getSkuId());

                // 售价、成本价
                if (itemSkuDto != null) {
                    params.put("salePrice", itemSkuDto.getSalePrice());
                    params.put("costPrice", itemSkuDto.getCostPrice());
                }
                // 查询开发者商品供应商信息
                if (appItemDto != null) {
                    SupplierSimpleInfoDto appItemDevSupplierInfo = getAppItemDevSupplierInfo(appItemDto.getId());
                    if (appItemDevSupplierInfo != null) {
                        params.put("devSupplierInfo", appItemDevSupplierInfo);
                    }
                }
                // 存储商品编码快照
                AppItemSkuDto appItemSkuDto = remoteAppItemSkuService.findSkuById(bo.getAppSkuId());
                params.put("itemCode",getItemCode(itemSkuDto,appItemSkuDto));
                // 实物
                if(StringUtils.equals(orderDto.getType(), ItemDto.TypeObject)){
                    if (each.getItem() != null && each.getItem().getItemExtraDto() != null ) {
                        ItemExtraDto itemExtra = each.getItem().getItemExtraDto();
                        if(itemExtra.getSupplierId()!=null){
                            params.put("channelName", SupplyTypeEnumUtil.getSupplierNameInfoById(itemExtra.getSupplierId().toString()));
                        }
                        params.put("channelId",  itemExtra.getSupplierId());
                    }else if (itemSkuDto != null){
                        Long itemId = itemSkuDto.getItemId();
                        ItemExtraDto result = remoteItemExtraService.findByItemId(itemId).getResult();
                        if (result!=null){
                            Long supplierId = result.getSupplierId();
                            params.put("channelName",supplierId!=null ? SupplyTypeEnumUtil.getSupplierNameInfoById(supplierId.toString()) : null);
                        }
                    }
                }
                // 虚拟商品
                if(StringUtils.equals(orderDto.getType(), ItemDto.TypeVirtual)){
                    if(each.getItem() != null){
                        params.put("channelName",  java.util.Optional.ofNullable(VirtualItemChannelEnum.getByCode(each.getItem().getMerchantCoding())).map(VirtualItemChannelEnum::getDesc).orElse(null));
                        params.put("channelId",  each.getItem().getMerchantCoding());
                    }
                }
                String bizParam = dto.getBizParam();
                if (StringUtils.isNotBlank(bizParam) && JSONValidator.from(bizParam).validate()){
                    Map<String, Object> oldParam = JSON.parseObject(bizParam, new TypeReference<Map<String, Object>>() {
                    });
                    oldParam.putAll(params);
                    dto.setBizParam(JSON.toJSONString(oldParam));
                }else {
                    dto.setBizParam(JSON.toJSONString(params));
                }
            }catch (Exception e){
                logger.warn("子订单设置额外信息异常 orderId=={}",orderDto.getId(),e);
            }
            orderItemDtos.add(dto);
        }
        //实物：主订单购买件数
        orderDto.setQuantity(quantity);
        orderDto.setFacePrice(facePrice);
        orderDto.setOrderItemDtoList(orderItemDtos);
        orderDto.setCredits(0L);
        orderDto.setFreight(goodsPriceVO.getFreight());

        orderDto.setConsumerPayPrice(0L);
        orderDto.setTotalPromoPrice(0L);
        //扣开发者的钱
        orderDto.setActualPrice(ComUtils.defaultObj(goodsPriceVO.getTotalDevActualPrice()).intValue());
        orderDto = setPayStatus(orderDto);
        return orderDto;
    }

    private SupplierSimpleInfoDto getAppItemDevSupplierInfo(Long appItemId) {
        //根据商品Id找到关联的供应商商品
        SupplierInfoDto supplierInfoDto = remoteSupplierItemService.querySupplierInfoByAppItemId(appItemId);
        if (supplierInfoDto == null) {
            return null;
        }
        SupplierSimpleInfoDto supplierSimpleInfoDto = new SupplierSimpleInfoDto();
        supplierSimpleInfoDto.setId(supplierInfoDto.getId());
        supplierSimpleInfoDto.setSupplierCustomerId(supplierInfoDto.getSupplierCustomerId());
        supplierSimpleInfoDto.setSupplierName(supplierInfoDto.getSupplierName());
        return supplierSimpleInfoDto;
    }

    /**
     * 获取商品编码
     */
    private String getItemCode(ItemSkuDto itemSkuDto, AppItemSkuDto appItemSkuDto) {
        try {
            // 优先取兑吧商品的编码
            if (itemSkuDto!=null){
                return itemSkuDto.getMerchantCoding();
            }
            if (appItemSkuDto!=null){
                return appItemSkuDto.getMerchantCoding();
            }
        }catch (Exception e){
            logger.warn("获取商品编码异常",e);
        }
        return "";
    }

    /**
     * 设置付款状态等
     *
     * @param orderDto
     * @return
     */
    private ShoppingTrolleryOrderDto setPayStatus(ShoppingTrolleryOrderDto orderDto) {
        if (orderDto.getActualPrice() != null && orderDto.getActualPrice() > 0) {
            orderDto.setPayStatus(OrdersDto.PayStatusWaitPay);
        } else {
            orderDto.setPayStatus(OrdersDto.PayStatusNone);
        }

        if (orderDto.getConsumerPayPrice() != null
                && orderDto.getConsumerPayPrice() > 0) {
            orderDto.setSubOrderType(OrdersDto.SubOrderTypeAmb);
            orderDto.setConsumerPayStatus(OrdersDto.ConsumerPayStatusWaitPay);
        } else {
            orderDto.setConsumerPayStatus(OrdersDto.ConsumerPayStatusNone);
        }
        return orderDto;
    }


    private String buildKey(Long appItemId, Long skuId) {
        skuId = null == skuId ? 0L : skuId;
        return appItemId.toString() + "_" + skuId.toString();
    }


    /**
     * 设置价格信息
     */
    private OrderItemDto setOrderItemPrice(OrderItemDto dto, GoodsPriceDetailBO bo) {
        dto.setCredits(bo.getTotalCredits());
        dto.setPrice(bo.getPrice());
        dto.setPromotion(bo.getHasPromotion());
        dto.setTotalPrice(bo.getTotalPrice());
        dto.setPromotionPrice(bo.getPromotionPrice());
        dto.setActualPrice(bo.getActualPrice());
        dto.setPromoActivityId(bo.getPromoActivityId());
        return dto;
    }

    /**
     * 创建订单,塞入基础字段
     */
    private ShoppingTrolleryOrderDto buildOrder(List<ItemKeyDto> itemKeyDtos, AppSimpleDto app, ConsumerDto consumer,ReceiveAddressDto addressDto) {
        ShoppingTrolleryOrderDto order = new ShoppingTrolleryOrderDto(true);
        order.setChargeMode(OrdersDto.ChargeModeMall);
        order.setConsumerId(consumer.getId());
        order.setAppId(consumer.getAppId());
        order.setDeveloperId(app.getDeveloperId());
        if (addressDto != null) {
            order.setBizParams(Objects.toString(addressDto.getAddrName(),"") +
                    ":" + Objects.toString(addressDto.getAddrPhone(),"") +
                    ":" + Objects.toString(addressDto.getAddrProvince(),"") +
                    ":" + Objects.toString(addressDto.getAddrCity(),"") +
                    ":" + Objects.toString(addressDto.getAddrArea(),"") +
                    ":" + Objects.toString(addressDto.getAddrStreet(),"") +
                    ":" + Objects.toString(addressDto.getAddrDetail(),""));
        }
        if (order.getActualPrice() != null && order.getActualPrice() > 0) {
            order.setPayStatus(OrdersDto.PayStatusWaitPay);
        } else {
            order.setPayStatus(OrdersDto.PayStatusNone);
        }
        order.setStatus(OrdersDto.StatusCreate);
        order.setType(ItemDto.TypeObject);
        order.setRelationType(OrdersDto.RelationTypeDefault);
        order.setDuibaPayStatus(OrdersDto.DuibaPayStatusNone);
        //必填 否则其他系统报错
        order.setBrief("");

        //取第一个数据插入
        ItemKeyDto itemKeyDto = itemKeyDtos.get(0);
        if (itemKeyDto.getAppItem() != null) {
            order.setBrief(ComUtils.defaultObj(itemKeyDto.getAppItem().getTitle()));
        }

        if (StringUtils.isBlank(order.getBrief())
                && itemKeyDto.getItem() != null) {
            order.setBrief(ComUtils.defaultObj(itemKeyDto.getItem().getName()));
        }

        //订单属于兑吧商品还是自有商品
        if (itemKeyDto.isDuibaAppItemMode()) {
            order.setTypeInt(GoodsTypeEnum.DUIBA.getGtype());
        } else {
            order.setTypeInt(GoodsTypeEnum.APP.getGtype());
        }
        // 商品来源
        order.setItemSource("duiba");
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("itemSource","duiba");
        order.setExtraInfo(extraInfo.toJSONString());
        return order;
    }



    private String getName(ItemKeyDto each) {
        AppItemDto appItemDto = each.getAppItem();
        ItemDto itemDto = each.getItem();

        if (appItemDto != null && StringUtils.isNotBlank(appItemDto.getTitle())) {
            return appItemDto.getTitle();
        }

        if (itemDto != null) {
            return ComUtils.defaultObj(itemDto.getName());
        }
        return "";
    }

}
