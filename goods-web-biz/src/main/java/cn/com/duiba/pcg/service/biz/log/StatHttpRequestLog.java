package cn.com.duiba.pcg.service.biz.log;

import cn.com.duiba.pcg.service.biz.dto.HttpRequestLogDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * HttpRequestLog  [orderId 123123] [type toDeveloper] [tag consumer] ....
 * <AUTHOR>
 */
public class StatHttpRequestLog {

	private static Logger log = LoggerFactory.getLogger(StatHttpRequestLog.class);

	public static void log(HttpRequestLogDto httpRequestLogDto) {
		StringBuilder formatLog = new StringBuilder();
		formatLog.append("[orderId " + httpRequestLogDto.getOrderId() + "] ");
		formatLog.append("[type " + httpRequestLogDto.getType() + "] ");
		formatLog.append("[tag " + httpRequestLogDto.getTag() + "] ");
		formatLog.append("[success " + httpRequestLogDto.getSuccess() + "] ");
		formatLog.append("[url " + httpRequestLogDto.getUrl() + "] ");
		formatLog.append("[resp " + httpRequestLogDto.getResp() + "] ");
		formatLog.append("[developerBizId " + httpRequestLogDto.getDeveloperBizId() + "] ");
		formatLog.append("[supplierBizId " + httpRequestLogDto.getSupplierBizId() + "] ");
		formatLog.append("[lotteryOrderId " + httpRequestLogDto.getTurntableOrderId() + "] ");
		log.info(formatLog.toString());
	}

}
