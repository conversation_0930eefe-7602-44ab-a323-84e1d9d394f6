package cn.com.duiba.pcg.service.biz.dto.jincheng.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 金城基本请求
 */
@Data
public class JinchengBaseRequest implements Serializable {
    /**
     * 请求流水号，无业务意义，保证全局唯一
     */
    private String sequenceNo;

    /**
     * 请求 unix 时间戳
     */
    private String timestamp;

    /**
     * 签名
     */
    private String signature;

    /**
     * AES 随机 key 使用 RSA 加密后的密文
     */
    private String encryptedKey;

    /**
     * 加密后的业务数据
     */
    private String encryptedData;

    /**
     * 方法名，对应接口明细中的接口标识
     */
    private String function;
}
