package cn.com.duiba.pcg.service.biz.config;

import cn.com.duiba.developer.center.api.domain.dto.AppSimpleDto;
import cn.com.duiba.pcg.service.biz.vo.cmb.CmbItemConfigVO;
import cn.com.duiba.pcg.service.biz.vo.oppo.OppoVO;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/1/16
 * @description: 商品通用配置类
 */
@Component
@RefreshScope
public class CommonConfig implements InitializingBean {

    /**
     * 新商品黑名单列表
     */
    @Value("#{'${pcgweb.oldGoodsDetail.ids}'.split(',')}")
    private Set<Long> oldGoodsDetailIds;

    /**
     * 海底捞强制不显示购物车商品名单
     */
    @Value("#{'${pcgweb.haidilao.nocart.ids}'.split(',')}")
    private Set<Long> haidilaoNocartIds;

    /**
     * 商品前置查询接口需要返回近期销量的appid列表
     */
    @Value("#{'${pcgweb.frontitem.exchangeNum.ids}'.split(',')}")
    private Set<Long> frontItemExchangeNumIds;

    /**
     * 商品前置查询接口需要返回优惠券卡密有效期的appid列表
     */
    @Value("#{'${pcgweb.frontitem.couponvalid.ids}'.split(',')}")
    private Set<Long> frontItemCouponValidIds;

    @Value("${gaw.szbank.pay.check.switch}")
    private  Boolean  szbankPayCheckSwitch;
    /**
     * 对第三方开放支付宝充值接口的appid列表
     */
    @Value("#{'${pcgweb.thirdparty.alipay.api.ids:1}'.split(',')}")
    private Set<Long> thirdpartyAlipayApiIds;
    /**
     * 对第三方开放话费、流量充值接口的appid列表
     */
    @Value("#{'${pcgweb.thirdparty.charge.api.ids:1}'.split(',')}")
    private Set<Long> thirdpartyChargeApiIds;

    /**
     * 兑吧老商品地域限购走新版校验流程的appid列表
     */
    @Value("#{'${pcgweb.exchange.addr.limit.v1.ids:1}'.split(',')}")
    private Set<Long> addrLimitAsV1Ids;

    /**
     * 兑吧前置接口过滤0库存商品应用白名单
     */
    @Value("#{'${pcgweb.frontitem.limit.zerostock:1}'.split(',')}")
    private Set<Long> frontLimitZeroStock;

    /**
     * 兑吧前置接口开启输出vipLevel等级
     */
    @Value("#{'${pcgweb.vip.level.appids:-1}'.split(',')}")
    private Set<Long> vipLevelAppIds;


    /**
     *  新疆农行定制
     */
    @Value("#{'${pcgweb.consumer.lable.appids:70711}'.split(',')}")
    private Set<Long> consumerLableXinJiangAppIds;

    /**
     * 合肥招行-定制兑换时间限制
     *
     */
    @Value("${pcgweb.hfcmb.item.ids:[{\"appId\":62445,\"itemIds\":[128270292720489,128270491962024],\"minSecs\":[\"10:00:00\",\"23:59:00\"]}]}")
    private String hfcmbItemIds;

    /**
     * oppo-加密解密key
     *
     */
    @Value("${pcgweb.oppo.appaes.key:[{\"aesKey\":\"JuGi3FCECD1dA2BPL1lCWC==\",\"appKey\":\"oTtXFe2fVHCqt4yYreD6DfdckV8\"}]}")
    private String oppoAppAesKey;

    /**
     * 兑吧前置接增加主图白名单
     */
    @Value("#{'${pcgweb.frontitem.multiimage:1,68330}'.split(',')}")
    private Set<Long> frontMultiImage;

    /**
     * 采用经纬度定位白名单
     */
    @Value("#{'${pcgweb.pointAddrLimtAppIds:70370}'.split(',')}")
    private Set<Long> pointAddrLimtAppIds;

    /**
     * 采购-预下单-使用秘钥
     */
    @Value("${gaw.supply.order.pre.blowfishKey:CNxgrtFG2nYQUfuFawewnfnewfaasdfwwwwwwadsfawr2123a123fa32}")
    private String supplyOrderPreBlowfishKey;

    /**
     * 采购-预下单-使用中间页链接
     */
    @Value("${gaw.supply.order.pre.url:https://activity.m.duibatest.com.cn/customShare/share?id=Did1MTI5MTE&orderNum=}")
    private String supplyOrderPreUrl;

    /**
     * 采购-预下单-获取openidkey
     */
    @Value("${wechat.server.env.encrypt.key:encryptduiba}")
    private String cookieEncryptKey;

    @Value("${gaw.supply.order.redpack.url:https://activity.m.duibatest.com.cn/wechat/access?apk=3bRVpGghtsQXXeZpVH4VGi92Nr7V&dbredirect=https%3A%2F%2Factivity.m.duibatest.com.cn%2FcustomShare%2Fshare%3Fid%3DDid1MTI5MzU%26prize%3Dtrue%26orderNum%3D}")
    private String wxRedpackUrl;

    private List<CmbItemConfigVO> cmbItemConfigVOList;

    private List<OppoVO> oppoKeyVOList;

    public String getWxRedpackUrl() {
        return wxRedpackUrl;
    }

    public void setWxRedpackUrl(String wxRedpackUrl) {
        this.wxRedpackUrl = wxRedpackUrl;
    }

    /**
     * 是否新商品模型白名单app
     *
     * @param app
     * @return
     */
    public boolean isNewGoodsDetailApp(AppSimpleDto app) {
        return !oldGoodsDetailIds.contains(app.getId());
    }

    public String getCookieEncryptKey() {
        return cookieEncryptKey;
    }


    public Set<Long> getConsumerLableXinJiangAppIds() {
        return consumerLableXinJiangAppIds;
    }


    public boolean isReturnExchangeNum(Long appId){
        return frontItemExchangeNumIds.contains(appId);
    }

    public boolean isFrontItemCouponValidIds(Long appId){
        return frontItemCouponValidIds.contains(appId);
    }

    public Boolean getSzbankPayCheckSwitch() {
        return szbankPayCheckSwitch;
    }


    public boolean canAccessThirdpartyAlipayApi(Long appId) {
        return thirdpartyAlipayApiIds.contains(appId);
    }

    public boolean canAccessThirdpartyChargeApi(Long appId) {
        return thirdpartyChargeApiIds.contains(appId);
    }

    public boolean addrLimitAsV1(Long appId) {
        return addrLimitAsV1Ids.contains(appId);
    }

    public boolean frontStockZeroLimit(Long appId) {
        return frontLimitZeroStock.contains(appId);
    }

    public boolean canAccessVipLevel(Long appId) {
        return vipLevelAppIds.contains(appId);
    }

    public boolean showMultiImage(Long appId) {
        return frontMultiImage.contains(appId);
    }

    public boolean inPointAddrLimtAppIds(Long appId) {
        return pointAddrLimtAppIds.contains(appId);
    }

    public String getSupplyOrderPreBlowfishKey() {
        return supplyOrderPreBlowfishKey;
    }


    public String getSupplyOrderPreUrl() {
        return supplyOrderPreUrl;
    }

    public CmbItemConfigVO isCmbLimitItem(Long appId, Long itemId) {
       if(CollectionUtils.isEmpty(cmbItemConfigVOList)){
           return null;
       }

       for(CmbItemConfigVO cmbItemConfigVO : cmbItemConfigVOList){
           if(CollectionUtils.isEmpty(cmbItemConfigVO.getItemIds())){
               continue;
           }

           if(Objects.equals(cmbItemConfigVO.getAppId(), appId) && cmbItemConfigVO.getItemIds().contains(itemId)){
               return cmbItemConfigVO;
           }
       }

       return null;
    }

    public OppoVO getOppoVO(String appKey) {
        if(CollectionUtils.isEmpty(oppoKeyVOList) || StringUtils.isBlank(appKey)){
            return null;
        }

        List<OppoVO> filterList = oppoKeyVOList.stream().filter(vo -> Objects.equals(vo.getAppKey(), appKey)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)){
            return null;
        }
        return filterList.get(0);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (StringUtils.isBlank(hfcmbItemIds)) {
            cmbItemConfigVOList = Lists.newArrayList();
        }

        cmbItemConfigVOList = JSONObject.parseArray(hfcmbItemIds, CmbItemConfigVO.class);
        
        if(StringUtils.isBlank(oppoAppAesKey)){
            oppoKeyVOList = Lists.newArrayList();
        }
        oppoKeyVOList = JSONObject.parseArray(oppoAppAesKey, OppoVO.class);
    }
}
