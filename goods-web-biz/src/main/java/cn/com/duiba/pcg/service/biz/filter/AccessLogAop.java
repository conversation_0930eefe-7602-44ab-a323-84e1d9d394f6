package cn.com.duiba.pcg.service.biz.filter;

import cn.com.duiba.biz.tool.duiba.client.RequestLocal;
import cn.com.duiba.consumer.center.api.dto.ConsumerDto;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 访问日志
 *
 * <AUTHOR>
 * @date 2023/5/9 11:56 AM
 */
@Aspect
@Slf4j
@Component
public class AccessLogAop {

    @Pointcut("@annotation(org.springframework.web.bind.annotation.GetMapping) || @annotation(org.springframework.web.bind.annotation.PostMapping) || @annotation(org.springframework.web.bind.annotation.RequestMapping)  ")
    public void logger() {
        //定义日志的切面
    }

    @Around("logger()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception ex) {
            result = ex.getMessage();
            throw ex;
        } finally {
            try {
                handleAccessAndApplicationLog(getHttpServletRequest(), joinPoint, result);
            } catch (Exception e) {
                log.warn("", e);
            }
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return ((ServletRequestAttributes) Objects.requireNonNull(requestAttributes)).getRequest();
    }

    private void handleAccessAndApplicationLog(HttpServletRequest request, ProceedingJoinPoint joinPoint, Object result) {
        Map<String, Object> f = LogAopUtil.getFieldsName(joinPoint);
        // set userId as consumer_id
        // set uid as consumer_id
        ConsumerDto consumer = RequestLocal.getConsumerDO();
        Long consumerId = Optional.ofNullable(consumer).map(ConsumerDto::getId).orElse(null);
        Long appId = Optional.ofNullable(consumer).map(ConsumerDto::getAppId).orElse(null);

        String responseBody = LogAopUtil.getResponseBody(result, 5000);
        logToApplication(request, f, responseBody, consumerId, appId);
    }

    private void logToApplication(HttpServletRequest request, Map<String, Object> reqBody, Object responseBody, Long consumerId, Long appId) {
        JSONObject jsonObject = new JSONObject();
        putIfNotNull(jsonObject, "req_body", MapUtils.isNotEmpty(reqBody) ? reqBody : request.getQueryString());
        putIfNotNull(jsonObject, "consumer_id", consumerId);
        putIfNotNull(jsonObject, "app_id", appId);
        putIfNotNull(jsonObject, "resp_body", responseBody);
        jsonObject.put("url_path", LogAopUtil.getPath(request));
        log.info("accessLog:{}", jsonObject.toJSONString());
    }

    private void putIfNotNull(JSONObject jsonObject, String key, Object value) {
        if (value != null) {
            jsonObject.put(key, value);
        }
    }
}
