package cn.com.duiba.pcg.service.biz.vo.groupbuy;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pengyi
 * @description:
 * @date: 2021/11/23 下午5:43
 */
public class GroupBuyActInfoVO implements Serializable {
    private static final long serialVersionUID = -594468009824928901L;

    /**
     * 拼团活动id
     */
    private Long actId;

    /**
     * 当前用户拼团id
     */
    private Long groupId;

    /**
     * 预告时长 单位分
     */
    private Date foreshowDurtion;

    /**
     * 当前用户拼团状态
     * @see cn.com.duiba.mall.center.api.domain.enums.groupbuy.GroupBuyDetailStatusEnum
     */
    private String groupStatus;

    /**
     * 活动开始时间
     */
    private Date actStartTime;

    /**
     * 活动结束时间
     */
    private Date actEndTime;

    /**
     * 当前时间
     */
    private Date nowTime;

    /**
     * 几人成团
     */
    private Integer groupPeopleNum;

    /**
     * 拼团价格
     */
    private Long groupBuyPrice;

    /**
     * 商品当前拼团活动销量
     */
    private Integer saleAppItemNum;

    /**
     * 是否限购
     */
    private Boolean groupBuyExchange = true;

    /**
     * 限购数量
     */
    private Integer limitNum;

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Date getForeshowDurtion() {
        return foreshowDurtion;
    }

    public void setForeshowDurtion(Date foreshowDurtion) {
        this.foreshowDurtion = foreshowDurtion;
    }

    public String getGroupStatus() {
        return groupStatus;
    }

    public void setGroupStatus(String groupStatus) {
        this.groupStatus = groupStatus;
    }

    public Date getActStartTime() {
        return actStartTime;
    }

    public void setActStartTime(Date actStartTime) {
        this.actStartTime = actStartTime;
    }

    public Date getActEndTime() {
        return actEndTime;
    }

    public void setActEndTime(Date actEndTime) {
        this.actEndTime = actEndTime;
    }

    public Date getNowTime() {
        return nowTime;
    }

    public void setNowTime(Date nowTime) {
        this.nowTime = nowTime;
    }

    public Integer getGroupPeopleNum() {
        return groupPeopleNum;
    }

    public void setGroupPeopleNum(Integer groupPeopleNum) {
        this.groupPeopleNum = groupPeopleNum;
    }

    public Long getGroupBuyPrice() {
        return groupBuyPrice;
    }

    public void setGroupBuyPrice(Long groupBuyPrice) {
        this.groupBuyPrice = groupBuyPrice;
    }

    public Integer getSaleAppItemNum() {
        return saleAppItemNum;
    }

    public void setSaleAppItemNum(Integer saleAppItemNum) {
        this.saleAppItemNum = saleAppItemNum;
    }

    public Boolean getGroupBuyExchange() {
        return groupBuyExchange;
    }

    public void setGroupBuyExchange(Boolean groupBuyExchange) {
        this.groupBuyExchange = groupBuyExchange;
    }

    public Integer getLimitNum() {
        return limitNum;
    }

    public void setLimitNum(Integer limitNum) {
        this.limitNum = limitNum;
    }
}
