package cn.com.duiba.pcg.service.biz.config.jhj;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/5/17 15:20
 */
@Configuration
@ConfigurationProperties(prefix = "jhj")
@Slf4j
public class JhjConfig {

    /**
     * 白名单
     */
    private Set<String> appItemIds = Sets.newHashSet("298876406408574","298877235819114");

    private Set<Long> appIds = Sets.newHashSet(1L);

    private Set<String> groupNameList = Sets.newHashSet("test");

    /**
     * 金徽酒默认地址
     */
    private String jhjDefaultAddress = "{\"address\":\"知道了知道了知道了\",\"consignee\":\"测试1\",\"defaultFlag\":true,\"districtCode\":\"130203\",\"phoneNumber\":\"18885555851\",\"cityName\":\"唐山市\",\"districtName\":\"路北区\",\"cityCode\":\"130203\",\"provinceCode\":\"130000\",\"provinceName\":\"河北省\"}";
    /**
     * 兑吧默认地址id
     */
    private Long defaultAddressId = 2583L;

    /**
     *金徽酒商城id
     */
    private String pointsMallId;

    public String getJhjDefaultAddress() {
        return jhjDefaultAddress;
    }

    public void setJhjDefaultAddress(String jhjDefaultAddress) {
        this.jhjDefaultAddress = jhjDefaultAddress;
    }

    public String getPointsMallId() {
        return pointsMallId;
    }

    public void setPointsMallId(String pointsMallId) {
        this.pointsMallId = pointsMallId;
    }

    public Long getDefaultAddressId() {
        return defaultAddressId;
    }

    public void setDefaultAddressId(Long defaultAddressId) {
        this.defaultAddressId = defaultAddressId;
    }

    public Set<String> getGroupNameList() {
        return groupNameList;
    }

    public void setGroupNameList(Set<String> groupNameList) {
        this.groupNameList = groupNameList;
    }

    public Set<Long> getAppIds() {
        return appIds;
    }

    public void setAppIds(Set<Long> appIds) {
        this.appIds = appIds;
    }

    public Set<String> getAppItemIds() {
        return appItemIds;
    }

    public void setAppItemIds(Set<String> appItemIds) {
        this.appItemIds = appItemIds;
    }
}
