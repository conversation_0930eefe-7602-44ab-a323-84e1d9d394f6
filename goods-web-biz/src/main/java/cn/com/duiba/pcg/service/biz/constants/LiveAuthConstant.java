package cn.com.duiba.pcg.service.biz.constants;

/**
 * @Description
 * @Date 2022/5/23 15:02
 * <AUTHOR>
 */
public class LiveAuthConstant {

    // 免登dcustom认证字段
    public static final String GOOD_AUTH = "goodAuth";
    // AES密钥
    public static final String PRIVATE_KEY = "MTIzNDU2Nzg5MGFiY2RlZg==";

    public static final String LOG_PREFIX = "直播商品兑换身份认证";

    public static final Integer PHONE_LENGTH = 11;
    public static final Integer NAME_MIN_LENGTH = 2;
    public static final Integer NAME_MAX_LENGTH = 15;


    public static final String NETWORK_ERROR = "网络异常，请稍后重试~";
    public static final String ERROR_10000 = "您的身份证号码或手机号不对，为避免因个人信息错误导致报税失败，请重新检查";
    public static final String ERROR_10001 = "请输入正确的姓名";
    public static final String ERROR_10003 = "用户信息不存在";
    public static final String ERROR_10004 = "已完成身份认证";
    public static final String ERROR_1005 = "非法请求";
}
