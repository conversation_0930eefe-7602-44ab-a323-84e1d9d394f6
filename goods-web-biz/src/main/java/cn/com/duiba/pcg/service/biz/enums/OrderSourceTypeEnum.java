package cn.com.duiba.pcg.service.biz.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>订单来源枚举</p>
 * <p>created by <PERSON><PERSON> on 2021/5/31<p>
 **/
public enum OrderSourceTypeEnum {
    ORDER_GIVING_ACT(1,"订单满赠活动兑换"),
    ORDER_BONUS(2, "订单赠品领取"),
    CREATE_GROUP(3, "发起拼团"),
    JOIN_GROUP(4, "参团"),
    ;

    private static final Map<Integer, OrderSourceTypeEnum> MAP =
            Arrays.stream(values()).collect(Collectors.toMap(OrderSourceTypeEnum::getCode, Function.identity()));

    private Integer code;
    private String desc;


    OrderSourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderSourceTypeEnum ofCode(Integer code) {
        return MAP.get(code);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
