package cn.com.duiba.pcg.service.biz;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "icbc.elife")
public class IcbcElifeConfig {

    /**
     * app端，支付结果回调通知地址
     */
    private String notifyUrl4App = "http://activity.m.duiba.com.cn/taw/icbcElifeApp/charge/notify";

    public String getNotifyUrl4App() {
        return notifyUrl4App;
    }

    public void setNotifyUrl4App(String notifyUrl4App) {
        this.notifyUrl4App = notifyUrl4App;
    }
}
