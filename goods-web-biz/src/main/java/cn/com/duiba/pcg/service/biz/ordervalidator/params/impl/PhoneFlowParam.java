package cn.com.duiba.pcg.service.biz.ordervalidator.params.impl;

import cn.com.duiba.goods.center.api.remoteservice.dto.SupplierProductDto;
import org.apache.commons.lang.builder.ToStringBuilder;

/**
 * Created by dugq on 2018/11/22 0022.
 */
public class PhoneFlowParam extends DirectParam {
    private String phone;
    private String phoneCatName;
    private SupplierProductDto supplierProductDto;


    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhoneCatName() {
        return phoneCatName;
    }

    public void setPhoneCatName(String phoneCatName) {
        this.phoneCatName = phoneCatName;
    }

    public SupplierProductDto getSupplierProductDto() {
        return supplierProductDto;
    }

    public void setSupplierProductDto(SupplierProductDto supplierProductDto) {
        this.supplierProductDto = supplierProductDto;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
