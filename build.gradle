buildscript {
    ext {
        springBootVersion = '1.5.22.RELEASE'
    }
    repositories {
        mavenLocal()
        mavenCentral()
        maven { url "https://nexus.dui88.com/nexus/content/groups/public/" }
        maven { url "https://mirrors.ibiblio.org/maven2/" }
        maven { url "https://repo.maven.apache.org/maven2" }
        maven { url "https://plugins.gradle.org/m2/" } //sonarqube
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
    }
}

plugins{
    id 'org.sonarqube' version '2.5'
    id "io.spring.dependency-management" version "0.6.1.RELEASE"
}

ext['hazelcast.version']='3.11'

//sonarqube
apply plugin: 'org.sonarqube'
sonarqube {
    properties {
        property "sonar.projectName", "goods-access-web"
        property "sonar.projectKey", "cn.com.duiba:goods-access-web"
        property "sonar.host.url","http://sonar.dui88.com"
    }
}


allprojects {
    apply plugin: 'maven'
    apply plugin: 'java'
    apply plugin: 'idea'
    apply plugin: 'eclipse'
    apply plugin: "jacoco"
    test {
        ignoreFailures = true
    }

    group = 'cn.com.duiba.goods-access-web'
    version = '1.0.0-SNAPSHOT'

}


subprojects {

    apply plugin: "io.spring.dependency-management"
    sourceCompatibility = 1.8
    targetCompatibility = 1.8

    configurations.all {
        exclude group: "log4j", module:"log4j"
        exclude group: "org.slf4j", module: "slf4j-log4j12"

        resolutionStrategy {    // 解决SNAPSHOT包不拉最新版的问题
            resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
        }
    }


    dependencyManagement {
        dependencies {

            imports {
                mavenBom 'cn.com.duiba.boot:spring-boot-ext-dependencies:1.3.54'
                mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Edgware.SR6'
            }


            dependency('org.apache.zookeeper:zookeeper:3.4.8')
            dependency 'com.alibaba:fastjson:1.2.70'
            dependency 'javax.activation:activation:1.1.1'
            dependency 'javax.servlet:javax.servlet-api:3.1.0'
            dependency 'commons-validator:commons-validator:1.3.1'

            dependency 'org.apache.velocity:velocity:1.7'
            dependency 'org.apache.velocity:velocity-tools:2.0'
            dependency 'com.google.guava:guava:18.0'
            dependency 'com.github.rholder:guava-retrying:2.0.0'

            //业务依赖
            dependency("cn.com.duiba:biz-tool:1.7.5")
            dependency('cn.com.duiba:dcommons-base:0.3') {
                exclude 'com.alibaba:dubbo'
            }
            dependency 'cn.com.duiba.activity-center:activity-center-api:3.6.17'
            dependency 'cn.com.duiba.goods-center:goods-center-api:3.2.24'
            dependency 'cn.com.duiba.developer-center:developer-center-api:3.9.45'
            dependency 'cn.com.duiba.activity-comm-center:activity-common-api:1.4.13-hotfix-2'
            dependency 'cn.com.duiba.consumer-center:consumer-center-api:1.2.62'
            dependency 'cn.com.duiba.anticheat-center:anticheat-center-api:2.10.3'
            dependency 'cn.com.duiba.trade-center:trade-center-api:3.0.8'
            dependency 'cn.com.duiba.credits-center:credits-center-api:0.0.21'

            dependency 'cn.com.duiba:kvtable-service-api:1.6.9'
//            dependency 'cn.com.duiba:java-sdk:0.0.16'
            dependency("io.springfox:springfox-swagger-ui:2.5.0")
            dependency("io.springfox:springfox-swagger2:2.5.0")
            dependency('com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:20160827.1')
            dependency("cn.com.duiba.idmaker-service:idmaker-service-api:1.2.27")
            dependency("cn.com.duiba:dcommons-flowwork-client:1.3.1.coupon-SNAPSHOT")
            dependency('cn.com.duiba.activity-custom-center:activity-custom-center-api:1.0.35')
            dependency('cn.com.duiba.wechat-server:wechat-server-api:0.0.34')
            dependency("cn.com.duiba.mall-center:mall-center-api:0.1.32")
            dependency('cn.com.duiba:paycenter-client:3.5.31')
            //技术中台 地址库依赖
            dependency('cn.com.duiba.geo-server:geo-api:2.0.23')
            dependency("cn.com.duiba:duiba-api:1.0.5")
            dependency('cn.com.duiba.odps-center:odps-center-api:1.1.35')
            dependency('cn.com.duiba.supplier-center:supplier-center-api:2.1.47')
            dependency 'cn.com.duiba:message-service-api:0.1.18'
            //库存中心
            dependency('cn.com.duiba.stock-service:stock-service-api:2.1.13')
            dependency('org.bouncycastle:bcprov-jdk15on:1.60')
            dependency('net.coobird:thumbnailator:0.4.8')
            dependency('cn.com.duiba.thirdparty-service:thirdparty-service-api:1.2.21')
            //个有觉得不太合理，商品中心依赖活动plugin
            dependency('cn.com.duiba.plugin-center:plugin-center-api:1.1.15')
            //直播
            dependency 'cn.com.duiba:duiba-live-activity-center-api:0.0.93'
            dependency 'cn.com.duiba:duiba-live-supplier-center-api:0.0.4'
            dependency('cn.com.duiba.tuia.micro-us-center:union-star-center-api:0.1.72')
            dependency("cn.com.duiba:wolf:2.3.1")

            dependency 'io.jsonwebtoken:jjwt-api:0.11.1'
            dependency 'io.jsonwebtoken:jjwt-impl:0.11.1'
            dependency 'io.jsonwebtoken:jjwt-jackson:0.11.1'

            dependency 'cn.com.duiba.credits:duiba-java-sdk:2.1.0'

            dependency 'cn.com.duiba:duiba-base-service-api:0.0.1'
            dependency 'cn.com.duiba.thirdparty-service-vnew:thirdparty-service-vnew-api:1.3.21'

            dependency 'com.alibaba:easyexcel:2.2.4'
        }
        resolutionStrategy {
            cacheChangingModulesFor 0, 'seconds'
        }
    }



    repositories {
        mavenLocal()

        maven { url "https://nexus.dui88.com/nexus/content/groups/public/" }
        mavenCentral()

    }

    task listJars(description: 'Display all compile jars.') doLast {
        configurations.compile.each { File file -> println file.name }
    }

}
